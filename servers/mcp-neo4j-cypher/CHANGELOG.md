## Next 

### Fixed

* IT no longer has risk of affecting locally deployed Neo4j instances

### Changed

* IT now uses Testcontainers library instead of Docker scripts 

### Added

## v0.2.1

### Fixed

* Fixed MCP version notation for declaration in config files - README

## v0.2.0

### Changed

* Refactor mcp-neo4j-cypher to use the FastMCP class
* Implement Neo4j async driver
* Tool responses now return JSON serialized results
* Update README with new config options 
* Update integration tests

### Added

* Add support for environment variables
* Add Github workflow to test and format mcp-neo4j-cypher


## v0.1.1

...