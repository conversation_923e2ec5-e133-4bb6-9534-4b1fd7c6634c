[project]
name = "mcp-neo4j-aura-manager"
version = "0.2.2"
description = "MCP Neo4j Aura Database Instance Manager"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp>=1.6.0",
    "requests>=2.31.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pyright>=1.1.389",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.3",
]

[project.scripts]
mcp-neo4j-aura-manager = "mcp_neo4j_aura_manager:main"

[tool.pytest.ini_options]
pythonpath = [
  "src"
]
