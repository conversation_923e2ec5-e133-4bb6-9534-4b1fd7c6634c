[project]
name = "mcp-neo4j-memory"
version = "0.1.4"
description = "MCP Neo4j Knowledge Graph Memory Server"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp>=0.10.0",
    "neo4j>=5.26.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pyright>=1.1.389",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.3",
]

[project.scripts]
mcp-neo4j-memory = "mcp_neo4j_memory:main"

[tool.pytest.ini_options]
pythonpath = [
  "src"
]
