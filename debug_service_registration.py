#!/usr/bin/env python3

"""
Debug script to investigate service registration issues in Plugginger framework.
"""

import asyncio
from typing import Any

from plugginger import Plugginger<PERSON><PERSON><PERSON><PERSON>er, PluginBase, plugin, service
from plugginger.api.service import get_service_metadata, is_service_method
from plugginger.core.constants import SERVICE_METADATA_KEY


@plugin(name="debug_plugin", version="1.0.0")
class DebugPlugin(PluginBase):
    """Debug plugin for testing service registration."""

    def __init__(self, app: Any, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)

    @service()
    async def test_service(self) -> str:
        """Test service method."""
        return "test_result"

    @service(name="custom_service")
    async def another_method(self) -> str:
        """Another test service with custom name."""
        return "custom_result"


async def debug_service_registration() -> None:
    """Debug the service registration process."""
    print("=== Plugginger Service Registration Debug ===\n")

    # Step 1: Check @service decorator functionality
    print("1. Testing @service decorator:")
    plugin_class = DebugPlugin

    # Check if methods have service metadata
    test_service_method = plugin_class.test_service
    another_method = plugin_class.another_method

    print(f"   test_service has SERVICE_METADATA_KEY: {hasattr(test_service_method, SERVICE_METADATA_KEY)}")
    print(f"   another_method has SERVICE_METADATA_KEY: {hasattr(another_method, SERVICE_METADATA_KEY)}")

    if hasattr(test_service_method, SERVICE_METADATA_KEY):
        metadata = get_service_metadata(test_service_method)
        print(f"   test_service metadata: {metadata}")

    if hasattr(another_method, SERVICE_METADATA_KEY):
        metadata = get_service_metadata(another_method)
        print(f"   another_method metadata: {metadata}")

    print(f"   is_service_method(test_service): {is_service_method(test_service_method)}")
    print(f"   is_service_method(another_method): {is_service_method(another_method)}")

    # Step 2: Build app and check plugin instantiation
    print("\n2. Building app and checking plugin instantiation:")
    builder = PluggingerAppBuilder(app_name="debug_app")
    builder.include(DebugPlugin)

    app = builder.build()
    print(f"   App built successfully: {app._app_name}")

    # Step 3: Start plugins and check service registration
    print("\n3. Starting plugins and checking service registration:")
    await app.start_all_plugins()

    # Get plugin instance
    plugin_instance = app.get_plugin_instance("debug_app:debug_plugin")
    print(f"   Plugin instance retrieved: {plugin_instance is not None}")

    if plugin_instance:
        print(f"   Plugin instance type: {type(plugin_instance)}")
        print(f"   Plugin instance ID: {getattr(plugin_instance, '_plugginger_instance_id', 'NOT_SET')}")

        # Check bound methods
        bound_test_service = plugin_instance.test_service
        bound_another_method = plugin_instance.another_method

        print(f"   Bound test_service has metadata: {hasattr(bound_test_service, SERVICE_METADATA_KEY)}")
        print(f"   Bound another_method has metadata: {hasattr(bound_another_method, SERVICE_METADATA_KEY)}")

    # Step 4: Check service dispatcher state
    print("\n4. Checking service dispatcher state:")
    services = app.list_services()
    print(f"   Registered services: {services}")
    print(f"   Number of services: {len(services)}")

    # Check if services exist
    expected_services = ["debug_plugin.test_service", "debug_plugin.custom_service"]
    for service_name in expected_services:
        has_service = app.has_service(service_name)
        print(f"   Has service '{service_name}': {has_service}")

    # Step 5: Try to call services
    print("\n5. Attempting to call services:")
    for service_name in expected_services:
        try:
            result = await app.call_service(service_name)
            print(f"   Service '{service_name}' returned: {result}")
        except Exception as e:
            print(f"   Service '{service_name}' failed: {type(e).__name__}: {e}")

    # Step 6: Debug runtime facade internals
    print("\n6. Debugging runtime facade internals:")
    if app._runtime_facade:
        service_dispatcher = app._runtime_facade._service_dispatcher
        print(f"   Service dispatcher type: {type(service_dispatcher)}")
        print(f"   Service dispatcher services: {getattr(service_dispatcher, '_services', {})}")

    print("\n=== Debug Complete ===")


if __name__ == "__main__":
    asyncio.run(debug_service_registration())
