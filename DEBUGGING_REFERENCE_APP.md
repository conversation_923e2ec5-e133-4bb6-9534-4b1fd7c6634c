# Issue: AI-Chat Reference App Debugging & External Testing

## 🎯 **ÜBERGABE AN NÄCHSTE AI-INSTANZ**

### **Kontext & Aktueller Stand**
- **Sprint 1 S1.4** "Reference-App Proof-of-Concept" ist **95% abgeschlossen**
- AI-Chat Reference App wurde vollständig implementiert auf Branch `s1/reference-app-design`
- **Alle 7 Integration Tests bestehen** ✅
- **Plugin-Architektur funktioniert** (memory_store → chat_ai → web_api) ✅
- **`plugginger inspect --json` funktioniert** ✅
- **Python 3.13 Kompatibilität hinzugefügt** ✅
- **Manifest-Validierungsfehler behoben** (Manifeste deaktiviert) ✅

### **AKTUELLES PROBLEM**
User hat erfolgreich die App gestartet, aber **FastAPI Web-Server läuft nicht korrekt**:
- App startet ohne Fehler ✅
- Plugins laden erfolgreich ✅
- Browser zeigt "detail: not found" auf http://localhost:8000 ❌
- **Vermutung**: FastAPI/Uvicorn Dependencies fehlen oder Server startet nicht

### **BEREITS DURCHGEFÜHRTE SCHRITTE**
1. ✅ Git Branch Problem behoben (`git clone -b s1/reference-app-design`)
2. ✅ Python 3.13 Kompatibilität hinzugefügt (pyproject.toml angepasst)
3. ✅ Manifest-Validierungsfehler behoben (`ENABLE_MANIFESTS=false`)
4. ✅ App startet ohne Fehler
5. ❌ **NÄCHSTER SCHRITT**: FastAPI Web-Server Debugging

### **DATEIEN & STRUKTUR**
```
examples/ai-chat-reference/
├── app.py                  # Main app (create_app, start_app, main)
├── plugins/
│   ├── memory_store/       # In-Memory Storage Plugin
│   ├── chat_ai/           # OpenAI/Mock AI Plugin  
│   └── web_api/           # FastAPI Web Server Plugin
├── tests/test_integration.py  # 7/7 Tests bestehen
├── README.md              # <10 Min Setup Guide
└── pyproject.toml         # Dependencies
```

### **NÄCHSTE AUFGABEN**
1. **FastAPI Server Debugging** (PRIORITÄT 1)
2. **External Testing** mit Human + AI Agent
3. **Sprint 1 S1.4 Abschluss**

---

## 🐛 **DEBUGGING AUFGABEN**

### **Problem 1: FastAPI Server startet nicht**

**Symptome:**
- App startet erfolgreich
- Plugins laden ohne Fehler
- Browser zeigt "detail: not found" auf Port 8000
- Keine Uvicorn-Logs sichtbar

**Mögliche Ursachen:**
1. **FastAPI/Uvicorn nicht installiert** (wahrscheinlichste Ursache)
2. **Server startet nicht automatisch** (web_api Plugin Problem)
3. **Port-Konflikt** (anderer Service auf 8000)
4. **Import-Fehler** in web_api Plugin

**Debugging-Schritte:**

#### **Schritt 1: Dependencies prüfen**
```bash
# Im venv prüfen
pip list | grep -E "(fastapi|uvicorn)"
# Sollte zeigen: fastapi>=0.100.0, uvicorn>=0.20.0

# Falls fehlt:
pip install fastapi uvicorn pydantic
```

#### **Schritt 2: Server-Status prüfen**
```bash
# Port 8000 prüfen
netstat -tlnp | grep :8000
lsof -i :8000

# App-Logs analysieren
python app.py 2>&1 | tee app.log
# Suche nach "Uvicorn running" oder FastAPI-Fehlern
```

#### **Schritt 3: Plugin-Isolation testen**
```bash
# Test ohne Web-Server
python -c "
import asyncio
from app import create_app

async def test():
    app = create_app()
    await app.start_all_plugins()
    
    # Test Core Services
    health = await app.call_service('web_api.get_health_status')
    print('✅ Health:', health)
    
    conv_id = await app.call_service('memory_store.create_conversation')
    response = await app.call_service('chat_ai.generate_response', 'Hello!', conv_id)
    print('✅ Chat:', response[:50])
    
    await app.stop_all_plugins()

asyncio.run(test())
"
```

#### **Schritt 4: Manueller Server-Start**
```bash
# Falls automatischer Start fehlschlägt
python -c "
import asyncio
from app import create_app

async def manual_start():
    app = create_app()
    await app.start_all_plugins()
    
    # Manueller Server-Start
    await app.call_service('web_api.start_server', host='0.0.0.0', port=8000)
    
    print('🚀 Server manually started')
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        await app.stop_all_plugins()

asyncio.run(manual_start())
"
```

### **Problem 2: Import-Fehler in web_api Plugin**

**Mögliche Ursachen:**
- FastAPI nicht installiert → ImportError in web_api/plugin.py
- Pydantic Version-Konflikt
- Missing Dependencies

**Debugging:**
```bash
# Test Plugin-Imports
python -c "
try:
    from plugins.web_api import WebAPIPlugin
    print('✅ WebAPI Plugin import OK')
except Exception as e:
    print('❌ WebAPI Plugin import failed:', e)

try:
    import fastapi
    import uvicorn
    print('✅ FastAPI/Uvicorn available')
except Exception as e:
    print('❌ FastAPI/Uvicorn missing:', e)
"
```

### **Problem 3: Event-System oder DI-Probleme**

**Debugging:**
```bash
# Test Plugin-Dependencies
python -c "
import asyncio
from app import create_app

async def test_deps():
    app = create_app()
    await app.start_all_plugins()
    
    # Test Service-Calls
    services = app.list_services()
    print(f'Available services: {len(services)}')
    
    for service in ['memory_store.create_conversation', 
                   'chat_ai.get_model_info',
                   'web_api.get_health_status']:
        try:
            if 'create_conversation' in service:
                result = await app.call_service(service)
            else:
                result = await app.call_service(service)
            print(f'✅ {service}: OK')
        except Exception as e:
            print(f'❌ {service}: {e}')
    
    await app.stop_all_plugins()

asyncio.run(test_deps())
"
```

---

## 🧪 **EXTERNAL TESTING PLAN**

### **Phase 1: Human Tester**
**Ziel**: Fremder Entwickler baut Reference-App in ≤10 Minuten nach

**Test-Protokoll:**
1. **Setup-Zeit messen** (Ziel: <10 Min)
2. **README.md Anweisungen befolgen**
3. **Alle API-Endpoints testen**
4. **Feedback dokumentieren**

**Test-Commands:**
```bash
# Health Check
curl http://localhost:8000/health

# Chat Test
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?"}'

# Conversations
curl http://localhost:8000/conversations

# API Docs
open http://localhost:8000/docs
```

### **Phase 2: AI Agent Tester**
**Ziel**: Autonomer AI-Agent baut und testet Reference-App

**Agent-Tasks:**
1. **Repository clonen** (Branch s1/reference-app-design)
2. **Setup durchführen** (venv, dependencies, .env)
3. **App starten** und Logs analysieren
4. **API-Tests ausführen** (alle Endpoints)
5. **Integration-Tests laufen lassen**
6. **Bericht erstellen** mit Erfolg/Fehler-Status

---

## 📋 **AKZEPTANZKRITERIEN**

### **Debugging Abgeschlossen wenn:**
- [ ] **FastAPI Server startet erfolgreich** (Uvicorn-Logs sichtbar)
- [ ] **Health-Endpoint antwortet** (200 OK auf /health)
- [ ] **Chat-Endpoint funktioniert** (POST /chat gibt AI-Response)
- [ ] **API-Docs erreichbar** (http://localhost:8000/docs)
- [ ] **Alle Integration-Tests bestehen** (pytest)

### **External Testing Abgeschlossen wenn:**
- [ ] **Human Tester**: Setup in <10 Min + alle APIs funktionieren
- [ ] **AI Agent Tester**: Autonomer Test erfolgreich
- [ ] **Feedback dokumentiert** und ggf. Verbesserungen implementiert

### **Sprint 1 S1.4 Abgeschlossen wenn:**
- [ ] **Reference-App läuft produktiv** (alle Endpoints)
- [ ] **Externe Validierung erfolgreich** (2 Tester)
- [ ] **Dokumentation vollständig** (README, ARCHITECTURE)
- [ ] **Framework Production-Readiness demonstriert**

---

## 🔧 **QUICK-FIXES FÜR HÄUFIGE PROBLEME**

### **FastAPI nicht installiert:**
```bash
pip install fastapi uvicorn pydantic
```

### **Port-Konflikt:**
```bash
# Anderen Port verwenden
export SERVER_PORT=8001
python app.py
```

### **Import-Fehler:**
```bash
# PYTHONPATH setzen
export PYTHONPATH=.
python app.py
```

### **Manifest-Fehler (falls wieder aktiviert):**
```bash
export ENABLE_MANIFESTS=false
python app.py
```

---

## 📊 **ERFOLGS-METRIKEN**

- **Setup-Zeit**: <10 Minuten (gemessen)
- **API-Response-Zeit**: <1 Sekunde
- **Test-Coverage**: 100% Integration-Tests bestehen
- **External-Validation**: 2/2 Tester erfolgreich
- **Framework-KPI**: Production-Readiness demonstriert ✅

---

**NÄCHSTE SCHRITTE**: Debugging starten mit FastAPI-Dependencies prüfen!
