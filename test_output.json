{"app": {"name": "test_cli_app", "plugin_count": 3, "max_fractal_depth": 10}, "plugins": [{"registration_name": "test_service_plugin", "class_name": "TestServicePlugin", "module": "test_app_factory", "services": [{"name": "get_data", "method_name": "get_data", "signature": {"parameters": [{"name": "param", "type": "<class 'str'>", "default": "default", "kind": "POSITIONAL_OR_KEYWORD"}], "return_type": "<class 'str'>", "docstring": {"summary": "Get some test data.", "description": null, "raw": "Get some test data."}}, "metadata": {"name": "get_data", "timeout_seconds": null, "description": null, "method_name": "get_data", "signature": "(self, param: str = 'default') -> str", "parameters": ["{'name': 'param', 'annotation': <class 'str'>, 'default': 'default', 'kind': 'POSITIONAL_OR_KEYWORD'}"]}}, {"name": "process_data", "method_name": "process_data", "signature": {"parameters": [{"name": "input_data", "type": "<class 'str'>", "default": null, "kind": "POSITIONAL_OR_KEYWORD"}], "return_type": "dict[str, str]", "docstring": {"summary": "Process input data.", "description": null, "raw": "Process input data."}}, "metadata": {"name": "process_data", "timeout_seconds": null, "description": null, "method_name": "process_data", "signature": "(self, input_data: str) -> dict[str, str]", "parameters": ["{'name': 'input_data', 'annotation': <class 'str'>, 'default': None, 'kind': 'POSITIONAL_OR_KEYWORD'}"]}}], "event_listeners": [], "dependencies": [], "metadata": {"name": "test_service_plugin", "version": "1.0.0"}}, {"registration_name": "test_event_plugin", "class_name": "TestEventPlugin", "module": "test_app_factory", "services": [{"name": "emit_event", "method_name": "emit_event", "signature": {"parameters": [{"name": "event_type", "type": "<class 'str'>", "default": null, "kind": "POSITIONAL_OR_KEYWORD"}], "return_type": "<class 'str'>", "docstring": {"summary": "Emit a test event.", "description": null, "raw": "Emit a test event."}}, "metadata": {"name": "emit_event", "timeout_seconds": null, "description": null, "method_name": "emit_event", "signature": "(self, event_type: str) -> str", "parameters": ["{'name': 'event_type', 'annotation': <class 'str'>, 'default': None, 'kind': 'POSITIONAL_OR_KEYWORD'}"]}}], "event_listeners": [{"method_name": "on_user_created", "event_pattern": "user.created", "signature": {"parameters": [{"name": "user_id", "type": "<class 'int'>", "default": null, "kind": "POSITIONAL_OR_KEYWORD"}, {"name": "username", "type": "<class 'str'>", "default": null, "kind": "POSITIONAL_OR_KEYWORD"}], "return_type": "None", "docstring": {"summary": "Handle user creation events.", "description": null, "raw": "Handle user creation events."}}, "metadata": {"patterns": ["user.created"], "timeout_seconds": null, "description": null, "priority": 0, "method_name": "on_user_created", "signature": "(self, user_id: int, username: str) -> None", "parameters": ["{'name': 'user_id', 'annotation': <class 'int'>, 'default': None, 'kind': 'POSITIONAL_OR_KEYWORD'}", "{'name': 'username', 'annotation': <class 'str'>, 'default': None, 'kind': 'POSITIONAL_OR_KEYWORD'}"]}}], "dependencies": [], "metadata": {"name": "test_event_plugin", "version": "1.0.0"}}, {"registration_name": "test_dependent_plugin", "class_name": "TestDependentPlugin", "module": "test_app_factory", "services": [{"name": "dependent_service", "method_name": "dependent_service", "signature": {"parameters": [{"name": "input_data", "type": "<class 'str'>", "default": null, "kind": "POSITIONAL_OR_KEYWORD"}], "return_type": "dict[str, str]", "docstring": {"summary": "Service that depends on another plugin.", "description": null, "raw": "Service that depends on another plugin."}}, "metadata": {"name": "dependent_service", "timeout_seconds": null, "description": null, "method_name": "dependent_service", "signature": "(self, input_data: str) -> dict[str, str]", "parameters": ["{'name': 'input_data', 'annotation': <class 'str'>, 'default': None, 'kind': 'POSITIONAL_OR_KEYWORD'}"]}}], "event_listeners": [], "dependencies": [{"plugin_identifier": "test_service_plugin", "optional": false, "version_constraint": null}], "metadata": {"name": "test_dependent_plugin", "version": "1.0.0"}}], "dependency_graph": {"nodes": [{"id": "test_service_plugin", "type": "plugin", "metadata": {"class_name": "TestServicePlugin", "module": "test_app_factory", "plugin_name": "test_service_plugin", "plugin_version": "1.0.0"}}, {"id": "test_event_plugin", "type": "plugin", "metadata": {"class_name": "TestEventPlugin", "module": "test_app_factory", "plugin_name": "test_event_plugin", "plugin_version": "1.0.0"}}, {"id": "test_dependent_plugin", "type": "plugin", "metadata": {"class_name": "TestDependentPlugin", "module": "test_app_factory", "plugin_name": "test_dependent_plugin", "plugin_version": "1.0.0"}}], "edges": [{"from": "test_dependent_plugin", "to": "test_service_plugin", "type": "depends_on", "optional": false, "version_constraint": null}]}, "metadata": {"generated_at": "2025-06-02T09:42:21.971461+00:00", "generated_by": "plugginger-inspect", "schema_version": "1.0.0"}}