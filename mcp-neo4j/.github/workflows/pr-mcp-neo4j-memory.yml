name: MCP Neo4j Memory Tests

on:
  push:
    branches: [ main, master ]
    paths:
      - 'servers/mcp-neo4j-memory/**'
  pull_request:
    branches: [ main, master ]
    paths:
      - 'servers/mcp-neo4j-memory/**'
  workflow_dispatch:  # Allows manual triggering of the workflow

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Install UV
      run: |
        curl -LsSf https://astral.sh/uv/install.sh | sh
        echo "$HOME/.cargo/bin" >> $GITHUB_PATH
    
    - name: Install dependencies
      run: |
        cd servers/mcp-neo4j-memory
        uv venv
        uv pip install -e ".[dev]"
    
    - name: Run tests
      run: |
        cd servers/mcp-neo4j-memory
        ./test.sh