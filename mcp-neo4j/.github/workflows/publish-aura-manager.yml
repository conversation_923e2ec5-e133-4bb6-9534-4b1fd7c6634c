# This workflow will upload a Python Package to PyPI when a release is created
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python#publishing-to-package-registries

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Publish Neo4j MCP Aura Manager Package

on:
  push:
    tags:
      - mcp-neo4j-aura-manager-v*
  workflow_dispatch:  # Allows manual triggering of the workflow

permissions:
  contents: read

jobs:

  pypi-publish:
    runs-on: ubuntu-latest

    permissions:
      # IMPORTANT: this permission is mandatory for trusted publishing
      id-token: write

    # Dedicated environments with protections for publishing are strongly recommended.
    # For more information, see: https://docs.github.com/en/actions/deployment/targeting-different-environments/using-environments-for-deployment#deployment-protection-rules
    environment:
      name: pypi
      url: https://pypi.org/project/mcp-neo4j-aura-manager/

    steps:

      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: "Set up Python"
        uses: actions/setup-python@v5
        with:
          python-version-file: "servers/mcp-neo4j-cloud-aura-api/pyproject.toml"

      - name: Build release distributions
        run: |
          cd servers/mcp-neo4j-cloud-aura-api/
          uv build

      - name: Publish release
        env:
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          cd servers/mcp-neo4j-cloud-aura-api/
          uv publish
