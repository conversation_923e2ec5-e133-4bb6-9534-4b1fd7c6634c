# path to a directory with all packages
storage: ../tmp/local-registry/storage

# a list of other known repositories we can talk to
uplinks:
  npmjs:
    url: https://registry.npmjs.org/
    maxage: 60m

packages:
  '**':
    # give all users (including non-authenticated users) full access
    # because it is a local registry
    access: $all
    publish: $all
    unpublish: $all

    # if package is not available locally, proxy requests to npm registry
    proxy: npmjs

# log settings
log:
  type: stdout
  format: pretty
  level: warn

publish:
  allow_offline: true # set offline to true to allow publish offline
