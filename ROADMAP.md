# Plugginger Framework Roadmap

## 🤖 <PERSON><PERSON><PERSON> zukünftige KI-Instanzen: Projekt-Kontext

**Wenn du diese Date<PERSON> liest, arbeitest du wahrscheinlich an der Weiterentwicklung des Plugginger-Frameworks. Hier ist der wichtige Kontext:**

### Was ist Plugginger?
Plugginger ist ein Python-Framework für modulare Anwendungen, ähnlich wie Flask-Extensions oder Django-Apps, aber mit einem entscheidenden Unterschied: **Es ist speziell dafür designed, dass KI-Agenten eigenständig Plugins entwickeln können**.

### Warum ist das wichtig?
- **Problem**: Bestehende Plugin-Systeme erfordern tiefes Framework-Wissen
- **Lösung**: Plugginger macht Plugin-Entwicklung so einfach, dass KI-Agenten es können
- **Ziel**: Ein Ökosystem, wo KI-Agenten autonom nützliche Plugins erstellen

### Aktuelle Architektur (vereinfacht)
```python
# So sieht ein Plugin aus:
@plugin(name="my_cache", version="1.0.0")
class CachePlugin(PluginBase):
    needs: List[Depends] = [Depends("logger")]

    @service()
    async def get(self, key: str) -> Optional[str]:
        """Get value from cache."""
        return self._cache.get(key)

    @on_event("app.shutdown")
    async def cleanup(self, event_data: dict) -> None:
        """Clean up resources."""
        self._cache.clear()
```

### Warum diese Roadmap existiert
Das Framework ist **technisch funktionsfähig** (810 Tests, 74% Coverage), aber **KI-Agenten können es noch nicht effektiv nutzen**, weil:
1. Plugin-Metadaten nur via Python-Import lesbar sind
2. Keine standardisierten Templates existieren
3. Kein schnelles Feedback-System für KI-generierten Code

## 🎯 Vision: KI-First Plugin Framework

Plugginger soll das erste wirklich **KI-freundliche Plugin-System** werden. KI-Agenten sollen eigenständig Plugins entwickeln, testen und deployen können.

## 📊 Aktueller Stand (Stand: 1. Juni 2025)

### ✅ Was bereits funktioniert
- **810 Tests** (Unit + Integration + E2E) - alle grün [![Tests](https://github.com/jkehrhahn/plugginger/actions/workflows/test.yml/badge.svg)](https://github.com/jkehrhahn/plugginger/actions) [![Coverage](https://codecov.io/gh/jkehrhahn/plugginger/branch/main/graph/badge.svg)](https://codecov.io/gh/jkehrhahn/plugginger)
- **74% Code Coverage** - solide Testabdeckung
- **Robuste Architektur**: Builder-Phasen, Runtime-Facade, Event-System
- **Fractal Composition**: Verschachtelte Apps mit Event-Bridging
- **Dependency Injection**: Automatische DI mit Proxy-Pattern
- **Fault Tolerance**: Verschiedene Error-Handling-Strategien
- **Type Safety**: mypy --strict compliant

**Verifikation**: `pytest tests/ -q` sollte in <30 Sekunden grün durchlaufen

### 🚨 Was für KI-Agenten noch fehlt
- **Maschinenlesbare Metadaten**: Plugins nur via Python-Import inspizierbar
- **Discovery-API**: Keine JSON-Schnittstelle für App-Struktur
- **Standardisierte Templates**: Kein Scaffold-System für neue Plugins
- **Version-Management**: Keine SemVer-Validation für Dependencies
- **Developer-Feedback**: Keine schnelle Validierung für KI-generierten Code

## �️ Wichtige Dateien & Verzeichnisse (für Orientierung)

### Kern-Framework
```
src/plugginger/
├── api/                    # Public API (stabil)
│   ├── builder.py         # PluggingerAppBuilder - Haupt-Entry-Point
│   ├── plugin.py          # @plugin Decorator & PluginBase
│   ├── service.py         # @service Decorator
│   ├── events.py          # @on_event Decorator
│   └── app.py             # PluggingerAppInstance - Runtime
├── _internal/             # Framework-Internals
│   ├── builder_phases/    # Build-Pipeline (4 Phasen)
│   ├── runtime/           # Event/Service Dispatcher, Lifecycle
│   └── validation/        # Input-Validation
└── core/                  # Shared Types & Exceptions
```

### Tests & Beispiele
```
tests/
├── unit/                  # 781 Unit-Tests (alle grün)
├── integration/           # 10 Integration-Tests
└── e2e/                   # 19 E2E-Tests (inkl. Fractal Composition)

examples/                  # Noch leer - hier sollen Reference-Plugins hin
```

### Entwicklung
```
pyproject.toml            # Dependencies, Build-Config
pytest.ini               # Test-Konfiguration (Coverage-Gate: 75%)
ruff.toml                 # Linting-Rules
```

## 🔍 Wie du den aktuellen Stand checkst

### 1. Tests laufen lassen
```bash
# Alle Tests (sollten grün sein)
python -m pytest tests/ -v

# Nur Unit-Tests (781 Tests)
python -m pytest tests/unit/ -v

# Coverage-Report
python -m pytest tests/ --cov=src/plugginger --cov-report=html
```

### 2. Framework ausprobieren
```bash
# Einfaches Beispiel
cd examples/  # (noch leer - hier könntest du anfangen)

# Oder schaue in die E2E-Tests für Beispiele:
# tests/e2e/test_fractal_composition_integration.py
# tests/e2e/test_event_system_integration.py
```

### 3. Code-Qualität prüfen
```bash
# Linting (sollte sauber sein)
ruff check src/

# Type-Checking (sollte ohne Fehler sein)
mypy src/plugginger --strict
```

## �🗺️ 3-Sprint MVP-Roadmap

### 🔴 Sprint 1 (Woche 1-2): Manifest + Discovery MVP

**Ziel**: KI-Agenten können App-Struktur ohne Python-Import verstehen
**Definition of Done**: Reference-App startet in CI und liefert 200 OK auf `/health`, `plugginger inspect --json` funktioniert

#### S1.1 - Soft API-Freeze
**Status**: done

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| Version Bump | Setze Version auf 0.9.0-alpha | done | AI-Agent-001 | 2025-01-15 |
| Experimental Namespace | Erstelle `plugginger.experimental.*` für unstable APIs | done | AI-Agent-002 | 2025-01-15 |
| Core API Documentation | Dokumentiere stable candidates (plugin, service, on_event) | done | AI-Agent-003 | 2025-01-15 |
| Breaking Change Policy | Definiere was noch brechen darf vs. stable | done | AI-Agent-004 | 2025-01-15 |

#### S1.2 - Manifest-Schema
**Status**: done

| Task | Beschreibung | Status | Assignee | Completion Date | Issue |
|------|-------------|--------|----------|-----------------|-------|
| Schema Definition | YAML-Schema für Plugin-Manifeste | done | AI-Agent-005 | 2025-01-15 | #14 |
| Auto-Generation | Builder exportiert Manifeste automatisch | done | AI-Agent-006 | 2025-01-15 | #1 |
| Validation | Manifest-Validation beim Plugin-Load | done | AI-Agent-009 | 2025-06-01 | #7 |
| Examples | Beispiel-Manifeste für verschiedene Plugin-Typen | done | AI-Agent-008 | 2025-06-01 | #3 |

**Manifest-Format**: Siehe Beispiel oben in der Architektur-Sektion

#### S1.3 - Discovery Command
**Status**: todo

| Task | Beschreibung | Status | Assignee | Completion Date | Issue |
|------|-------------|--------|----------|-----------------|-------|
| CLI Command | `plugginger inspect --json` Implementation | done | AI-Agent-010 | 2025-06-01 | #11 |
| JSON Schema | Standardisiertes JSON-Format für App-Graph | review | AI-Agent-011 | - | #13 |
| Service Signatures | Export von Service-Signaturen mit Typen | review | AI-Agent-012 | - | #15 |
| Dependency Graph | Visualisierung der Plugin-Dependencies | todo | - | - | - |

#### S1.4 - Reference-App Proof-of-Concept
**Status**: todo

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| App Design | "AI-Chat mit Memory" - Architektur | todo | - | - |
| Plugin Development | chat_ai, memory_store, web_api Plugins | todo | - | - |
| Documentation | Setup-Guide für <10 Minuten | todo | - | - |
| External Testing | 2 externe Tester (Mensch + KI-Agent) | todo | - | - |

**Sprint 1 KPI**: Fremder Entwickler baut Reference-App in ≤10 Minuten nach

---

### 🟡 Sprint 2 (Woche 3-4): Developer Experience Essentials

**Ziel**: KI-Agenten können eigenständig neue Plugins erstellen
**Definition of Done**: `plugginger new plugin test_plugin && cd test_plugin && pytest` läuft in <60 Sekunden durch

#### S2.1 - Scaffold-CLI
**Status**: todo

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| CLI Implementation | `plugginger new plugin` Command | todo | - | - |
| Templates | KI-optimierte Plugin-Templates | todo | - | - |
| Project Structure | Standard-Layout für neue Plugins | todo | - | - |
| Test Integration | Automatische Test-Setup | todo | - | - |

#### S2.2 - SemVer Dependencies
**Status**: todo

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| Depends Enhancement | `Depends("logger", version=">=1.0.0,<2.0.0")` | todo | - | - |
| Version Parsing | Semantic Version Validation | todo | - | - |
| Conflict Detection | Builder-Validation für Inkompatibilitäten | todo | - | - |
| Error Messages | Klare Fehlermeldungen mit Lösungsvorschlägen | todo | - | - |

#### S2.3 - Docstring-Konvention
**Status**: todo

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| Convention Definition | Standard-Format für Service-Docstrings | todo | - | - |
| Template Updates | Scaffold-Templates mit Beispielen | todo | - | - |
| Validation | Linting für Docstring-Format | todo | - | - |
| Documentation | Guide für KI-freundliche Dokumentation | todo | - | - |

#### S2.4 - Doctor Command
**Status**: todo

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| CLI Implementation | `plugginger doctor` Health-Check | todo | - | - |
| Validation Suite | Manifest, Dependencies, Signatures | todo | - | - |
| Error Reporting | Strukturierte Fehlerausgabe | todo | - | - |
| CI Integration | Exit-Codes für automatisierte Tests | todo | - | - |

**Sprint 2 KPI**: `plugginger new plugin` → lauffähiger Test in ≤60 Sekunden

---

### 🟠 Sprint 3 (Woche 5-6): Fokus-Entscheidung

**Ziel**: Wähle EINEN Fokus für maximale Wirkung

#### Option A: Resource-Limits (Sicherheit-fokussiert)
**Status**: pending_decision

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| Memory Limits | Plugin-spezifische Memory-Quotas | todo | - | - |
| Timeout Management | Service-Call und Event-Processing Timeouts | todo | - | - |
| Monitoring Hooks | Minimal Metrics für Performance-Tracking | todo | - | - |
| Isolation Testing | Plugin kann andere nicht crashen | todo | - | - |

#### Option B: Hot-Reload (DX-fokussiert)
**Status**: pending_decision

| Task | Beschreibung | Status | Assignee | Completion Date |
|------|-------------|--------|----------|-----------------|
| File Watcher | Überwachung von Plugin-Dateien | todo | - | - |
| Selective Reload | Nur geänderte Plugins neu laden | todo | - | - |
| Development Server | `plugginger dev --watch` Command | todo | - | - |
| State Preservation | Plugin-State wo möglich erhalten | todo | - | - |

**Sprint 3 KPI**:
- Option A: Plugin-Isolation verhindert System-Crashes
- Option B: Änderungszeit von 3s auf <1s reduziert

**Entscheidungshilfe für Option A vs B**:
- Wähle **Option A** wenn: Sicherheit/Stabilität wichtiger als DX
- Wähle **Option B** wenn: Development-Speed wichtiger als Isolation
- **Empfehlung**: Frage echte Nutzer oder baue Reference-App und schaue, was mehr schmerzt
- **Entscheidungsfrist**: Ende Sprint 2 Retro (spätestens Woche 4)

## 🤖 Automatisierungs-Ideen (niedrige Priorität)

### CI/CD-Verbesserungen
- **10-Min-Challenge**: GitHub Action testet Reference-App Setup in ≤10 Min
- **Doctor-Check**: `plugginger doctor` als Required Check für alle PRs [![Doctor](https://img.shields.io/badge/doctor-0%20issues-green)](https://github.com/jkehrhahn/plugginger/actions)
- **DoD-Check**: GitHub Action warnt bei `status: done` ohne `Completion Date`
- **Roadmap-Lint**: Pre-commit Hook prüft Tabellen-Konsistenz

### Monitoring
- **KPI-Tracking**: Automatische Messung der Erfolgs-Metriken
- **Performance-Regression**: Benchmark-Tests in CI
- **Coverage-Trend**: Historische Coverage-Entwicklung

### Quick-Win-Ideen
- **MkDocs Integration**: ROADMAP.md als Landing-Page mit verlinkbaren Sektionen
- **`plugginger doctor --fix`**: Auto-Fix für fehlende `manifest.yaml` und Standards
- **UTC-Timestamps**: Manifest-Export immer mit UTC+Z Format (`2025-06-01T12:34:56Z`)

## 🚫 Bewusst NICHT in MVP

### Verschoben auf Post-MVP
- ❌ **Enterprise-Security** (RBAC, Secret-Rotation)
- ❌ **OpenTelemetry-Integration** 
- ❌ **Plugin-Registry-Backend**
- ❌ **Distributed Tracing**
- ❌ **Advanced-DI-Features**

### Experimentell markiert
- 🧪 **Fractal-Composition-Advanced** → `plugginger.experimental.fractal`
- 🧪 **Event-Sourcing** → `plugginger.experimental.events`
- 🧪 **Plugin-Marketplace** → `plugginger.experimental.registry`

## � Security-Baseline & Packaging-Entscheidungen

### Security-Mindeststandards
- **Plugin-Isolation**: Plugins laufen non-privileged, kein `os.exec()` oder `subprocess` erlaubt
- **Execution-Mode**: Standardmäßig `execution_mode: thread`, `process`/`external` nur nach Freigabe
- **Resource-Limits**: Memory/CPU-Quotas pro Plugin (implementiert in Sprint 3 Option A)
- **Input-Validation**: Alle Plugin-Inputs werden validiert
- **No-Privilege-Escalation**: Plugins können keine System-Rechte erlangen

### Packaging-Konvention
- **PyPI-Namespace**: Plugins verwenden `plugginger-<name>` Präfix (z.B. `plugginger-cache`), keine Unterstriche im `<name>`-Teil (PEP-503)
- **Manifest-Location**: `manifest.yaml` im Package-Root
- **Dependencies**: Plugins deklarieren `plugginger>=0.9.0` als Dependency
- **Versioning**: Plugins folgen SemVer, kompatibel mit `Depends("name", version=">=1.0.0,<2.0.0")`

## �📋 Governance

### Contribution Guidelines
1. Nutze `plugginger new plugin` für Setup
2. Folge Docstring-Konvention (siehe Beispiele)
3. Teste mit `plugginger doctor` vor PR

### RFC-Prozess
- Große Änderungen: Issue mit "RFC:" Prefix (nutze [RFC-Template](docs/RFC_TEMPLATE.md))
- 48h Diskussion, dann Entscheidung
- Breaking Changes nur in `experimental.*`

## 📊 Erfolgs-Metriken

| Metrik | Ziel | Baseline | Aktuell | Status |
|--------|------|----------|---------|--------|
| Test Coverage | >75% | 74% | 74% | 🟡 |
| External Plugin Creation Time | <10 Min | unbekannt | - | todo |
| KI-Agent Success Rate | >80% | unbekannt | - | todo |
| Plugin Scaffold Time | <60 Sek | unbekannt | - | todo |

## 📝 Protokoll-Hinweise für zukünftige KI-Instanzen

### Wie du mit den Protokoll-Tabellen arbeitest

1. **Status-Updates**: Ändere Status von `todo` zu `doing` (in Arbeit) zu `done` (fertig) oder `blocked` (blockiert)
2. **Assignee**: Trage deinen Namen/ID ein wenn du an einem Task arbeitest
3. **Completion Date**: Trage das Datum ein wenn Task fertig ist
4. **Neue Tasks**: Füge neue Zeilen hinzu wenn du Subtasks identifizierst

### Wichtige Arbeitsregeln

1. **Immer Tests zuerst**: Bevor du Code änderst, stelle sicher dass alle Tests grün sind
2. **Kleine Schritte**: Lieber 5 kleine PRs als 1 großer
3. **KPI-fokussiert**: Jeder Sprint hat ein messbares Ziel - fokussiere darauf
4. **Dokumentation**: Aktualisiere diese ROADMAP.md bei größeren Änderungen

### Bei Problemen/Blockern

1. **Technische Probleme**: Schaue in die E2E-Tests für funktionierende Beispiele
2. **Architektur-Fragen**: Schaue in `src/plugginger/api/` für Public API
3. **Test-Probleme**: `pytest tests/ -v` sollte immer grün sein
4. **Unklarheiten**: Erstelle GitHub Issue mit "RFC:" Prefix

### Erfolg messen

- **Sprint 1**: Kann ein Fremder die Reference-App in 10 Min nachbauen?
- **Sprint 2**: Kann `plugginger new plugin` in 60 Sek einen funktionierenden Test erzeugen?
- **Sprint 3**: Funktioniert die gewählte Option (A oder B) messbar besser?

## 🎯 Nächste Schritte für dich

1. **Orientierung**: Laufe die Tests (`pytest tests/ -v`) um sicherzustellen dass alles funktioniert
2. **Verstehen**: Schaue dir `tests/e2e/test_fractal_composition_integration.py` an für ein vollständiges Beispiel
3. **Planen**: Entscheide welchen Sprint/Task du als nächstes angehst
4. **Protokollieren**: Update die entsprechende Tabelle mit deinem Status

---

**Letzte Aktualisierung**: 1. Juni 2025
**Nächste Review**: Nach Sprint 1 Completion
**Maintainer**: KI-Agenten (rotierend)
**RFC-Template**: `docs/RFC_TEMPLATE.md`
