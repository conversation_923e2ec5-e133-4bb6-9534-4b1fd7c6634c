# Service Signatures with Docstrings

This example demonstrates how the enhanced `plugginger inspect --json` command exports detailed service signatures including docstring information for AI agents.

## Overview

The Service Signatures feature extends the app graph discovery to include:

- **Complete parameter information** (name, type, default, kind)
- **Return type annotations**
- **Docstring extraction** (summary, description, raw)
- **JSON Schema validation**

This enables AI agents to understand service interfaces without importing Python code.

## Example Plugin

```python
from plugginger.api.plugin import PluginBase
from plugginger.api.service import service

class DataProcessorPlugin(PluginBase):
    @service()
    async def process_data(self, data: str, format: str = "json", validate: bool = True) -> dict[str, Any]:
        """
        Process input data and return structured result.
        
        This service processes raw input data according to the specified format
        and optionally validates the result before returning it.
        
        Args:
            data: Raw input data to process
            format: Output format (json, xml, yaml)
            validate: Whether to validate the processed result
            
        Returns:
            Processed data as a dictionary with metadata
            
        Raises:
            ValueError: If data format is invalid
            ProcessingError: If processing fails
        """
        # Implementation here
        return {"processed": True, "data": data}
```

## JSON Output

When running `plugginger inspect --json`, the service signature is exported as:

```json
{
  "plugins": [
    {
      "registration_name": "data_processor",
      "services": [
        {
          "name": "process_data",
          "method_name": "process_data",
          "signature": {
            "parameters": [
              {
                "name": "data",
                "type": "<class 'str'>",
                "default": null,
                "kind": "POSITIONAL_OR_KEYWORD"
              },
              {
                "name": "format",
                "type": "<class 'str'>",
                "default": "json",
                "kind": "POSITIONAL_OR_KEYWORD"
              },
              {
                "name": "validate",
                "type": "<class 'bool'>",
                "default": "True",
                "kind": "POSITIONAL_OR_KEYWORD"
              }
            ],
            "return_type": "dict[str, typing.Any]",
            "docstring": {
              "summary": "Process input data and return structured result.",
              "description": "This service processes raw input data according to the specified format\nand optionally validates the result before returning it.\n\nArgs:\n    data: Raw input data to process\n    format: Output format (json, xml, yaml)\n    validate: Whether to validate the processed result\n    \nReturns:\n    Processed data as a dictionary with metadata\n    \nRaises:\n    ValueError: If data format is invalid\n    ProcessingError: If processing fails",
              "raw": "Process input data and return structured result.\n        \n        This service processes raw input data according to the specified format\n        and optionally validates the result before returning it.\n        \n        Args:\n            data: Raw input data to process\n            format: Output format (json, xml, yaml)\n            validate: Whether to validate the processed result\n            \n        Returns:\n            Processed data as a dictionary with metadata\n            \n        Raises:\n            ValueError: If data format is invalid\n            ProcessingError: If processing fails"
            }
          },
          "metadata": {
            "name": "process_data"
          }
        }
      ]
    }
  ]
}
```

## AI Agent Benefits

With this detailed signature information, AI agents can:

1. **Understand service interfaces** without code inspection
2. **Generate correct service calls** with proper parameters
3. **Validate compatibility** before plugin integration
4. **Generate documentation** from docstrings
5. **Handle errors appropriately** based on documented exceptions

## Schema Validation

The JSON output is validated against the extended app graph schema that includes:

```json
{
  "method_signature": {
    "type": "object",
    "required": ["parameters", "return_type"],
    "properties": {
      "parameters": { "type": "array" },
      "return_type": { "type": ["string", "null"] },
      "docstring": {
        "type": ["object", "null"],
        "properties": {
          "summary": { "type": "string" },
          "description": { "type": ["string", "null"] },
          "raw": { "type": "string" }
        }
      }
    }
  }
}
```

## Usage

```bash
# Export app graph with service signatures
plugginger inspect my.app:factory --json

# Save to file for AI agent processing
plugginger inspect my.app:factory --json --output app-graph.json

# Validate schema
plugginger schema app-graph --output schema.json
```

This feature is part of Sprint 1 S1.3 Discovery Command, enabling KI-Agent compatibility for the Plugginger framework.
