# VSCode Testing Guide

This guide explains how to run tests in VSCode and resolve common issues with the Test Explorer.

## Known Issues

### Integration Test Compatibility

Some integration tests may fail in the VSCode Test Explorer but pass when run via command line. This is a known issue with complex integration tests that involve CLI commands and mocking.

**Affected Tests:**
- `tests/integration/test_cli_inspect_integration.py::TestCLIInspectIntegration::test_inspect_json_output_structure`

**Workaround:**
1. Run integration tests via command line: `python -m pytest tests/integration/ -v`
2. Use the simplified VSCode-compatible tests in `tests/integration/test_vscode_compatibility.py`

## VSCode Configuration

The project includes VSCode-specific pytest configuration:

### `.vscode/settings.json`
```json
{
  "python.testing.pytestArgs": [
    "-v",
    "--tb=short", 
    "--no-cov",
    "--disable-warnings",
    "-p", "no:cacheprovider"
  ]
}
```

### `pytest-vscode.ini`
```ini
[pytest]
minversion = 7.0
addopts = -ra -q --tb=short --no-cov
testpaths = tests
asyncio_mode = auto
```

## Running Tests

### Command Line (Recommended)
```bash
# All tests
python -m pytest

# Specific test file
python -m pytest tests/unit/schemas/test_json_schema.py -v

# Integration tests
python -m pytest tests/integration/ -v --no-cov

# VSCode-compatible tests only
python -m pytest tests/integration/test_vscode_compatibility.py -v
```

### VSCode Test Explorer
1. Open Command Palette (`Ctrl+Shift+P`)
2. Run "Python: Refresh Tests"
3. Use Test Explorer panel to run individual tests
4. If tests fail in VSCode but pass in CLI, use command line instead

## Test Categories

### Unit Tests
- **Location**: `tests/unit/`
- **VSCode Compatible**: ✅ Yes
- **Coverage**: Required

### Integration Tests
- **Location**: `tests/integration/`
- **VSCode Compatible**: ⚠️ Partial (some tests may fail in VSCode)
- **Coverage**: Not required for individual test runs

### VSCode-Compatible Integration Tests
- **Location**: `tests/integration/test_vscode_compatibility.py`
- **VSCode Compatible**: ✅ Yes
- **Purpose**: Simplified versions of integration tests that work reliably in VSCode

## Troubleshooting

### Test Fails in VSCode but Passes in CLI

**Symptoms:**
- Test shows as failed in VSCode Test Explorer
- Same test passes when run via `python -m pytest`

**Causes:**
- Different Python environment
- VSCode pytest configuration differences
- Complex mocking or CLI interactions

**Solutions:**
1. Run test via command line to verify it actually works
2. Check VSCode Python interpreter matches project virtual environment
3. Use simplified test versions from `test_vscode_compatibility.py`
4. Disable coverage for individual test runs: `--no-cov`

### Coverage Failures

**Symptoms:**
- Test passes but fails due to low coverage
- "Coverage failure: total of X is less than fail-under=75"

**Solutions:**
1. Run with `--no-cov` flag for individual tests
2. Run full test suite for coverage: `python -m pytest`
3. Use VSCode pytest args that include `--no-cov`

### Import Errors

**Symptoms:**
- `ModuleNotFoundError` in VSCode but not CLI
- Tests can't find project modules

**Solutions:**
1. Verify VSCode Python interpreter: `Ctrl+Shift+P` → "Python: Select Interpreter"
2. Choose `.venv/bin/python` from project directory
3. Restart VSCode after changing interpreter
4. Check `python.testing.cwd` setting in VSCode

## Best Practices

### For Test Development
1. Write tests to pass in both CLI and VSCode
2. Avoid complex CLI mocking in integration tests
3. Use direct API calls instead of subprocess when possible
4. Keep integration tests simple and focused

### For Daily Development
1. Use VSCode Test Explorer for unit tests
2. Use command line for integration tests
3. Run full test suite before commits: `python -m pytest`
4. Check both test results and coverage

### For CI/CD
1. Always use command line pytest in CI
2. Include coverage requirements: `--cov-fail-under=75`
3. Run all test categories: unit, integration, and compatibility tests

## JSON Schema Testing

The JSON Schema implementation includes comprehensive tests:

### Unit Tests
- `tests/unit/schemas/test_json_schema.py`: Schema validation tests
- `tests/unit/cli/test_cmd_schema.py`: CLI command tests

### Integration Tests  
- `tests/integration/test_cli_inspect_integration.py`: Full CLI integration
- `tests/integration/test_vscode_compatibility.py`: VSCode-compatible versions

All JSON Schema tests pass in both CLI and VSCode environments.
