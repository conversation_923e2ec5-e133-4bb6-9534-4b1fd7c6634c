(venv) keh@kmachine:~/plugginger/examples/ai-chat-reference$ nano .env
(venv) keh@kmachine:~/plugginger/examples/ai-chat-reference$ python app.py
🔧 Creating AI-Chat Reference App...
2025-06-02 15:02:40,948 - plugginger.builder - INFO - PluggingerAppBuilder initialized for app 'ai_chat_reference' (depth: 0, max_depth_initial: 10).
2025-06-02 15:02:40,948 - plugginger.builder - INFO - PluggingerAppBuilder initialized for app 'ai_chat_reference' (depth: 0, max_depth_initial: 10).
2025-06-02 15:02:40,949 - plugginger.builder - INFO - Manifest loading enabled (require_manifests=False)
2025-06-02 15:02:40,949 - plugginger.builder - INFO - Manifest loading enabled (require_manifests=False)
2025-06-02 15:02:40,967 - plugginger.builder - ERROR - Invalid manifest file for plugin 'MemoryStorePlugin' at /home/<USER>/plugginger/examples/ai-chat-reference/plugins/memory_store/manifest.yaml: Invalid manifest data: 68 validation errors for PluginManifest
metadata.tags
  Extra inputs are not permitted [type=extra_forbidden, input_value=['storage', 'memory', 'chat', 'session'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.method_name
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.signature
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.kind
  Field required [type=missing, input_value={'name': 'title', 'type':...e for the conversation'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional title for the conversation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'str', 'descript...unique conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.method_name
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.signature
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...o store the message in'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation to store the message in', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.kind
  Field required [type=missing, input_value={'name': 'role', 'type': ...er, assistant, system)'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Message role (user, assistant, system)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.kind
  Field required [type=missing, input_value={'name': 'content', 'type...: 'The message content'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The message content', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.kind
  Field required [type=missing, input_value={'name': 'metadata', 'typ...tadata for the message'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.3.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='dict[str, Any] | None', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional metadata for the message', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'None'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...rsation ID to retrieve'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to retrieve', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.kind
  Field required [type=missing, input_value={'name': 'limit', 'type':... of messages to return'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='int', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Maximum number of messages to return', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.kind
  Field required [type=missing, input_value={'name': 'include_metadat...clude message metadata'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='bool', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Whether to include message metadata', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...in chronological order'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.3.method_name
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.signature
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...conversation summaries'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...: 'The conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...etadata and statistics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.method_name
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.signature
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...versation ID to delete'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to delete', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'bool', 'descrip...ed, False if not found'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.6.method_name
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.signature
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...statistics and metrics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.patterns
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.method_name
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.signature
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.event_pattern
  Extra inputs are not permitted [type=extra_forbidden, input_value='chat.response_generated', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.handler
  Extra inputs are not permitted [type=extra_forbidden, input_value='handle_chat_response', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
events_emitted
  Extra inputs are not permitted [type=extra_forbidden, input_value=[{'event_name': 'memory.c..., 'deleted_at': 'str'}}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-06-02 15:02:40,967 - plugginger.builder - ERROR - Invalid manifest file for plugin 'MemoryStorePlugin' at /home/<USER>/plugginger/examples/ai-chat-reference/plugins/memory_store/manifest.yaml: Invalid manifest data: 68 validation errors for PluginManifest
metadata.tags
  Extra inputs are not permitted [type=extra_forbidden, input_value=['storage', 'memory', 'chat', 'session'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.method_name
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.signature
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.kind
  Field required [type=missing, input_value={'name': 'title', 'type':...e for the conversation'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional title for the conversation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'str', 'descript...unique conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.method_name
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.signature
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...o store the message in'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation to store the message in', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.kind
  Field required [type=missing, input_value={'name': 'role', 'type': ...er, assistant, system)'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Message role (user, assistant, system)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.kind
  Field required [type=missing, input_value={'name': 'content', 'type...: 'The message content'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The message content', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.kind
  Field required [type=missing, input_value={'name': 'metadata', 'typ...tadata for the message'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.3.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='dict[str, Any] | None', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional metadata for the message', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'None'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...rsation ID to retrieve'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to retrieve', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.kind
  Field required [type=missing, input_value={'name': 'limit', 'type':... of messages to return'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='int', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Maximum number of messages to return', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.kind
  Field required [type=missing, input_value={'name': 'include_metadat...clude message metadata'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='bool', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Whether to include message metadata', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...in chronological order'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.3.method_name
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.signature
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...conversation summaries'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...: 'The conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...etadata and statistics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.method_name
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.signature
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...versation ID to delete'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to delete', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'bool', 'descrip...ed, False if not found'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.6.method_name
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.signature
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...statistics and metrics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.patterns
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.method_name
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.signature
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.event_pattern
  Extra inputs are not permitted [type=extra_forbidden, input_value='chat.response_generated', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.handler
  Extra inputs are not permitted [type=extra_forbidden, input_value='handle_chat_response', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
events_emitted
  Extra inputs are not permitted [type=extra_forbidden, input_value=[{'event_name': 'memory.c..., 'deleted_at': 'str'}}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-06-02 15:02:40,967 - plugginger.builder - ERROR - Failed to load manifest for memory_store: Invalid manifest file for plugin 'MemoryStorePlugin' at /home/<USER>/plugginger/examples/ai-chat-reference/plugins/memory_store/manifest.yaml: Invalid manifest data: 68 validation errors for PluginManifest
metadata.tags
  Extra inputs are not permitted [type=extra_forbidden, input_value=['storage', 'memory', 'chat', 'session'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.method_name
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.signature
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.kind
  Field required [type=missing, input_value={'name': 'title', 'type':...e for the conversation'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional title for the conversation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'str', 'descript...unique conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.method_name
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.signature
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...o store the message in'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation to store the message in', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.kind
  Field required [type=missing, input_value={'name': 'role', 'type': ...er, assistant, system)'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Message role (user, assistant, system)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.kind
  Field required [type=missing, input_value={'name': 'content', 'type...: 'The message content'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The message content', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.kind
  Field required [type=missing, input_value={'name': 'metadata', 'typ...tadata for the message'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.3.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='dict[str, Any] | None', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional metadata for the message', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'None'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...rsation ID to retrieve'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to retrieve', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.kind
  Field required [type=missing, input_value={'name': 'limit', 'type':... of messages to return'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='int', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Maximum number of messages to return', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.kind
  Field required [type=missing, input_value={'name': 'include_metadat...clude message metadata'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='bool', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Whether to include message metadata', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...in chronological order'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.3.method_name
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.signature
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...conversation summaries'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...: 'The conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...etadata and statistics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.method_name
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.signature
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...versation ID to delete'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to delete', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'bool', 'descrip...ed, False if not found'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.6.method_name
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.signature
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...statistics and metrics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.patterns
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.method_name
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.signature
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.event_pattern
  Extra inputs are not permitted [type=extra_forbidden, input_value='chat.response_generated', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.handler
  Extra inputs are not permitted [type=extra_forbidden, input_value='handle_chat_response', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
events_emitted
  Extra inputs are not permitted [type=extra_forbidden, input_value=[{'event_name': 'memory.c..., 'deleted_at': 'str'}}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-06-02 15:02:40,967 - plugginger.builder - ERROR - Failed to load manifest for memory_store: Invalid manifest file for plugin 'MemoryStorePlugin' at /home/<USER>/plugginger/examples/ai-chat-reference/plugins/memory_store/manifest.yaml: Invalid manifest data: 68 validation errors for PluginManifest
metadata.tags
  Extra inputs are not permitted [type=extra_forbidden, input_value=['storage', 'memory', 'chat', 'session'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.method_name
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.signature
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.kind
  Field required [type=missing, input_value={'name': 'title', 'type':...e for the conversation'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional title for the conversation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'str', 'descript...unique conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.method_name
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.signature
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...o store the message in'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation to store the message in', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.kind
  Field required [type=missing, input_value={'name': 'role', 'type': ...er, assistant, system)'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Message role (user, assistant, system)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.kind
  Field required [type=missing, input_value={'name': 'content', 'type...: 'The message content'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The message content', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.kind
  Field required [type=missing, input_value={'name': 'metadata', 'typ...tadata for the message'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.3.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='dict[str, Any] | None', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional metadata for the message', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'None'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...rsation ID to retrieve'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to retrieve', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.kind
  Field required [type=missing, input_value={'name': 'limit', 'type':... of messages to return'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='int', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Maximum number of messages to return', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.kind
  Field required [type=missing, input_value={'name': 'include_metadat...clude message metadata'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='bool', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Whether to include message metadata', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...in chronological order'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.3.method_name
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.signature
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...conversation summaries'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...: 'The conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...etadata and statistics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.method_name
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.signature
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...versation ID to delete'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to delete', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'bool', 'descrip...ed, False if not found'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.6.method_name
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.signature
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...statistics and metrics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.patterns
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.method_name
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.signature
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.event_pattern
  Extra inputs are not permitted [type=extra_forbidden, input_value='chat.response_generated', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.handler
  Extra inputs are not permitted [type=extra_forbidden, input_value='handle_chat_response', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
events_emitted
  Extra inputs are not permitted [type=extra_forbidden, input_value=[{'event_name': 'memory.c..., 'deleted_at': 'str'}}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
Traceback (most recent call last):
  File "/home/<USER>/plugginger/src/plugginger/schemas/generator.py", line 343, in manifest_from_yaml
    return manifest_type.model_validate(data)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/plugginger/examples/ai-chat-reference/venv/lib64/python3.13/site-packages/pydantic/main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 68 validation errors for PluginManifest
metadata.tags
  Extra inputs are not permitted [type=extra_forbidden, input_value=['storage', 'memory', 'chat', 'session'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.method_name
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.signature
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.kind
  Field required [type=missing, input_value={'name': 'title', 'type':...e for the conversation'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional title for the conversation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'str', 'descript...unique conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.method_name
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.signature
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...o store the message in'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation to store the message in', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.kind
  Field required [type=missing, input_value={'name': 'role', 'type': ...er, assistant, system)'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Message role (user, assistant, system)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.kind
  Field required [type=missing, input_value={'name': 'content', 'type...: 'The message content'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The message content', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.kind
  Field required [type=missing, input_value={'name': 'metadata', 'typ...tadata for the message'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.3.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='dict[str, Any] | None', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional metadata for the message', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'None'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...rsation ID to retrieve'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to retrieve', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.kind
  Field required [type=missing, input_value={'name': 'limit', 'type':... of messages to return'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='int', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Maximum number of messages to return', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.kind
  Field required [type=missing, input_value={'name': 'include_metadat...clude message metadata'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='bool', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Whether to include message metadata', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...in chronological order'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.3.method_name
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.signature
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...conversation summaries'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...: 'The conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...etadata and statistics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.method_name
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.signature
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...versation ID to delete'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to delete', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'bool', 'descrip...ed, False if not found'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.6.method_name
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.signature
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...statistics and metrics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.patterns
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.method_name
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.signature
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.event_pattern
  Extra inputs are not permitted [type=extra_forbidden, input_value='chat.response_generated', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.handler
  Extra inputs are not permitted [type=extra_forbidden, input_value='handle_chat_response', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
events_emitted
  Extra inputs are not permitted [type=extra_forbidden, input_value=[{'event_name': 'memory.c..., 'deleted_at': 'str'}}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/plugginger/src/plugginger/_internal/validation/manifest_loader.py", line 87, in load_plugin_manifest
    manifest = manifest_from_yaml(yaml_content, PluginManifest)
  File "/home/<USER>/plugginger/src/plugginger/schemas/generator.py", line 347, in manifest_from_yaml
    raise ValueError(f"Invalid manifest data: {e}") from e
ValueError: Invalid manifest data: 68 validation errors for PluginManifest
metadata.tags
  Extra inputs are not permitted [type=extra_forbidden, input_value=['storage', 'memory', 'chat', 'session'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.method_name
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.signature
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.kind
  Field required [type=missing, input_value={'name': 'title', 'type':...e for the conversation'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional title for the conversation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'str', 'descript...unique conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.method_name
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.signature
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...o store the message in'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation to store the message in', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.kind
  Field required [type=missing, input_value={'name': 'role', 'type': ...er, assistant, system)'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Message role (user, assistant, system)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.kind
  Field required [type=missing, input_value={'name': 'content', 'type...: 'The message content'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The message content', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.kind
  Field required [type=missing, input_value={'name': 'metadata', 'typ...tadata for the message'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.3.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='dict[str, Any] | None', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional metadata for the message', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'None'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...rsation ID to retrieve'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to retrieve', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.kind
  Field required [type=missing, input_value={'name': 'limit', 'type':... of messages to return'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='int', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Maximum number of messages to return', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.kind
  Field required [type=missing, input_value={'name': 'include_metadat...clude message metadata'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='bool', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Whether to include message metadata', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...in chronological order'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.3.method_name
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.signature
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...conversation summaries'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...: 'The conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...etadata and statistics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.method_name
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.signature
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...versation ID to delete'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to delete', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'bool', 'descrip...ed, False if not found'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.6.method_name
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.signature
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...statistics and metrics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.patterns
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.method_name
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.signature
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.event_pattern
  Extra inputs are not permitted [type=extra_forbidden, input_value='chat.response_generated', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.handler
  Extra inputs are not permitted [type=extra_forbidden, input_value='handle_chat_response', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
events_emitted
  Extra inputs are not permitted [type=extra_forbidden, input_value=[{'event_name': 'memory.c..., 'deleted_at': 'str'}}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/plugginger/examples/ai-chat-reference/app.py", line 180, in <module>
    asyncio.run(main())
    ~~~~~~~~~~~^^^^^^^^
  File "/usr/lib64/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/usr/lib64/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/usr/lib64/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/plugginger/examples/ai-chat-reference/app.py", line 154, in main
    app = create_app(enable_manifests=enable_manifests)
  File "/home/<USER>/plugginger/examples/ai-chat-reference/app.py", line 52, in create_app
    builder.include(MemoryStorePlugin)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/plugginger/src/plugginger/api/builder.py", line 293, in include
    self._register_item_class(registration_name, plugin_class, is_app_plugin_type=False)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/plugginger/src/plugginger/api/builder.py", line 257, in _register_item_class
    manifest = self._manifest_loader.load_plugin_manifest(
        item_class,
        manifest_path=None,  # Auto-discover
        require_manifest=self._require_manifests
    )
  File "/home/<USER>/plugginger/src/plugginger/_internal/validation/manifest_loader.py", line 108, in load_plugin_manifest
    raise PluginRegistrationError(error_msg) from e
plugginger.core.exceptions.PluginRegistrationError: Invalid manifest file for plugin 'MemoryStorePlugin' at /home/<USER>/plugginger/examples/ai-chat-reference/plugins/memory_store/manifest.yaml: Invalid manifest data: 68 validation errors for PluginManifest
metadata.tags
  Extra inputs are not permitted [type=extra_forbidden, input_value=['storage', 'memory', 'chat', 'session'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.method_name
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.signature
  Field required [type=missing, input_value={'name': 'create_conversa...nique conversation ID'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.kind
  Field required [type=missing, input_value={'name': 'title', 'type':...e for the conversation'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.0.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional title for the conversation', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.0.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'str', 'descript...unique conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.method_name
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.signature
  Field required [type=missing, input_value={'name': 'store_message',...urns': {'type': 'None'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...o store the message in'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation to store the message in', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.kind
  Field required [type=missing, input_value={'name': 'role', 'type': ...er, assistant, system)'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Message role (user, assistant, system)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.kind
  Field required [type=missing, input_value={'name': 'content', 'type...: 'The message content'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The message content', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.kind
  Field required [type=missing, input_value={'name': 'metadata', 'typ...tadata for the message'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.1.parameters.3.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='dict[str, Any] | None', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.parameters.3.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Optional metadata for the message', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.1.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'None'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...n chronological order'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...rsation ID to retrieve'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to retrieve', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.kind
  Field required [type=missing, input_value={'name': 'limit', 'type':... of messages to return'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.1.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='int', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.1.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Maximum number of messages to return', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.kind
  Field required [type=missing, input_value={'name': 'include_metadat...clude message metadata'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.2.parameters.2.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='bool', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=False, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.parameters.2.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='Whether to include message metadata', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.2.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...in chronological order'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.3.method_name
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.signature
  Field required [type=missing, input_value={'name': 'list_conversati...onversation summaries'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.3.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'list[dict[str, ...conversation summaries'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.method_name
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.signature
  Field required [type=missing, input_value={'name': 'get_conversatio...tadata and statistics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...: 'The conversation ID'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.4.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.4.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...etadata and statistics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.method_name
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.signature
  Field required [type=missing, input_value={'name': 'delete_conversa...d, False if not found'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.kind
  Field required [type=missing, input_value={'name': 'conversation_id...versation ID to delete'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.5.parameters.0.type
  Extra inputs are not permitted [type=extra_forbidden, input_value='str', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.required
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.parameters.0.description
  Extra inputs are not permitted [type=extra_forbidden, input_value='The conversation ID to delete', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.5.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'bool', 'descrip...ed, False if not found'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
services.6.method_name
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.signature
  Field required [type=missing, input_value={'name': 'get_storage_sta...tatistics and metrics'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
services.6.returns
  Extra inputs are not permitted [type=extra_forbidden, input_value={'type': 'dict[str, Any]'...statistics and metrics'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.patterns
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.method_name
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.signature
  Field required [type=missing, input_value={'event_pattern': 'chat.r...ponses', 'priority': 10}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
event_listeners.0.event_pattern
  Extra inputs are not permitted [type=extra_forbidden, input_value='chat.response_generated', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
event_listeners.0.handler
  Extra inputs are not permitted [type=extra_forbidden, input_value='handle_chat_response', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
events_emitted
  Extra inputs are not permitted [type=extra_forbidden, input_value=[{'event_name': 'memory.c..., 'deleted_at': 'str'}}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
(venv) keh@kmachine:~/plugginger/examples/ai-chat-reference$
