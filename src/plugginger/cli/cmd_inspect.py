# src/plugginger/cli/cmd_inspect.py

"""
Implementation of the 'plugginger inspect' command.

This module provides functionality to analyze and export Plugginger application
structure for AI agents and developers.
"""

import json
import logging
from datetime import UTC, datetime
from typing import Any

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.plugin import PluginBase
from plugginger.schemas.json.app_graph import validate_app_graph

logger = logging.getLogger(__name__)


class AppInspector:
    """
    Inspector for analyzing Plugginger application structure.

    This class provides functionality to extract detailed information about
    plugins, services, events, and dependencies for AI agents and developers.
    """

    def __init__(self, app: PluggingerAppBuilder | PluggingerAppInstance) -> None:
        """
        Initialize the app inspector.

        Args:
            app: Application builder or instance to inspect
        """
        self.app = app
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def analyze(self) -> dict[str, Any]:
        """
        Analyze the application and return structured information.

        Returns:
            Dictionary containing app structure information
        """
        self.logger.info("Starting application analysis")

        # Build app if we have a builder
        if isinstance(self.app, PluggingerAppBuilder):
            app_instance = self.app.build()
        else:
            app_instance = self.app

        # Extract information
        app_info = self._extract_app_info(app_instance)
        plugins_info = self._extract_plugins_info(app_instance)
        dependency_graph = self._extract_dependency_graph(app_instance)
        metadata = self._generate_metadata()

        result = {
            "app": app_info,
            "plugins": plugins_info,
            "dependency_graph": dependency_graph,
            "metadata": metadata
        }

        # Validate against schema
        is_valid, errors = validate_app_graph(result)
        if not is_valid:
            self.logger.warning(f"Generated app graph failed validation: {errors}")

        self.logger.info("Application analysis completed")
        return result

    def _extract_app_info(self, app: PluggingerAppInstance) -> dict[str, Any]:
        """Extract basic application information."""
        return {
            "name": app.app_name,
            "plugin_count": len(app._registered_plugin_classes),
            "max_fractal_depth": app._max_build_depth_for_sub_apps
        }

    def _extract_plugins_info(self, app: PluggingerAppInstance) -> list[dict[str, Any]]:
        """Extract information about all registered plugins."""
        plugins = []

        for reg_name, plugin_class in app._registered_plugin_classes.items():
            plugin_info = {
                "registration_name": reg_name,
                "class_name": plugin_class.__name__,
                "module": plugin_class.__module__,
                "services": self._extract_services(plugin_class),
                "event_listeners": self._extract_event_listeners(plugin_class),
                "dependencies": self._extract_dependencies(plugin_class),
                "metadata": self._extract_plugin_metadata(plugin_class)
            }
            plugins.append(plugin_info)

        return plugins

    def _extract_services(self, plugin_class: type[PluginBase]) -> list[dict[str, Any]]:
        """Extract service definitions from a plugin class."""
        services = []

        # Get services from plugin metadata
        if hasattr(plugin_class, '_plugginger_services'):
            for service_name, service_metadata in plugin_class._plugginger_services.items():
                method = getattr(plugin_class, service_name, None)
                if method:
                    service_info = {
                        "name": service_metadata.get("name", service_name),
                        "method_name": service_name,
                        "signature": self._extract_method_signature(method),
                        "metadata": service_metadata
                    }
                    services.append(service_info)
        else:
            # Fallback: scan for @service decorated methods
            for attr_name in dir(plugin_class):
                if attr_name.startswith('_'):
                    continue
                attr = getattr(plugin_class, attr_name)
                if hasattr(attr, '_plugginger_service_config'):
                    service_metadata = getattr(attr, '_plugginger_service_config', {})
                    # Convert metadata to JSON-serializable format
                    clean_metadata = self._clean_metadata_for_json(service_metadata)
                    service_info = {
                        "name": service_metadata.get("name", attr_name),
                        "method_name": attr_name,
                        "signature": self._extract_method_signature(attr),
                        "metadata": clean_metadata
                    }
                    services.append(service_info)

        return services

    def _extract_event_listeners(self, plugin_class: type[PluginBase]) -> list[dict[str, Any]]:
        """Extract event listener definitions from a plugin class."""
        listeners = []

        # Get event listeners from plugin metadata
        if hasattr(plugin_class, '_plugginger_event_listeners'):
            for listener_name, listener_metadata in plugin_class._plugginger_event_listeners.items():
                method = getattr(plugin_class, listener_name, None)
                if method:
                    listener_info = {
                        "method_name": listener_name,
                        "event_pattern": listener_metadata.get("event_pattern", ""),
                        "signature": self._extract_method_signature(method),
                        "metadata": listener_metadata
                    }
                    listeners.append(listener_info)
        else:
            # Fallback: scan for @on_event decorated methods
            for attr_name in dir(plugin_class):
                if attr_name.startswith('_'):
                    continue
                attr = getattr(plugin_class, attr_name)
                if hasattr(attr, '_plugginger_event_config'):
                    event_metadata = getattr(attr, '_plugginger_event_config', {})
                    patterns = event_metadata.get("patterns", [])
                    event_pattern = patterns[0] if patterns else ""
                    # Convert metadata to JSON-serializable format
                    clean_metadata = self._clean_metadata_for_json(event_metadata)
                    listener_info = {
                        "method_name": attr_name,
                        "event_pattern": event_pattern,
                        "signature": self._extract_method_signature(attr),
                        "metadata": clean_metadata
                    }
                    listeners.append(listener_info)

        return listeners

    def _extract_dependencies(self, plugin_class: type[PluginBase]) -> list[dict[str, Any]]:
        """Extract dependency declarations from a plugin class."""
        dependencies = []

        # Get dependencies from plugin metadata
        if hasattr(plugin_class, '_plugginger_needs'):
            for dep in plugin_class._plugginger_needs:
                dep_info = {
                    "name": str(dep.plugin_identifier),
                    "optional": dep.optional,
                    "version": getattr(dep, 'version_constraint', None)
                }
                dependencies.append(dep_info)
        elif hasattr(plugin_class, 'needs'):
            # Fallback: check for 'needs' class attribute
            for dep in plugin_class.needs:
                dep_info = {
                    "name": str(dep.plugin_identifier),
                    "optional": dep.optional,
                    "version": getattr(dep, 'version_constraint', None)
                }
                dependencies.append(dep_info)

        return dependencies

    def _extract_plugin_metadata(self, plugin_class: type[PluginBase]) -> dict[str, Any]:
        """Extract plugin metadata."""
        metadata = {}

        # Extract standard plugin metadata
        if hasattr(plugin_class, '_plugginger_plugin_name'):
            metadata["name"] = plugin_class._plugginger_plugin_name
        if hasattr(plugin_class, '_plugginger_plugin_version'):
            metadata["version"] = plugin_class._plugginger_plugin_version
        if hasattr(plugin_class, '_plugginger_plugin_description'):
            metadata["description"] = plugin_class._plugginger_plugin_description

        return metadata

    def _extract_dependency_graph(self, app: PluggingerAppInstance) -> dict[str, Any]:
        """Extract detailed dependency graph structure with cycle detection."""
        nodes = []
        edges = []

        # Create nodes for all plugins
        for reg_name, plugin_class in app._registered_plugin_classes.items():
            node = {
                "id": reg_name,
                "type": "plugin",
                "metadata": {
                    "class_name": plugin_class.__name__,
                    "module": plugin_class.__module__,
                    "plugin_name": getattr(plugin_class, '_plugginger_plugin_name', reg_name),
                    "plugin_version": getattr(plugin_class, '_plugginger_plugin_version', None)
                }
            }
            nodes.append(node)

        # Create edges for dependencies
        for reg_name, plugin_class in app._registered_plugin_classes.items():
            dependencies = self._extract_dependencies(plugin_class)
            for dep in dependencies:
                edge = {
                    "from": reg_name,
                    "to": dep["plugin_identifier"],
                    "type": "depends_on",
                    "optional": dep["optional"],
                    "version_constraint": dep["version_constraint"]
                }
                edges.append(edge)

        # Detect cycles (skip for now to avoid potential infinite loops)
        graph_info = {
            "nodes": nodes,
            "edges": edges
        }

        try:
            cycles = self._detect_dependency_cycles(nodes, edges)
            if cycles:
                graph_info["cycles"] = cycles
                self.logger.warning(f"Detected {len(cycles)} dependency cycles")
        except Exception as e:
            self.logger.warning(f"Cycle detection failed: {e}")

        return graph_info

    def _detect_dependency_cycles(self, nodes: list[dict[str, Any]], edges: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """Detect circular dependencies in the graph."""
        # Build adjacency list
        graph: dict[str, list[str]] = {}
        for node in nodes:
            graph[node["id"]] = []

        for edge in edges:
            if not edge["optional"]:  # Only consider required dependencies for cycle detection
                graph[edge["from"]].append(edge["to"])

        # DFS-based cycle detection
        visited: set[str] = set()
        rec_stack: set[str] = set()
        cycles: list[list[str]] = []

        def dfs(node: str, path: list[str]) -> None:
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return

            if node in visited:
                return

            visited.add(node)
            rec_stack.add(node)
            path.append(node)

            for neighbor in graph.get(node, []):
                if neighbor in graph:  # Only follow edges to existing nodes
                    dfs(neighbor, path.copy())

            rec_stack.remove(node)

        # Check each node
        for node_id in graph:
            if node_id not in visited:
                dfs(node_id, [])

        # Convert cycles to dict format for JSON schema
        cycle_dicts = []
        for cycle in cycles:
            cycle_dict = {
                "nodes": cycle,
                "type": "circular_dependency"
            }
            cycle_dicts.append(cycle_dict)

        return cycle_dicts

    def _clean_metadata_for_json(self, metadata: dict[str, Any]) -> dict[str, Any]:
        """Clean metadata to make it JSON-serializable."""
        clean_metadata: dict[str, Any] = {}
        for key, value in metadata.items():
            # Skip None values to avoid JSON schema validation issues
            if value is None:
                continue
            elif isinstance(value, str | int | float | bool):
                clean_metadata[key] = value
            elif isinstance(value, list | tuple):
                clean_metadata[key] = [str(item) for item in value]
            elif isinstance(value, dict):
                clean_metadata[key] = self._clean_metadata_for_json(value)
            else:
                clean_metadata[key] = str(value)
        return clean_metadata

    def _extract_method_signature(self, method: Any) -> dict[str, Any]:
        """Extract method signature information including docstring."""
        import inspect

        try:
            sig = inspect.signature(method)

            parameters = []
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue

                # Ensure all values are JSON-serializable strings
                param_type = None
                if param.annotation != inspect.Parameter.empty:
                    param_type = str(param.annotation)

                param_default = None
                if param.default != inspect.Parameter.empty:
                    param_default = str(param.default)

                param_info = {
                    "name": param_name,
                    "type": param_type,
                    "default": param_default,
                    "kind": param.kind.name
                }
                parameters.append(param_info)

            # Extract docstring information
            docstring_info = self._extract_docstring_info(method)

            return {
                "parameters": parameters,
                "return_type": str(sig.return_annotation) if sig.return_annotation != inspect.Signature.empty else None,
                "docstring": docstring_info
            }

        except Exception as e:
            self.logger.warning(f"Could not extract signature for {method}: {e}")
            return {"parameters": [], "return_type": None, "docstring": None}

    def _extract_docstring_info(self, method: Any) -> dict[str, Any] | None:
        """Extract and parse docstring information from a method."""
        import inspect

        try:
            docstring = inspect.getdoc(method)
            if not docstring:
                return None

            # Split docstring into summary and description
            lines = docstring.strip().split('\n')
            if not lines:
                return None

            # First non-empty line is the summary
            summary = lines[0].strip()

            # Rest is description (skip empty lines after summary)
            description_lines = []
            in_description = False

            for line in lines[1:]:
                stripped = line.strip()
                if not stripped and not in_description:
                    continue  # Skip empty lines before description
                in_description = True
                description_lines.append(line.rstrip())

            description = '\n'.join(description_lines).strip() if description_lines else None

            return {
                "summary": summary,
                "description": description,
                "raw": docstring
            }

        except Exception as e:
            self.logger.warning(f"Could not extract docstring for {method}: {e}")
            return None

    def _generate_metadata(self) -> dict[str, Any]:
        """Generate metadata about the analysis."""
        return {
            "generated_at": datetime.now(UTC).isoformat(),
            "generated_by": "plugginger-inspect",
            "schema_version": "1.0.0"
        }


def cmd_inspect(factory_path: str, output_format: str = "json", output_file: str | None = None) -> None:
    """
    Execute the inspect command.

    Args:
        factory_path: Path to app factory function (module:function)
        output_format: Output format (json)
        output_file: Optional output file path
    """
    from plugginger.cli.utils import resolve_app_factory

    logger.info(f"Inspecting application from factory: {factory_path}")

    try:
        # Load the app builder
        app_builder = resolve_app_factory(factory_path)

        # Analyze the application
        inspector = AppInspector(app_builder)
        result = inspector.analyze()

        # Output the result
        if output_format == "json":
            output = json.dumps(result, indent=2)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(output)
            print(f"Analysis written to {output_file}")
        else:
            print(output)

    except Exception as e:
        logger.error(f"Failed to inspect application: {e}")
        raise
