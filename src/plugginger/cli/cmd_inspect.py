# src/plugginger/cli/cmd_inspect.py

"""
Implementation of the 'plugginger inspect' command.

This module provides functionality to inspect and analyze Plugginger applications,
generating machine-readable JSON output for KI-agents and external tools.
"""

import json
import logging
from pathlib import Path
from typing import Any

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.cli.utils import resolve_app_factory
from plugginger.core.constants import EVENT_METADATA_KEY, SERVICE_METADATA_KEY
from plugginger.core.exceptions import PluginRegistrationError

logger = logging.getLogger(__name__)


def _json_serializer(obj: Any) -> str:
    """
    JSON serializer for non-serializable objects.

    Args:
        obj: Object to serialize

    Returns:
        String representation of the object
    """
    return str(obj)


def cmd_inspect(factory_path: str, output_json: bool, output_file: Path | None) -> None:
    """
    Execute the inspect command.

    Args:
        factory_path: Path to app factory function (module:function)
        output_json: Whether to output JSON format
        output_file: Optional output file path
    """
    logger.info(f"Inspecting app factory: {factory_path}")

    try:
        # Load the app builder
        app_builder = resolve_app_factory(factory_path)
        logger.info(f"Loaded app builder for: {app_builder._app_name}")

        # Create inspector and analyze the app
        inspector = AppInspector(app_builder)
        analysis_result = inspector.analyze()

        if output_json:
            # Output JSON format with safe serialization
            json_output = json.dumps(analysis_result, indent=2, ensure_ascii=False, default=_json_serializer)
            
            if output_file:
                output_file.write_text(json_output, encoding='utf-8')
                print(f"✓ Inspection results written to {output_file}")
            else:
                print(json_output)
        else:
            # Output human-readable format
            _print_human_readable_analysis(analysis_result)

    except Exception as e:
        logger.error(f"Failed to inspect app: {e}")
        raise


class AppInspector:
    """
    Inspector for analyzing Plugginger applications.
    
    This class provides functionality to extract metadata, services, events,
    and dependencies from a configured PluggingerAppBuilder without starting the app.
    """

    def __init__(self, app_builder: PluggingerAppBuilder) -> None:
        """
        Initialize the inspector.

        Args:
            app_builder: Configured app builder to inspect
        """
        self.app_builder = app_builder
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def analyze(self) -> dict[str, Any]:
        """
        Analyze the app and return structured data.

        Returns:
            Dictionary containing app analysis results

        Raises:
            PluginRegistrationError: If app analysis fails
        """
        try:
            self.logger.debug("Starting app analysis")

            # Extract basic app information
            app_info = self._extract_app_info()
            
            # Extract plugin information
            plugins_info = self._extract_plugins_info()
            
            # Extract dependency graph
            dependency_graph = self._extract_dependency_graph()

            # Build final result
            result = {
                "app": app_info,
                "plugins": plugins_info,
                "dependency_graph": dependency_graph,
                "metadata": {
                    "generated_at": self._get_current_timestamp(),
                    "generated_by": "plugginger inspect",
                    "schema_version": "1.0.0"
                }
            }

            self.logger.info(f"Analysis complete: {len(plugins_info)} plugins found")
            return result

        except Exception as e:
            self.logger.error(f"App analysis failed: {e}")
            raise PluginRegistrationError(f"Failed to analyze app: {e}") from e

    def _extract_app_info(self) -> dict[str, Any]:
        """Extract basic app information."""
        return {
            "name": self.app_builder._app_name,
            "max_fractal_depth": getattr(self.app_builder, '_max_fractal_depth', None),
            "plugin_count": len(self.app_builder.get_registered_plugin_classes())
        }

    def _extract_plugins_info(self) -> list[dict[str, Any]]:
        """Extract information about all registered plugins."""
        plugins_info = []
        
        # Get registered plugin classes
        plugin_classes = self.app_builder.get_registered_plugin_classes()
        
        for registration_name, plugin_class in plugin_classes.items():
            plugin_info = self._analyze_plugin(registration_name, plugin_class)
            plugins_info.append(plugin_info)
        
        return plugins_info

    def _analyze_plugin(self, registration_name: str, plugin_class: type) -> dict[str, Any]:
        """
        Analyze a single plugin class.

        Args:
            registration_name: Name used for plugin registration
            plugin_class: Plugin class to analyze

        Returns:
            Dictionary containing plugin analysis
        """
        plugin_info = {
            "registration_name": registration_name,
            "class_name": plugin_class.__name__,
            "module": plugin_class.__module__,
            "services": self._extract_services(plugin_class),
            "event_listeners": self._extract_event_listeners(plugin_class),
            "dependencies": self._extract_dependencies(registration_name),
            "metadata": self._extract_plugin_metadata(plugin_class)
        }
        
        return plugin_info

    def _extract_services(self, plugin_class: type) -> list[dict[str, Any]]:
        """Extract service definitions from plugin class."""
        services = []

        # Look for methods decorated with @service
        for attr_name in dir(plugin_class):
            if attr_name.startswith('_'):
                continue

            attr = getattr(plugin_class, attr_name)
            if hasattr(attr, SERVICE_METADATA_KEY):
                metadata = getattr(attr, SERVICE_METADATA_KEY)
                service_info = {
                    "name": metadata.get("name", attr_name),
                    "method_name": attr_name,
                    "signature": self._extract_method_signature(attr),
                    "metadata": metadata
                }
                services.append(service_info)

        return services

    def _extract_event_listeners(self, plugin_class: type) -> list[dict[str, Any]]:
        """Extract event listener definitions from plugin class."""
        listeners = []

        # Look for methods decorated with @on_event
        for attr_name in dir(plugin_class):
            if attr_name.startswith('_'):
                continue

            attr = getattr(plugin_class, attr_name)
            if hasattr(attr, EVENT_METADATA_KEY):
                metadata = getattr(attr, EVENT_METADATA_KEY)
                # Get first pattern for backward compatibility
                patterns = metadata.get('patterns', [])
                first_pattern = patterns[0] if patterns else None

                listener_info = {
                    "method_name": attr_name,
                    "event_pattern": first_pattern,
                    "patterns": patterns,
                    "signature": self._extract_method_signature(attr),
                    "metadata": metadata
                }
                listeners.append(listener_info)

        return listeners

    def _extract_dependencies(self, registration_name: str) -> list[dict[str, Any]]:
        """Extract dependency declarations for a plugin."""
        dependencies = []

        # Get dependencies from builder's internal storage
        if hasattr(self.app_builder, '_plugin_dependency_declarations'):
            plugin_deps = self.app_builder._plugin_dependency_declarations.get(registration_name, [])

            for dep in plugin_deps:
                # Extract name from Depends object
                if hasattr(dep, 'dependency'):
                    dep_name = dep.dependency
                else:
                    dep_name = str(dep)

                dep_info = {
                    "name": dep_name,
                    "optional": getattr(dep, 'optional', False),
                    "version": getattr(dep, 'version_constraint', None)
                }
                dependencies.append(dep_info)

        return dependencies

    def _extract_plugin_metadata(self, plugin_class: type) -> dict[str, Any]:
        """Extract metadata from plugin class decorators."""
        metadata = {}
        
        # Extract @plugin decorator metadata
        if hasattr(plugin_class, '_plugginger_plugin_metadata'):
            metadata.update(plugin_class._plugginger_plugin_metadata)
        
        return metadata

    def _extract_method_signature(self, method: Any) -> dict[str, Any]:
        """
        Extract method signature information.

        Args:
            method: Method to analyze

        Returns:
            Dictionary containing signature information
        """
        import inspect
        
        try:
            sig = inspect.signature(method)
            
            parameters = []
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                    
                # Ensure all values are JSON-serializable strings
                param_type = None
                if param.annotation != inspect.Parameter.empty:
                    param_type = str(param.annotation)

                param_default = None
                if param.default != inspect.Parameter.empty:
                    param_default = str(param.default)

                param_info = {
                    "name": param_name,
                    "type": param_type,
                    "default": param_default,
                    "kind": param.kind.name
                }
                parameters.append(param_info)
            
            return {
                "parameters": parameters,
                "return_type": str(sig.return_annotation) if sig.return_annotation != inspect.Signature.empty else None
            }
            
        except Exception as e:
            self.logger.warning(f"Could not extract signature for {method}: {e}")
            return {"parameters": [], "return_type": None}

    def _extract_dependency_graph(self) -> dict[str, Any]:
        """Extract dependency relationships between plugins."""
        graph: dict[str, Any] = {
            "nodes": [],
            "edges": []
        }
        
        # Get all registered plugins as nodes
        plugin_classes = self.app_builder.get_registered_plugin_classes()
        for registration_name in plugin_classes.keys():
            graph["nodes"].append({
                "id": registration_name,
                "type": "plugin"
            })
        
        # Extract dependency edges
        if hasattr(self.app_builder, '_plugin_dependency_declarations'):
            for plugin_name, dependencies in self.app_builder._plugin_dependency_declarations.items():
                for dep in dependencies:
                    # Extract name from Depends object
                    if hasattr(dep, 'dependency'):
                        dep_name = dep.dependency
                    else:
                        dep_name = str(dep)

                    graph["edges"].append({
                        "from": plugin_name,
                        "to": dep_name,
                        "type": "depends_on",
                        "optional": getattr(dep, 'optional', False)
                    })
        
        return graph

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime, timezone
        return datetime.now(timezone.utc).isoformat()


def _print_human_readable_analysis(analysis: dict[str, Any]) -> None:
    """
    Print analysis results in human-readable format.

    Args:
        analysis: Analysis results dictionary
    """
    app_info = analysis["app"]
    plugins = analysis["plugins"]
    
    print(f"\n📱 App: {app_info['name']}")
    print(f"   Plugins: {app_info['plugin_count']}")
    if app_info.get('max_fractal_depth'):
        print(f"   Max Fractal Depth: {app_info['max_fractal_depth']}")
    
    print(f"\n🔌 Plugins ({len(plugins)}):")
    for plugin in plugins:
        print(f"   • {plugin['registration_name']} ({plugin['class_name']})")
        
        if plugin['services']:
            print(f"     Services: {', '.join(s['name'] for s in plugin['services'])}")
        
        if plugin['event_listeners']:
            events = [f"{l['method_name']}({l['event_pattern']})" for l in plugin['event_listeners']]
            print(f"     Events: {', '.join(events)}")
        
        if plugin['dependencies']:
            deps = [d['name'] for d in plugin['dependencies']]
            print(f"     Depends: {', '.join(deps)}")
    
    # Print dependency graph summary
    graph = analysis["dependency_graph"]
    if graph["edges"]:
        print(f"\n🔗 Dependencies ({len(graph['edges'])}):")
        for edge in graph["edges"]:
            optional_marker = " (optional)" if edge.get("optional") else ""
            print(f"   {edge['from']} → {edge['to']}{optional_marker}")
