"""
Manifest generation utilities.

This module provides functions to generate plugin and application manifests
from existing plugin classes and application instances.
"""

from __future__ import annotations

import inspect
from datetime import datetime
from typing import Any

from pydantic import BaseModel

from plugginger.api.events import (
    get_event_listener_metadata,
    is_event_listener_method,
)
from plugginger.api.plugin import get_plugin_metadata, is_plugin_class
from plugginger.api.service import (
    get_service_metadata,
    is_service_method,
)
from plugginger.schemas.manifest import (
    AppManifest,
    DependencyInfo,
    EventListenerInfo,
    ExecutionMode,
    ParameterInfo,
    ParameterKind,
    PluginManifest,
    PluginMetadata,
    PluginRuntime,
    ServiceInfo,
)


def _extract_parameter_info(param: inspect.Parameter) -> ParameterInfo:
    """Extract parameter information from inspect.Parameter."""
    return ParameterInfo(
        name=param.name,
        annotation=str(param.annotation) if param.annotation != inspect.Parameter.empty else None,
        default=param.default if param.default != inspect.Parameter.empty else None,
        kind=ParameterKind(param.kind.name),
    )


def _extract_service_info(plugin_class: type[Any], method_name: str) -> ServiceInfo:
    """Extract service information from a plugin method."""
    method = getattr(plugin_class, method_name)
    metadata = get_service_metadata(method)
    sig = inspect.signature(method)

    # Extract parameters (skip 'self')
    parameters = [
        _extract_parameter_info(param)
        for param in list(sig.parameters.values())[1:]
    ]

    return ServiceInfo(
        name=metadata["name"],
        method_name=metadata["method_name"],
        description=metadata.get("description"),
        timeout_seconds=metadata.get("timeout_seconds"),
        signature=metadata["signature"],
        parameters=parameters,
        return_annotation=str(sig.return_annotation) if sig.return_annotation != inspect.Signature.empty else None,
    )


def _extract_event_listener_info(plugin_class: type[Any], method_name: str) -> EventListenerInfo:
    """Extract event listener information from a plugin method."""
    method = getattr(plugin_class, method_name)
    metadata = get_event_listener_metadata(method)
    sig = inspect.signature(method)

    # Extract parameters (skip 'self')
    parameters = [
        _extract_parameter_info(param)
        for param in list(sig.parameters.values())[1:]
    ]

    return EventListenerInfo(
        patterns=metadata["patterns"],
        method_name=metadata["method_name"],
        description=metadata.get("description"),
        timeout_seconds=metadata.get("timeout_seconds"),
        priority=metadata.get("priority", 0),
        signature=metadata["signature"],
        parameters=parameters,
    )


def _extract_dependency_info(plugin_class: type[Any]) -> list[DependencyInfo]:
    """Extract dependency information from plugin needs."""
    dependencies = []

    # Get the needs attribute from the class
    needs = getattr(plugin_class, "needs", [])

    for dep in needs:
        # Handle both Depends objects and simple strings
        if hasattr(dep, "key"):
            # This is a Depends object
            dependencies.append(DependencyInfo(
                name=dep.key,
                version=getattr(dep, "version", None),
                optional=getattr(dep, "optional", False),
                description=getattr(dep, "description", None),
            ))
        elif hasattr(dep, "__str__") and "Depends(" in str(dep):
            # This is a Depends object represented as string
            dep_str = str(dep)
            # Extract name from "Depends('name', optional=False)" format
            import re
            match = re.search(r"Depends\('([^']+)'(?:, optional=([^)]+))?\)", dep_str)
            if match:
                name = match.group(1)
                optional_str = match.group(2)
                optional = optional_str == "True" if optional_str else False
                dependencies.append(DependencyInfo(
                    name=name,
                    optional=optional,
                ))
        else:
            # This is a simple string
            dependencies.append(DependencyInfo(
                name=str(dep),
                optional=False,
            ))

    return dependencies


def _extract_config_schema(plugin_class: type[Any]) -> dict[str, Any] | None:
    """Extract configuration schema from plugin class."""
    config_schema = getattr(plugin_class, "_plugginger_config_schema", None)

    if config_schema and issubclass(config_schema, BaseModel):
        # Convert Pydantic model to JSON Schema
        schema: dict[str, Any] = config_schema.model_json_schema()
        return schema

    return None


def generate_plugin_manifest(
    plugin_class: type[Any],
    *,
    author: str | None = None,
    homepage: str | None = None,
    repository: str | None = None,
    license: str | None = None,
    keywords: list[str] | None = None,
    description: str | None = None,
    execution_mode: ExecutionMode = ExecutionMode.THREAD,
    python_version: str | None = None,
    plugginger_version: str = ">=0.9.0",
) -> PluginManifest:
    """
    Generate a plugin manifest from a plugin class.

    Args:
        plugin_class: The plugin class to generate manifest for
        author: Plugin author (optional)
        homepage: Plugin homepage URL (optional)
        repository: Plugin repository URL (optional)
        license: Plugin license (optional)
        keywords: Plugin keywords (optional)
        description: Plugin description (optional, uses class docstring if not provided)
        execution_mode: Plugin execution mode (default: thread)
        python_version: Required Python version (optional)
        plugginger_version: Required Plugginger version (default: >=0.9.0)

    Returns:
        PluginManifest object

    Raises:
        ValueError: If plugin_class is not a valid plugin class
    """
    if not is_plugin_class(plugin_class):
        raise ValueError(f"Class {plugin_class.__name__} is not a valid plugin class")

    # Get plugin metadata
    plugin_meta = get_plugin_metadata(plugin_class)

    # Use class docstring as description if not provided
    if description is None:
        description = inspect.getdoc(plugin_class)

    # Create plugin metadata
    metadata = PluginMetadata(
        name=plugin_meta["name"],
        version=plugin_meta["version"],
        description=description,
        author=author,
        homepage=homepage,
        repository=repository,
        license=license,
        keywords=keywords or [],
    )

    # Create runtime configuration
    runtime = PluginRuntime(
        execution_mode=execution_mode,
        python_version=python_version,
        plugginger_version=plugginger_version,
    )

    # Extract dependencies
    dependencies = _extract_dependency_info(plugin_class)

    # Extract services
    services = []
    for attr_name in dir(plugin_class):
        if not attr_name.startswith("_"):
            attr = getattr(plugin_class, attr_name)
            if is_service_method(attr):
                services.append(_extract_service_info(plugin_class, attr_name))

    # Extract event listeners
    event_listeners = []
    for attr_name in dir(plugin_class):
        if not attr_name.startswith("_"):
            attr = getattr(plugin_class, attr_name)
            if is_event_listener_method(attr):
                event_listeners.append(_extract_event_listener_info(plugin_class, attr_name))

    # Extract configuration schema
    config_schema = _extract_config_schema(plugin_class)

    return PluginManifest(
        metadata=metadata,
        runtime=runtime,
        dependencies=dependencies,
        services=services,
        event_listeners=event_listeners,
        config_schema=config_schema,
        generated_at=datetime.utcnow(),
        generated_by="plugginger-manifest-generator",
    )


def generate_app_manifest(
    app_name: str,
    app_version: str,
    plugin_classes: list[type[Any]],
    *,
    description: str | None = None,
    plugin_configs: dict[str, dict[str, Any]] | None = None,
    global_config: dict[str, Any] | None = None,
) -> AppManifest:
    """
    Generate an application manifest from plugin classes.

    Args:
        app_name: Application name
        app_version: Application version
        plugin_classes: List of plugin classes in the application
        description: Application description (optional)
        plugin_configs: Plugin-specific configurations (optional)
        global_config: Global application configuration (optional)

    Returns:
        AppManifest object

    Raises:
        ValueError: If any plugin class is not valid
    """
    # Validate all plugin classes
    plugin_names = []
    for plugin_class in plugin_classes:
        if not is_plugin_class(plugin_class):
            raise ValueError(f"Class {plugin_class.__name__} is not a valid plugin class")

        plugin_meta = get_plugin_metadata(plugin_class)
        plugin_names.append(plugin_meta["name"])

    return AppManifest(
        app_name=app_name,
        app_version=app_version,
        description=description,
        plugins=plugin_names,
        plugin_configs=plugin_configs or {},
        global_config=global_config or {},
        generated_at=datetime.utcnow(),
        generated_by="plugginger-app-manifest-generator",
    )


def manifest_to_yaml(manifest: PluginManifest | AppManifest) -> str:
    """
    Convert a manifest to YAML string.

    Args:
        manifest: The manifest to convert

    Returns:
        YAML string representation
    """
    import yaml

    # Convert to dict and handle datetime serialization
    manifest_dict = manifest.model_dump(mode="json")

    # Custom YAML representer for better formatting
    def represent_none(self: Any, data: Any) -> Any:
        return self.represent_scalar("tag:yaml.org,2002:null", "")

    yaml.add_representer(type(None), represent_none)

    yaml_output: str = yaml.dump(
        manifest_dict,
        default_flow_style=False,
        sort_keys=False,
        allow_unicode=True,
        indent=2,
    )
    return yaml_output


def manifest_from_yaml(yaml_content: str, manifest_type: type[PluginManifest] | type[AppManifest]) -> PluginManifest | AppManifest:
    """
    Load a manifest from YAML string.

    Args:
        yaml_content: YAML string content
        manifest_type: Type of manifest to load (PluginManifest or AppManifest)

    Returns:
        Loaded manifest object

    Raises:
        ValueError: If YAML is invalid or doesn't match schema
    """
    import yaml

    try:
        data = yaml.safe_load(yaml_content)
        return manifest_type.model_validate(data)
    except yaml.YAMLError as e:
        raise ValueError(f"Invalid YAML: {e}") from e
    except Exception as e:
        raise ValueError(f"Invalid manifest data: {e}") from e


# Export all generator functions
__all__ = [
    "generate_plugin_manifest",
    "generate_app_manifest",
    "manifest_to_yaml",
    "manifest_from_yaml",
]
