# src/plugginger/_internal/validation/manifest_validation.py

"""
Validation related to plugin manifests and their consistency with plugin code.

This module provides comprehensive validation of plugin manifests, ensuring
that manifest declarations match the actual plugin implementation.
"""

from __future__ import annotations

import inspect
import logging
from typing import TYPE_CHECKING, Any

from plugginger.core.exceptions import ValidationError
from plugginger.schemas.manifest import PluginManifest

if TYPE_CHECKING:
    from plugginger.api.plugin import PluginBase


class ManifestValidator:
    """
    Validator for plugin manifests and their consistency with plugin code.

    This class provides comprehensive validation of plugin manifests:
    1. Schema compliance validation (handled by Pydantic)
    2. Consistency validation between manifest and plugin implementation
    3. Service signature validation against manifest declarations
    4. Dependency validation against manifest requirements
    """

    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the manifest validator.

        Args:
            logger: Optional logger for validation messages
        """
        self._logger = logger or logging.getLogger(__name__)

    def validate_plugin_manifest_consistency(
        self,
        plugin_class: type[PluginBase],
        manifest: PluginManifest,
    ) -> None:
        """
        Validate that a plugin manifest is consistent with the plugin implementation.

        This method performs comprehensive validation:
        1. Plugin metadata consistency (name, version)
        2. Service declarations match actual service methods
        3. Event listener declarations match actual event handlers
        4. Dependency declarations are consistent

        Args:
            plugin_class: Plugin class to validate
            manifest: Plugin manifest to validate against

        Raises:
            ValidationError: If manifest is inconsistent with plugin implementation
        """
        self._logger.debug(f"Validating manifest consistency for plugin {plugin_class.__name__}")

        # Validate plugin metadata consistency
        self._validate_plugin_metadata_consistency(plugin_class, manifest)

        # Validate service declarations
        self._validate_service_declarations(plugin_class, manifest)

        # Validate event listener declarations
        self._validate_event_listener_declarations(plugin_class, manifest)

        # Validate dependency declarations
        self._validate_dependency_declarations(plugin_class, manifest)

        self._logger.debug(f"Manifest validation successful for plugin {plugin_class.__name__}")

    def _validate_plugin_metadata_consistency(
        self,
        plugin_class: type[PluginBase],
        manifest: PluginManifest,
    ) -> None:
        """Validate plugin metadata consistency between class and manifest."""
        from plugginger.api.plugin import get_plugin_metadata

        # Get plugin metadata from decorator
        plugin_meta = get_plugin_metadata(plugin_class)

        # Validate plugin name consistency
        if plugin_meta["name"] != manifest.metadata.name:
            raise ValidationError(
                f"Plugin name mismatch: class declares '{plugin_meta['name']}' "
                f"but manifest declares '{manifest.metadata.name}'"
            )

        # Validate plugin version consistency
        if str(plugin_meta["version"]) != manifest.metadata.version:
            raise ValidationError(
                f"Plugin version mismatch: class declares '{plugin_meta['version']}' "
                f"but manifest declares '{manifest.metadata.version}'"
            )

    def _validate_service_declarations(
        self,
        plugin_class: type[PluginBase],
        manifest: PluginManifest,
    ) -> None:
        """Validate service declarations match actual service methods."""
        from plugginger.api.service import get_service_metadata, is_service_method

        # Get actual service methods from plugin class
        actual_services = {}
        for method_name in dir(plugin_class):
            method = getattr(plugin_class, method_name)
            if is_service_method(method):
                service_meta = get_service_metadata(method)
                service_name = service_meta.get("name", method_name)
                actual_services[service_name] = {
                    "method": method,
                    "metadata": service_meta,
                    "method_name": method_name,
                }

        # Get declared services from manifest
        declared_services = {service.name: service for service in manifest.services}

        # Check for missing service declarations
        for service_name in actual_services:
            if service_name not in declared_services:
                raise ValidationError(
                    f"Service '{service_name}' is implemented in plugin but not declared in manifest"
                )

        # Check for extra service declarations
        for service_name in declared_services:
            if service_name not in actual_services:
                raise ValidationError(
                    f"Service '{service_name}' is declared in manifest but not implemented in plugin"
                )

        # Validate service signatures
        for service_name, service_info in declared_services.items():
            actual_service = actual_services[service_name]
            self._validate_service_signature(
                actual_service["method"],
                service_info,
                service_name,
                actual_service["method_name"]
            )

    def _validate_service_signature(
        self,
        method: Any,
        service_info: Any,
        service_name: str,
        method_name: str,
    ) -> None:
        """Validate service method signature against manifest declaration."""
        try:
            sig = inspect.signature(method)
        except (ValueError, TypeError) as e:
            raise ValidationError(
                f"Cannot inspect signature of service method '{method_name}': {e}"
            ) from e

        # Validate return type if declared in manifest
        if hasattr(service_info, 'return_annotation') and service_info.return_annotation:
            if sig.return_annotation == inspect.Signature.empty:
                raise ValidationError(
                    f"Service '{service_name}' declares return type '{service_info.return_annotation}' "
                    f"in manifest but method '{method_name}' has no return annotation"
                )

        # Additional signature validation can be added here
        # For now, we rely on the existing service method validation

    def _validate_event_listener_declarations(
        self,
        plugin_class: type[PluginBase],
        manifest: PluginManifest,
    ) -> None:
        """Validate event listener declarations match actual event handlers."""
        from plugginger.api.events import get_event_listener_metadata, is_event_listener_method

        # Get actual event listeners from plugin class
        actual_listeners = {}
        for method_name in dir(plugin_class):
            method = getattr(plugin_class, method_name)
            if is_event_listener_method(method):
                listener_meta = get_event_listener_metadata(method)
                event_pattern = listener_meta.get("event_pattern", "")
                actual_listeners[f"{method_name}:{event_pattern}"] = {
                    "method": method,
                    "metadata": listener_meta,
                    "method_name": method_name,
                }

        # Get declared listeners from manifest
        declared_listeners = {}
        for listener in manifest.event_listeners:
            # Use first pattern for key (listeners can have multiple patterns)
            pattern = listener.patterns[0] if listener.patterns else ""
            key = f"{listener.method_name}:{pattern}"
            declared_listeners[key] = listener

        # Check for missing listener declarations
        for listener_key in actual_listeners:
            if listener_key not in declared_listeners:
                method_name = listener_key.split(":")[0]
                raise ValidationError(
                    f"Event listener '{method_name}' is implemented in plugin but not declared in manifest"
                )

        # Check for extra listener declarations
        for listener_key in declared_listeners:
            if listener_key not in actual_listeners:
                method_name = listener_key.split(":")[0]
                raise ValidationError(
                    f"Event listener '{method_name}' is declared in manifest but not implemented in plugin"
                )

    def _validate_dependency_declarations(
        self,
        plugin_class: type[PluginBase],
        manifest: PluginManifest,
    ) -> None:
        """Validate dependency declarations are consistent."""
        # Get actual dependencies from plugin class
        actual_deps = set()
        if hasattr(plugin_class, 'needs'):
            for dep in plugin_class.needs:
                if hasattr(dep, 'plugin_identifier'):
                    if isinstance(dep.plugin_identifier, str):
                        actual_deps.add(dep.plugin_identifier)
                    else:
                        # Handle class-based dependencies
                        from plugginger.api.plugin import get_plugin_metadata
                        dep_meta = get_plugin_metadata(dep.plugin_identifier)
                        actual_deps.add(dep_meta["name"])

        # Get declared dependencies from manifest
        declared_deps = {dep.name for dep in manifest.dependencies}

        # Check for missing dependency declarations
        for dep_name in actual_deps:
            if dep_name not in declared_deps:
                raise ValidationError(
                    f"Dependency '{dep_name}' is declared in plugin code but not in manifest"
                )

        # Check for extra dependency declarations
        for dep_name in declared_deps:
            if dep_name not in actual_deps:
                raise ValidationError(
                    f"Dependency '{dep_name}' is declared in manifest but not in plugin code"
                )


def validate_plugin_manifest_consistency(
    plugin_class: type[PluginBase],
    manifest: PluginManifest,
) -> None:
    """
    Validate that a plugin manifest is consistent with the plugin implementation.

    This is a convenience function that creates a ManifestValidator and performs
    the validation. For repeated validations, consider creating a ManifestValidator
    instance and reusing it.

    Args:
        plugin_class: Plugin class to validate
        manifest: Plugin manifest to validate against

    Raises:
        ValidationError: If manifest is inconsistent with plugin implementation
    """
    validator = ManifestValidator()
    validator.validate_plugin_manifest_consistency(plugin_class, manifest)
