# src/plugginger/_internal/builder_phases/interface_registrar.py

"""
Service and event interface registration for the PluggingerAppBuilder.

This module contains the InterfaceRegistrar class which handles the registration
of plugin services and event listeners with the runtime dispatchers.
"""

from __future__ import annotations

import logging
from collections.abc import Callable
from typing import TYPE_CHECKING, Any

from plugginger.core.constants import EVENT_METADATA_KEY, SERVICE_METADATA_KEY
from plugginger.core.exceptions import ServiceNameConflictError

if TYPE_CHECKING:
    from plugginger._internal.runtime_facade import RuntimeFacade
    from plugginger.api.plugin import PluginBase

# Type alias for instance ID generator function
InstanceIdGeneratorFunc = Callable[[str], str]


class InterfaceRegistrar:
    """
    Handles registration of plugin services and event listeners.

    This class is responsible for registering all plugin services and event
    listeners with the appropriate runtime dispatchers, and for finalizing
    event bridges for AppPlugin instances.
    """

    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the interface registrar.

        Args:
            logger: Optional logger for registration messages
        """
        self._logger = logger or logging.getLogger(__name__)

    def register_all(
        self,
        plugin_instances_map: dict[str, PluginBase],
        runtime_facade: "RuntimeFacade"
    ) -> None:
        """
        Register all plugin interfaces (services and event listeners).

        Iterates through all instantiated plugins and registers their services
        and event listeners with the runtime dispatchers. Also finalizes event
        bridges for AppPlugin instances.

        Args:
            plugin_instances_map: Map of registration names to plugin instances
            runtime_facade: Runtime facade containing the dispatchers
        """
        self._logger.debug("Registering all plugin interfaces (services and event listeners).")

        for registration_name, plugin_instance in plugin_instances_map.items():
            self._register_plugin_services_and_events(
                plugin_instance,
                registration_name,
                runtime_facade._service_dispatcher,
                runtime_facade._event_dispatcher
            )

            # If it's an AppPlugin, finalize its event bridges
            # Import here to avoid circular import
            from plugginger.api.app_plugin import AppPluginBase
            if isinstance(plugin_instance, AppPluginBase):
                self._finalize_app_plugin_event_bridges(
                    plugin_instance,
                    runtime_facade._event_dispatcher
                )

        self._logger.debug("All plugin interfaces registered.")

    def _register_plugin_services_and_events(
        self,
        plugin_instance: PluginBase,
        registration_name: str,
        service_dispatcher: Any,
        event_dispatcher: Any
    ) -> None:
        """
        Register services and event listeners for a single plugin instance.

        Args:
            plugin_instance: The plugin instance to register
            registration_name: Name under which plugin is known in this app's scope
            service_dispatcher: Service dispatcher instance
            event_dispatcher: Event dispatcher instance
        """
        instance_id = plugin_instance._plugginger_instance_id
        self._logger.debug(
            f"Registering services/events for plugin instance '{instance_id}' "
            f"(registered as '{registration_name}')."
        )

        for member_name in dir(plugin_instance):
            if member_name.startswith("_"):
                continue
            member_obj = getattr(plugin_instance, member_name)
            if not callable(member_obj):
                continue

            # Register services
            service_meta = getattr(member_obj, SERVICE_METADATA_KEY, None)
            if service_meta:  # Service metadata exists
                self._register_service(
                    member_obj,
                    service_meta,
                    member_name,
                    registration_name,
                    instance_id,
                    service_dispatcher
                )

            # Register event listeners
            event_meta = getattr(member_obj, EVENT_METADATA_KEY, None)
            if event_meta and "patterns" in event_meta:
                self._register_event_listeners(
                    member_obj,
                    event_meta,
                    member_name,
                    instance_id,
                    event_dispatcher
                )

    def _register_service(
        self,
        member_obj: Any,
        service_meta: dict[str, Any],
        member_name: str,
        registration_name: str,
        instance_id: str,
        service_dispatcher: Any
    ) -> None:
        """Register a single service method."""
        service_local_name = service_meta.get("name", member_name)

        fully_qualified_service_name: str
        if ":" in service_local_name:  # Assumed pre-qualified global name
            fully_qualified_service_name = service_local_name
        else:  # Prefix with registration_name (plugin's name in this app scope)
            fully_qualified_service_name = f"{registration_name}.{service_local_name}"

        try:
            service_dispatcher.add_service(fully_qualified_service_name, member_obj)
        except ServiceNameConflictError as e:
            self._logger.error(
                f"Service name conflict for '{fully_qualified_service_name}' from plugin '{instance_id}'."
            )
            raise ServiceNameConflictError(
                f"Service '{fully_qualified_service_name}' (from plugin instance '{instance_id}', "
                f"method '{member_name}') conflicts with an existing service."
            ) from e

    def _register_event_listeners(
        self,
        member_obj: Any,
        event_meta: dict[str, Any],
        member_name: str,
        instance_id: str,
        event_dispatcher: Any
    ) -> None:
        """Register event listeners for a plugin method."""
        patterns_list: list[str] = event_meta["patterns"]
        for pattern_str in patterns_list:
            event_dispatcher.add_listener(pattern_str, member_obj)
            self._logger.debug(
                f"  Registered event listener for pattern '{pattern_str}' "
                f"from plugin '{instance_id}' (handler: {member_name})."
            )

    def _finalize_app_plugin_event_bridges(
        self,
        app_plugin_instance: Any,  # AppPluginBase - using Any to avoid circular import
        outer_event_dispatcher: Any  # EventDispatcher of the app being built
    ) -> None:
        """
        Set up event bridges for an AppPlugin instance.

        This method processes the event bridge configurations stored in the AppPlugin
        and registers the appropriate listeners with the outer event dispatcher.

        Args:
            app_plugin_instance: The AppPlugin instance with configured event bridges
            outer_event_dispatcher: The event dispatcher of the outer app
        """
        instance_id = app_plugin_instance._plugginger_instance_id
        bridge_configs = app_plugin_instance._event_bridges_config

        self._logger.debug(
            f"Finalizing {len(bridge_configs)} event bridges for AppPlugin '{instance_id}'"
        )

        for bridge_config in bridge_configs:
            direction = bridge_config.get("direction")

            if direction == "external_to_internal":
                self._setup_external_to_internal_bridge(
                    app_plugin_instance,
                    bridge_config,
                    outer_event_dispatcher,
                    instance_id
                )
            elif direction == "internal_to_external":
                # Internal->external bridges are activated by the internal app's builder
                self._logger.debug(
                    f"Internal->external bridge configured for AppPlugin '{instance_id}' - "
                    f"will be activated by internal app builder"
                )
            else:
                self._logger.warning(
                    f"Unknown bridge direction '{direction}' in AppPlugin '{instance_id}' - skipping"
                )

        self._logger.debug(f"Event bridge finalization completed for AppPlugin '{instance_id}'")

    def _setup_external_to_internal_bridge(
        self,
        app_plugin_instance: Any,
        bridge_config: dict[str, Any],
        outer_event_dispatcher: Any,
        instance_id: str
    ) -> None:
        """Set up an external-to-internal event bridge."""
        external_pattern = bridge_config["external_pattern"]

        # Create closure that calls the AppPlugin's bridge execution method
        def _external_event_handler(event_data: dict[str, Any], matched_event_type: str) -> None:
            # Create async task for the bridge execution
            import asyncio
            asyncio.create_task(
                app_plugin_instance._execute_external_to_internal_bridge(
                    matched_event_type, event_data, bridge_config
                )
            )

        # Register the handler with the outer event dispatcher
        outer_event_dispatcher.add_listener(external_pattern, _external_event_handler)

        self._logger.debug(
            f"Registered external->internal bridge listener for pattern '{external_pattern}' "
            f"on AppPlugin '{instance_id}'"
        )
