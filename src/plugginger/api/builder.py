# src/plugginger/api/builder.py

"""
Defines the `PluggingerAppBuilder` class, used to configure and build
a `PluggingerAppInstance`. This is the primary entry point for assembling
a Plugginger application from its constituent plugins and app-plugins,
incorporating comprehensive dependency management, validation, and support
for fractal composition.
"""

from __future__ import annotations

import inspect
import logging  # Using standard Python logging
from typing import TYPE_CHECKING, Any

# Internal components
from plugginger._internal.builder_phases import (
    AppConfigResolver,
    DependencyOrchestrator,
    InterfaceRegistrar,
    PluginInstantiator,
)
from plugginger.api.depends import Depends

if TYPE_CHECKING:
    from plugginger.api.app import PluggingerAppInstance  # The product of the builder
    from plugginger.api.app_plugin import AppPluginBase  # For fractal composition

# Plugginger API and internal imports
from plugginger.api.plugin import PluginBase  # For type checking and metadata access
from plugginger.config.models import GlobalAppConfig  # Default config model
from plugginger.core.constants import (
    PLUGIN_METADATA_KEY,
)
from plugginger.core.exceptions import (
    AppPluginError,
    MissingDependencyError,
    PluginRegistrationError,
)
# Schema imports moved to function level to avoid circular imports

# Standard Python logger for the builder module
builder_logger: logging.Logger = logging.getLogger("plugginger.builder")
# Ensure a handler is present for the logger if not configured elsewhere (e.g., for library use)
if not builder_logger.handlers and not logging.getLogger("plugginger").handlers: # Check root too
    _handler = logging.StreamHandler()
    _formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    _handler.setFormatter(_formatter)
    # Add handler to the 'plugginger' root logger to affect all sub-loggers
    logging.getLogger("plugginger").addHandler(_handler)
    logging.getLogger("plugginger").setLevel(logging.WARNING) # Default for library
    builder_logger.setLevel(logging.INFO) # Builder can be more verbose if needed


class PluggingerAppBuilder:
    """
    Collects plugin and app-plugin class definitions, resolves their dependencies,
    validates configurations, and ultimately builds a runnable `PluggingerAppInstance`.

    This builder implements a comprehensive build process including:
    - Registration of `PluginBase` and `AppPluginBase` subclasses.
    - Construction and validation of a dependency graph (detecting cycles, missing deps).
    - Validation of version constraints between plugins.
    - Enforcement of maximum fractal depth for nested AppPlugins.
    - Instantiation of plugins in topological order.
    - Dependency Injection (DI) of plugin proxies into constructors, validated against
      `__init__` signatures and `needs` declarations.
    - Processing of global and plugin-specific configurations using Pydantic schemas.
    - Registration of `@service` methods and `@on_event` listeners with the runtime.
    - Setup of event bridges for `AppPluginBase` instances.
    """

    _app_name: str
    _parent_app_plugin_context: AppPluginBase | None
    _current_fractal_depth: int
    _max_fractal_depth: int
    _instance_id_prefix: str | None

    _registered_item_classes: dict[str, type[PluginBase] | type[AppPluginBase]]
    _plugin_dependency_declarations: dict[str, list[Depends]]
    _logger: logging.Logger # Instance logger

    def __init__(
        self,
        app_name: str,
        parent_app_plugin_context: AppPluginBase | None = None,
        _current_depth: int = 0, # Internal: tracks nesting for fractal depth limit
        _max_depth_from_config: int | None = None, # Passed from outer builder if sub-app
    ):
        """
        Initializes a new `PluggingerAppBuilder`.

        Args:
            app_name: The conceptual name of the application being built. This name
                      is used for logging and identification.
            parent_app_plugin_context: If this builder is used by an `AppPluginBase`
                                       to construct its internal app, this should be the
                                       instance of that `AppPluginBase`. It's used for
                                       context in fractal compositions (e.g., event bridging).
            _current_depth: Internal parameter to track the current nesting depth
                            of AppPlugins. Users should not set this directly.
            _max_depth_from_config: Internal parameter to propagate the maximum
                                    fractal depth from a parent builder.
        """
        if not isinstance(app_name, str) or not app_name:
            raise ValueError("PluggingerAppBuilder.app_name must be a non-empty string.")

        self._app_name = app_name
        self._parent_app_plugin_context = parent_app_plugin_context
        self._current_fractal_depth = _current_depth

        # Set instance ID prefix for hierarchical naming
        if parent_app_plugin_context is not None:
            self._instance_id_prefix = getattr(parent_app_plugin_context, '_plugginger_instance_id', app_name)
        else:
            self._instance_id_prefix = None

        # Max depth is either passed down or taken from GlobalAppConfig's default
        # It will be re-evaluated when the actual GlobalAppConfig is processed in build()
        default_max_depth = GlobalAppConfig.model_fields["max_fractal_depth"].default \
            if GlobalAppConfig.model_fields["max_fractal_depth"].default is not None else 10

        self._max_fractal_depth = _max_depth_from_config if _max_depth_from_config is not None \
                                  else default_max_depth

        self._registered_item_classes = {}
        self._plugin_dependency_declarations = {}
        self._logger = builder_logger # Use the module-level logger

        # Initialize builder phase helpers
        self._config_resolver = AppConfigResolver(self._logger)
        self._dependency_orchestrator = DependencyOrchestrator(self._logger)
        self._plugin_instantiator = PluginInstantiator(self._logger)
        self._interface_registrar = InterfaceRegistrar(self._logger)

        self._logger.info(f"PluggingerAppBuilder initialized for app '{app_name}' "
                          f"(depth: {_current_depth}, max_depth_initial: {self._max_fractal_depth}).")

    def get_registered_plugin_classes(self) -> dict[str, type[PluginBase] | type[AppPluginBase]]:
        """
        Returns a copy of the registered plugin classes dictionary.
        """
        return self._registered_item_classes.copy()

    def _get_plugin_metadata_attr(
        self,
        plugin_class_to_check: type[Any],
        attribute_name: str,
        context_message: str = "plugin processing"
    ) -> Any:
        """
        Safely retrieves a metadata attribute (like `_plugginger_plugin_name`)
        from a class, ensuring it was decorated with `@plugin`.

        Args:
            plugin_class_to_check: The class to inspect.
            attribute_name: The name of the metadata attribute to retrieve.
            context_message: A string describing the context of this call, for error messages.

        Returns:
            The value of the metadata attribute.

        Raises:
            PluginRegistrationError: If the class is not a valid plugin or lacks the attribute.
        """
        if not (inspect.isclass(plugin_class_to_check) and hasattr(plugin_class_to_check, PLUGIN_METADATA_KEY)):
             raise PluginRegistrationError(
                f"Class '{plugin_class_to_check.__name__}' used in '{context_message}' is not a valid "
                "Plugginger plugin (missing @plugin decorator or not a class)."
            )
        if not hasattr(plugin_class_to_check, attribute_name):
            # This should ideally be caught by the @plugin decorator itself if it ensures
            # all necessary attributes are set. This is a safeguard.
            raise PluginRegistrationError( # pragma: no cover
                f"Plugin class '{plugin_class_to_check.__name__}' is missing essential Plugginger "
                f"metadata attribute '{attribute_name}'. Ensure it's correctly processed by @plugin."
            )
        return getattr(plugin_class_to_check, attribute_name)

    def _register_item_class(
        self,
        registration_name: str, # The name under which this item is registered in *this* builder
        item_class: type[PluginBase] | type[AppPluginBase],
        is_app_plugin_type: bool
    ) -> None:
        """
        Internal helper to register a plugin or AppPlugin class.
        Validates the class and stores its definition and dependency declarations.
        """
        if not registration_name or not registration_name.isidentifier():
             raise PluginRegistrationError(
                 f"Invalid registration name '{registration_name}' for class '{item_class.__name__}'. "
                 "Must be a valid Python identifier (e.g., 'my_plugin')."
            )

        if registration_name in self._registered_item_classes:
            raise PluginRegistrationError(
                f"An item (Plugin or AppPlugin) with the registration name '{registration_name}' "
                f"is already included in the builder for app '{self._app_name}'."
            )

        # Ensure @plugin decorator was applied (checks for PLUGIN_METADATA_KEY)
        # and retrieve its declared name for consistency check if needed.
        # The actual @plugin(name=...) is more for self-identification of the plugin class.
        self._get_plugin_metadata_attr(item_class, "_plugginger_plugin_name", f"register_item({registration_name})")

        # Type checking for base classes
        if is_app_plugin_type:
            # Import here to avoid circular import
            from plugginger.api.app_plugin import AppPluginBase
            if not issubclass(item_class, AppPluginBase): # pragma: no cover
                raise PluginRegistrationError(
                    f"Class '{item_class.__name__}' registered as an AppPlugin (via include_app as '{registration_name}') "
                    "must inherit from `plugginger.api.app_plugin.AppPluginBase`."
                )
        else: # Regular plugin
            if not issubclass(item_class, PluginBase): # pragma: no cover
                # This case should be caught by @plugin decorator if PluginBase inheritance is enforced there.
                # If @plugin allows decorating non-PluginBase, this check is vital.
                # Assuming @plugin enforces PluginBase inheritance.
                raise PluginRegistrationError(
                    f"Class '{item_class.__name__}' registered as a Plugin (via include as '{registration_name}') "
                    "must inherit from `plugginger.api.plugin.PluginBase`."
                )
            # Import here to avoid circular import
            from plugginger.api.app_plugin import AppPluginBase
            if issubclass(item_class, AppPluginBase): # Regular plugins cannot be AppPlugins
                raise PluginRegistrationError(
                    f"Class '{item_class.__name__}' (an AppPlugin) was registered using `include()`. "
                    f"Please use `builder.include_app(..., as_plugin_name='{registration_name}')` for AppPlugins."
                )


        self._registered_item_classes[registration_name] = item_class

        # Retrieve 'needs' from the class, defaulting to empty list (PluginBase provides this default)
        class_level_needs: list[Depends] = getattr(item_class, "needs", [])
        if not isinstance(class_level_needs, list) or \
           not all(isinstance(dep, Depends) for dep in class_level_needs):
            raise PluginRegistrationError( # pragma: no cover
                f"Plugin '{registration_name}' (class {item_class.__name__}) has an invalid 'needs' attribute. "
                "It must be a `List[Depends]`."
            )
        self._plugin_dependency_declarations[registration_name] = list(class_level_needs) # Store a copy

        log_type = "AppPlugin class" if is_app_plugin_type else "Plugin class"
        self._logger.debug(f"{log_type} '{item_class.__name__}' registered as '{registration_name}' "
                           f"for app '{self._app_name}' with {len(class_level_needs)} declared dependencies.")

    def include(self, plugin_class: type[PluginBase]) -> PluggingerAppBuilder:
        """
        Registers a standard plugin class with the builder.

        The class must be decorated with `@plugin` and inherit from `PluginBase`.
        It must **not** be an `AppPluginBase` subclass; use `include_app()` for those.
        The plugin will be registered under the name specified in its `@plugin(name=...)` decorator.

        Args:
            plugin_class: The `PluginBase` subclass to include.

        Returns:
            The builder instance, allowing for a fluent (chained) interface.
        """
        if not inspect.isclass(plugin_class):
            raise TypeError(f"Argument to `include()` must be a class, got {type(plugin_class)}.")

        # Retrieve the registration name from the @plugin decorator
        registration_name: str = self._get_plugin_metadata_attr(plugin_class, "_plugginger_plugin_name", "include")
        self._register_item_class(registration_name, plugin_class, is_app_plugin_type=False)
        return self

    def include_app(
        self,
        app_plugin_class: type[AppPluginBase],
        as_plugin_name: str, # This is how the outer app refers to this AppPlugin
    ) -> PluggingerAppBuilder:
        """
        Registers an `AppPluginBase` subclass, enabling fractal composition.

        The `app_plugin_class` (which must inherit from `AppPluginBase` and be
        decorated with `@plugin`) will manage its own internal `PluggingerAppInstance`.

        Args:
            app_plugin_class: The `AppPluginBase` subclass to include.
            as_plugin_name: The name under which this AppPlugin will be known and
                            addressable within the current application being built.
                            This name is used for dependency injection targeting this
                            AppPlugin and for namespacing its exported services/events.
                            It must be a valid Python identifier.

        Returns:
            The builder instance for chaining.
        """
        if self._current_fractal_depth + 1 > self._max_fractal_depth:
            raise AppPluginError(
                f"Cannot include AppPlugin '{as_plugin_name}' (class {app_plugin_class.__name__}): "
                f"Exceeds maximum configured fractal depth of {self._max_fractal_depth} "
                f"(attempting to build at depth {self._current_fractal_depth + 1})."
            )

        if not inspect.isclass(app_plugin_class):
            raise TypeError(f"Argument to `include_app()` must be a class, got {type(app_plugin_class)}.")

        # The AppPlugin is registered in *this* builder under `as_plugin_name`.
        # Its own `@plugin(name=...)` is for its self-identification if it were top-level,
        # or if other plugins *within its own internal app* were to depend on its facade (less common).
        self._register_item_class(as_plugin_name, app_plugin_class, is_app_plugin_type=True)
        return self

    def _generate_plugin_instance_id(self, registration_name_in_current_scope: str) -> str:
        """
        Generates a globally unique, hierarchical instance ID for a plugin.
        If this builder is for a top-level app, `_app_name` is used as prefix.
        If for a sub-app, `_parent_app_plugin_context._plugginger_instance_id` is used.

        Args:
            registration_name_in_current_scope: The name under which the plugin
                is registered in the current builder's scope.

        Returns:
            The globally unique instance ID string.
        """
        # The instance_id prefix is determined by the context this builder operates in.
        # If it's a top-level builder, _parent_app_plugin_context is None.
        # If it's a builder for an AppPlugin's internal app, _parent_app_plugin_context is the AppPlugin instance.

        # Let's use self._app_name as the conceptual prefix for the current app level.
        # The builder's own `_app_name` should already be correctly namespaced if it's a sub-app builder.
        # Example:
        # Top builder: app_name="main"
        #   - Plugin "db" -> instance_id="main:db"
        #   - AppPlugin "users_module" (class UserManagementAppPlugin @plugin(name="user_mgmt_facade"))
        #     -> instance_id="main:users_module"
        #     Its internal builder: app_name="main:users_module_internal_app"
        #       - Internal Plugin "auth" -> instance_id="main:users_module_internal_app:auth"
        # This seems overly complex.
        # Simpler: instance_id is just the registration_name at its current level,
        # and full qualification happens at runtime if needed by looking up the hierarchy.
        # Or, the builder gets an `instance_id_prefix` at __init__.

        # Using the `_instance_id_prefix` set during builder initialization:
        if self._instance_id_prefix: # This builder is for a sub-app
            return f"{self._instance_id_prefix}:{registration_name_in_current_scope}"
        # This builder is for a top-level app, use its conceptual name as prefix
        return f"{self._app_name}:{registration_name_in_current_scope}"


    def build(
        self,
        app_config_input: GlobalAppConfig | dict[str, Any] | None = None,
    ) -> PluggingerAppInstance:
        """
        Builds and returns a `PluggingerAppInstance` based on the registered items
        and provided configuration.

        This method orchestrates the build process by delegating to specialized
        helper classes for each phase: configuration resolution, dependency
        management, plugin instantiation, and interface registration.
        """
        self._logger.info(f"Starting build for PluggingerAppInstance '{self._app_name}' "
                          f"(prefix: '{self._instance_id_prefix}', depth: {self._current_fractal_depth}).")

        # Phase 0: Resolve and Validate Global Configuration
        global_config = self._config_resolver.resolve_and_validate(
            self._app_name,
            self._max_fractal_depth,
            app_config_input
        )

        # Update max_fractal_depth based on potentially more restrictive config
        self._max_fractal_depth = min(self._max_fractal_depth, global_config.max_fractal_depth)
        if self._current_fractal_depth >= self._max_fractal_depth:
            raise AppPluginError(f"Initial build depth {self._current_fractal_depth} already meets or "
                                 f"exceeds max_fractal_depth {self._max_fractal_depth} for app '{self._app_name}'.")


        # Phase 1: Initialize Runtime Components (via RuntimeFacade)
        # Import here to avoid circular import
        from plugginger._internal.runtime_facade import RuntimeFacade

        runtime_facade = RuntimeFacade(
            global_config=global_config,
            logger=self._logger.info  # Pass a callable logger
        )

        # Phase 2: Build and Validate Dependency Graph
        graph = self._dependency_orchestrator.build_graph(
            self._registered_item_classes,
            self._plugin_dependency_declarations,
            self._get_plugin_metadata_attr
        )

        # Create resolver functions for validation
        def get_class_by_reg_name(reg_name: str) -> type[PluginBase]:
            cls = self._registered_item_classes.get(reg_name)
            if cls is None:
                raise MissingDependencyError(f"Class for '{reg_name}' not found.")
            return cls

        def get_version_by_reg_name(reg_name: str) -> str:
            cls = get_class_by_reg_name(reg_name)
            version = self._get_plugin_metadata_attr(cls, "_plugginger_plugin_version", "version_resolver")
            return str(version)

        sorted_registration_names = self._dependency_orchestrator.validate_graph_and_resolve_order(
            graph,
            self._registered_item_classes,
            get_version_by_reg_name,
            get_class_by_reg_name,
            self._plugin_dependency_declarations
        )

        # Phase 3: Create Provisional PluggingerAppInstance
        # Import here to avoid circular import
        from plugginger.api.app import PluggingerAppInstance

        provisional_app_instance = PluggingerAppInstance(
            app_name=self._app_name,
            runtime_facade=runtime_facade,
            global_config=global_config,
            parent_app_plugin_context=self._parent_app_plugin_context,
            _builder_fractal_depth=self._current_fractal_depth,
            _registered_plugin_classes=self._registered_item_classes.copy(),
        )
        # Make builder's fractal depth info available to AppPluginBase._configure_internal_app
        provisional_app_instance._current_build_depth_for_sub_apps = self._current_fractal_depth
        provisional_app_instance._max_build_depth_for_sub_apps = self._max_fractal_depth


        # Phase 4: Plugin Instantiation & Dependency Injection
        plugin_instances_map, plugin_configs_for_setup = self._plugin_instantiator.instantiate_all(
            sorted_registration_names,
            self._registered_item_classes,
            provisional_app_instance,
            global_config,
            self._plugin_dependency_declarations,
            self._generate_plugin_instance_id,
            self._get_plugin_metadata_attr
        )

        # Phase 5: Service and Event Listener Registration
        self._interface_registrar.register_all(plugin_instances_map, runtime_facade)

        # Phase 6: Finalize RuntimeFacade
        final_plugins_in_order_list = [plugin_instances_map[reg_name] for reg_name in sorted_registration_names]
        runtime_facade.finalize_setup(
            plugins_in_order=final_plugins_in_order_list,
            plugin_configs_for_setup=plugin_configs_for_setup
        )

        self._logger.info(f"PluggingerAppInstance '{self._app_name}' "
                            f"built successfully with {len(final_plugins_in_order_list)} plugins.")
        return provisional_app_instance

    def export_manifests(
        self,
        output_dir: str = "manifests",
        *,
        app_version: str = "1.0.0",
        app_description: str | None = None,
        author: str | None = None,
        license: str | None = None,
        keywords: list[str] | None = None,
        include_individual_manifests: bool = True,
        include_app_manifest: bool = True,
    ) -> dict[str, str]:
        """
        Export plugin and application manifests to YAML files.

        This method generates manifests for all registered plugins and optionally
        creates an application manifest that describes the complete application.

        Args:
            output_dir: Directory to save manifest files (default: "manifests")
            app_version: Version of the application (default: "1.0.0")
            app_description: Description of the application (optional)
            author: Author of plugins/application (optional)
            license: License for plugins/application (optional)
            keywords: Keywords for plugins/application (optional)
            include_individual_manifests: Whether to generate individual plugin manifests
            include_app_manifest: Whether to generate application manifest

        Returns:
            Dictionary mapping file paths to YAML content

        Raises:
            ValueError: If no plugins are registered or output directory is invalid
        """
        import os
        from pathlib import Path

        # Import schema functions here to avoid circular imports
        from plugginger.schemas import (
            generate_app_manifest,
            generate_plugin_manifest,
            manifest_to_yaml,
        )

        if not self._registered_item_classes:
            raise ValueError("No plugins registered - cannot export manifests")

        if not output_dir or not isinstance(output_dir, str):
            raise ValueError("output_dir must be a non-empty string")

        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        exported_files: dict[str, str] = {}

        # Export individual plugin manifests
        if include_individual_manifests:
            for registration_name, plugin_class in self._registered_item_classes.items():
                try:
                    # Generate plugin manifest
                    manifest = generate_plugin_manifest(
                        plugin_class,
                        author=author,
                        license=license,
                        keywords=keywords,
                    )

                    # Convert to YAML
                    yaml_content = manifest_to_yaml(manifest)

                    # Save to file
                    manifest_filename = f"{registration_name}_manifest.yaml"
                    manifest_path = output_path / manifest_filename

                    with open(manifest_path, "w", encoding="utf-8") as f:
                        f.write(yaml_content)

                    exported_files[str(manifest_path)] = yaml_content
                    self._logger.info(f"Exported manifest for plugin '{registration_name}' to {manifest_path}")

                except Exception as e:
                    self._logger.warning(f"Failed to export manifest for plugin '{registration_name}': {e}")

        # Export application manifest
        if include_app_manifest:
            try:
                # Get all plugin classes as a list
                plugin_classes = list(self._registered_item_classes.values())

                # Generate application manifest
                app_manifest = generate_app_manifest(
                    app_name=self._app_name,
                    app_version=app_version,
                    plugin_classes=plugin_classes,
                    description=app_description,
                )

                # Convert to YAML
                app_yaml_content = manifest_to_yaml(app_manifest)

                # Save to file
                app_manifest_filename = f"{self._app_name}_app_manifest.yaml"
                app_manifest_path = output_path / app_manifest_filename

                with open(app_manifest_path, "w", encoding="utf-8") as f:
                    f.write(app_yaml_content)

                exported_files[str(app_manifest_path)] = app_yaml_content
                self._logger.info(f"Exported application manifest to {app_manifest_path}")

            except Exception as e:
                self._logger.warning(f"Failed to export application manifest: {e}")

        self._logger.info(f"Manifest export completed. {len(exported_files)} files exported to {output_path}")
        return exported_files
