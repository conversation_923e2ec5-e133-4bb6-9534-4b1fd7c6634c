#!/usr/bin/env python3

"""
Debug script to investigate dependency injection integration issues.
"""

import asyncio
from typing import Any

from plugginger import Depends, PluggingerAppBuilder, PluggingerAppInstance, PluginBase, plugin, service


@plugin(name="test_basic_plugin", version="1.0.0")
class BasicTestPlugin(PluginBase):
    """Basic plugin for testing core lifecycle."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.setup_called = False
        self.teardown_called = False

    async def setup(self, plugin_config: Any) -> None:
        """Plugin setup hook."""
        self.setup_called = True

    async def teardown(self) -> None:
        """Plugin teardown hook."""
        self.teardown_called = True

    @service()
    async def get_status(self) -> str:
        """Test service method."""
        return "active"


@plugin(name="dependent_plugin", version="1.0.0")
class DependentTestPlugin(PluginBase):
    """Plugin with dependencies for testing DI integration."""
    
    needs: list[Depends] = [Depends("test_basic_plugin")]

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.setup_called = False
        self.teardown_called = False

    async def setup(self, plugin_config: Any) -> None:
        """Plugin setup hook."""
        self.setup_called = True

    async def teardown(self) -> None:
        """Plugin teardown hook."""
        self.teardown_called = True

    @service()
    async def get_dependency_status(self) -> str:
        """Service that uses injected dependency."""
        # Access the injected dependency through the DI system
        basic_plugin = getattr(self, "test_basic_plugin", None)
        if basic_plugin is not None:
            return await basic_plugin.get_status()
        return "dependency_not_found"


async def debug_di_integration() -> None:
    """Debug the dependency injection integration."""
    print("=== Dependency Injection Integration Debug ===\n")

    # Build app
    builder = PluggingerAppBuilder(app_name="di_test")
    builder.include(BasicTestPlugin)
    builder.include(DependentTestPlugin)
    
    app = builder.build()
    print(f"App built successfully: {app._app_name}")
    
    # Start plugins
    print("\nStarting plugins...")
    await app.start_all_plugins()
    print("Plugins started successfully")
    
    # Check plugin instances
    print("\nChecking plugin instances:")
    basic_plugin = app.get_plugin_instance("di_test:test_basic_plugin")
    dependent_plugin = app.get_plugin_instance("di_test:dependent_plugin")
    
    print(f"Basic plugin: {basic_plugin}")
    print(f"Dependent plugin: {dependent_plugin}")
    
    if basic_plugin:
        print(f"Basic plugin type: {type(basic_plugin)}")
        print(f"Basic plugin setup called: {basic_plugin.setup_called}")
    
    if dependent_plugin:
        print(f"Dependent plugin type: {type(dependent_plugin)}")
        print(f"Dependent plugin setup called: {dependent_plugin.setup_called}")
        
        # Check injected dependencies
        print(f"Dependent plugin has test_basic_plugin attr: {hasattr(dependent_plugin, 'test_basic_plugin')}")
        if hasattr(dependent_plugin, 'test_basic_plugin'):
            injected_dep = getattr(dependent_plugin, 'test_basic_plugin')
            print(f"Injected dependency: {injected_dep}")
            print(f"Injected dependency type: {type(injected_dep)}")
    
    # Test service calls
    print("\nTesting service calls:")
    try:
        result1 = await app.call_service("test_basic_plugin.get_status")
        print(f"Basic service result: {result1}")
    except Exception as e:
        print(f"Basic service failed: {e}")
    
    try:
        result2 = await app.call_service("dependent_plugin.get_dependency_status")
        print(f"Dependent service result: {result2}")
    except Exception as e:
        print(f"Dependent service failed: {e}")
    
    print("\n=== Debug Complete ===")


if __name__ == "__main__":
    asyncio.run(debug_di_integration())
