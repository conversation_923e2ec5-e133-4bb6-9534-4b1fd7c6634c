[tool.poetry]
name = "plugginger"
version = "1.0.0"
description = "Plugginger: Stop developing Python monoliths. Better compose them from pluggable and reusable mini-applications."
authors = ["<PERSON><PERSON>hn <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/jkehrhahn/plugginger"
repository = "https://github.com/jkehrhahn/plugginger"
documentation = "https://github.com/jkehrhahn/plugginger/blob/main/CHANGELOG.md"
keywords = ["plugin", "framework", "ai-friendly", "simple", "async"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Typing :: Typed"
]
packages = [{ include = "plugginger", from = "src" }]
include = ["README.md"]
scripts = { plugginger = "plugginger.cli:main" }

[tool.poetry.dependencies]
python = ">=3.11 <3.13"
pydantic = "^2.11.5"
pytest-cov = "^6.1.1"
deptry = "^0.23.0"
bandit = "^1.8.3"
pycycle = "^0.0.8"
libcst = "^1.8.0"
networkx = "^3.5"
prospector = "^1.17.1"

[tool.poetry.extras]
dev = ["pytest", "pytest-asyncio", "black", "mypy", "ruff", "coverage"]

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"
black = "^25.1.0"
mypy = "^1.15.0"
ruff = "^0.11.11"
coverage = "^7.8.2"

[tool.poetry.group.security.dependencies]
bandit = "^1.7.5"
safety = "^3.0.0"

# docs dependencies removed - not needed for private repo
[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# IDE / Tooling
[tool.black]
line-length = 100
target-version = ['py311']

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
check_untyped_defs = true
ignore_missing_imports = true
mypy_path = "src"

[tool.ruff]
line-length = 100
target-version = "py311"

[tool.ruff.lint]
select = ["E", "W", "F", "I", "B", "C4", "UP"]
ignore = ["E501", "B008", "C901", "E701"]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
    "*/build/*",
    "*/dist/*",
    "*/cli/*",
    "*/stubgen/*",
    "*/testing/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]
show_missing = true
skip_covered = false

[tool.coverage.html]
directory = "htmlcov"

# pytest configuration moved to pytest.ini to avoid conflicts
[tool.deptry]
# Ignore specific error codes
ignore = ["DEP002"] # Ignore unused dependencies for dev tools
# Exclude patterns (override defaults)
exclude = [
    "venv",
    "\\.venv",
    "\\.direnv",
    "tests",
    "\\.git",
    "setup\\.py",
    "build",
    "dist",
    ".*\\.egg-info",
    "__pycache__"
]

[tool.vulture]
# Minimum confidence level for dead code detection (0-100)
min_confidence = 80
# Exclude patterns
exclude = [
    "*.pyc",
    "__pycache__",
    ".git",
    ".venv",
    "build",
    "dist",
    "*.egg-info"
]
# Sort results by confidence level
sort_by_size = true
# Ignore decorators that may make functions appear unused
ignore_decorators = [
    "@plugin",
    "@service",
    "@on_event",
    "@background_task",
    "@pytest.fixture",
    "@pytest.mark.*"
]
# Ignore names that may appear unused but are actually used
ignore_names = [
    # Plugin lifecycle methods
    "setup",
    "teardown",
    # Test fixtures and methods
    "test_*",
    "*_test",
    # Magic methods
    "__*__",
    # Common variable names
    "args",
    "kwargs",
    "self",
    "cls",
    # Dependency injection
    "injected_dependencies",
    # Lambda and callback parameters
    "t",
    "msg",
    # Exception handling parameters
    "exc_type",
    "exc_val",
    "exc_tb",
    # Protocol method parameters (may be unused in interface definitions)
    "direction",
    "source_dispatcher",
    "target_dispatcher",
    "expected_schema"
]
