<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="2955pt" height="2176pt"
 viewBox="0.00 0.00 2955.07 2176.20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 2172.2)">
<title>G</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="white" stroke="none" points="-4,4 -4,-2172.2 2951.07,-2172.2 2951.07,4 -4,4"/>
<!-- packaging -->
<g id="node1" class="node">
<title>packaging</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b65353" stroke="black" cx="383.04" cy="-2034.09" rx="40.08" ry="18"/>
<text text-anchor="middle" x="383.04" y="-2030.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging</text>
</g>
<!-- plugginger__internal_validation_dependency_validation -->
<g id="node19" class="node">
<title>plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="396.66,-1621.9 267.41,-1621.9 267.41,-1562.9 396.66,-1562.9 396.66,-1621.9"/>
<text text-anchor="middle" x="332.04" y="-1608.4" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="332.04" y="-1595.65" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="332.04" y="-1582.9" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validation.</text>
<text text-anchor="middle" x="332.04" y="-1570.15" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dependency_validation</text>
</g>
<!-- packaging&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge1" class="edge">
<title>packaging&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M381.97,-2015.95C378.55,-1962.96 366.8,-1797.11 346.04,-1661.1 344.66,-1652.09 342.87,-1642.47 341.03,-1633.39"/>
<polygon fill="#b65353" stroke="black" points="344.49,-1632.82 339.01,-1623.75 337.63,-1634.26 344.49,-1632.82"/>
</g>
<!-- packaging_specifiers -->
<g id="node2" class="node">
<title>packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b34c4c" stroke="black" cx="83.04" cy="-2034.09" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="83.04" y="-2037.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="83.04" y="-2024.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">specifiers</text>
</g>
<!-- packaging_specifiers&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge2" class="edge">
<title>packaging_specifiers&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M90.93,-2010.24C112.5,-1949.68 176.62,-1781.44 263.04,-1661.1 270.76,-1650.35 280.27,-1639.79 289.74,-1630.33"/>
<polygon fill="#b34c4c" stroke="black" points="292.17,-1632.85 296.92,-1623.38 287.3,-1627.82 292.17,-1632.85"/>
</g>
<!-- packaging_version -->
<g id="node3" class="node">
<title>packaging_version</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#c24747" stroke="black" cx="180.04" cy="-2135.5" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="180.04" y="-2138.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="180.04" y="-2126" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">version</text>
</g>
<!-- packaging_version&#45;&gt;packaging_specifiers -->
<g id="edge3" class="edge">
<title>packaging_version&#45;&gt;packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M159.94,-2113.9C145.86,-2099.47 126.84,-2079.98 111.24,-2064"/>
<polygon fill="#c24747" stroke="black" points="113.99,-2061.8 104.5,-2057.09 108.98,-2066.69 113.99,-2061.8"/>
</g>
<!-- packaging_version&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge4" class="edge">
<title>packaging_version&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M198.28,-2113.25C226.38,-2078.29 277.04,-2005.31 277.04,-1933.68 277.04,-1933.68 277.04,-1933.68 277.04,-1821.26 277.04,-1753.73 300.14,-1678.01 316.4,-1633.09"/>
<polygon fill="#c24747" stroke="black" points="319.66,-1634.37 319.85,-1623.77 313.1,-1631.94 319.66,-1634.37"/>
</g>
<!-- plugginger__internal -->
<g id="node4" class="node">
<title>plugginger__internal</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="2173.04" cy="-1592.4" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2173.04" y="-1595.65" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2173.04" y="-1582.9" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal</text>
</g>
<!-- plugginger_api_app -->
<g id="node23" class="node">
<title>plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1792.04,-575.28 1722.04,-575.28 1722.04,-529.03 1792.04,-529.03 1792.04,-575.28"/>
<text text-anchor="middle" x="1757.04" y="-561.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1757.04" y="-549.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1757.04" y="-536.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_app -->
<g id="edge5" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2173.13,-1568.54C2173.38,-1505.2 2174.04,-1322 2174.04,-1169.74 2174.04,-1169.74 2174.04,-1169.74 2174.04,-907.78 2174.04,-845.52 2363.62,-722.05 2326.04,-672.41"/>
<path fill="none" stroke="black" d="M2326.04,-670.41C2247.31,-595.36 1939.18,-657.01 1840.04,-612.28"/>
</g>
<!-- plugginger_api_builder -->
<g id="node26" class="node">
<title>plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1742.04,-1100.03 1672.04,-1100.03 1672.04,-1053.78 1742.04,-1053.78 1742.04,-1100.03"/>
<text text-anchor="middle" x="1707.04" y="-1086.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1707.04" y="-1073.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1707.04" y="-1061.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_builder -->
<g id="edge6" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2162.88,-1568.96C2150.36,-1539.96 2129.8,-1487.58 2122.04,-1440.32 2116.03,-1403.72 2118.69,-1393.81 2122.04,-1356.88 2125.16,-1322.36 2154.44,-1309.53 2136.04,-1280.16"/>
<path fill="none" stroke="black" d="M2136.04,-1278.16C2068.74,-1188.37 1933.79,-1278.95 1908.04,-1169.74"/>
</g>
<!-- plugginger_api_events -->
<g id="node28" class="node">
<title>plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#5c8a2e" stroke="black" cx="1046.04" cy="-1398.6" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1046.04" y="-1408.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1046.04" y="-1395.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">api.</text>
<text text-anchor="middle" x="1046.04" y="-1382.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_events -->
<g id="edge7" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2139.33,-1574.85C2095.77,-1554.21 2017.08,-1519.39 1946.04,-1501.01"/>
<path fill="none" stroke="black" d="M1946.04,-1499.01C1901.92,-1487.59 1891.19,-1482.49 1846.04,-1476.32 1682.69,-1453.99 1261.2,-1493.17 1105.04,-1440.32 1098.38,-1438.07 1091.8,-1434.83 1085.57,-1431.15"/>
<polygon fill="#85f910" stroke="black" points="1087.7,-1428.36 1077.4,-1425.9 1083.92,-1434.25 1087.7,-1428.36"/>
</g>
<!-- plugginger_api_service -->
<g id="node30" class="node">
<title>plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#5c8a2e" stroke="black" cx="1163.04" cy="-1398.6" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1163.04" y="-1408.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1163.04" y="-1395.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">api.</text>
<text text-anchor="middle" x="1163.04" y="-1382.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">service</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_service -->
<g id="edge8" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1946.04,-1499.01C1917.63,-1491.65 1912.62,-1482.96 1884.04,-1476.32 1867.96,-1472.58 1393.97,-1423.44 1223.43,-1405.83"/>
<polygon fill="#85f910" stroke="black" points="1223.97,-1402.36 1213.67,-1404.82 1223.25,-1409.33 1223.97,-1402.36"/>
</g>
<!-- plugginger__internal_builder_phases -->
<g id="node5" class="node">
<title>plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1282.79,-1302.29 1193.29,-1302.29 1193.29,-1256.04 1282.79,-1256.04 1282.79,-1302.29"/>
<text text-anchor="middle" x="1238.04" y="-1288.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1238.04" y="-1276.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1238.04" y="-1263.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases</text>
</g>
<!-- plugginger__internal_builder_phases&#45;&gt;plugginger_api_builder -->
<g id="edge9" class="edge">
<title>plugginger__internal_builder_phases&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1248.89,-1255.94C1261.32,-1232.26 1283.55,-1194.82 1311.04,-1169.74"/>
<path fill="none" stroke="black" d="M1311.04,-1167.74C1362.03,-1121.2 1566.82,-1093.29 1660.2,-1082.74"/>
<polygon fill="blue" stroke="black" points="1660.49,-1086.23 1670.05,-1081.65 1659.72,-1079.28 1660.49,-1086.23"/>
</g>
<!-- plugginger__internal_builder_phases_app_config_resolver -->
<g id="node6" class="node">
<title>plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#669933" stroke="black" cx="1651.04" cy="-1398.6" rx="80.26" ry="41.72"/>
<text text-anchor="middle" x="1651.04" y="-1414.6" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1651.04" y="-1401.85" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_internal.</text>
<text text-anchor="middle" x="1651.04" y="-1389.1" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">builder_phases.</text>
<text text-anchor="middle" x="1651.04" y="-1376.35" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">app_config_resolver</text>
</g>
<!-- plugginger__internal_builder_phases_app_config_resolver&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge10" class="edge">
<title>plugginger__internal_builder_phases_app_config_resolver&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1582.99,-1376.14C1562.48,-1369.8 1539.88,-1362.94 1519.04,-1356.88 1441.19,-1334.25 1350.64,-1309.88 1293.84,-1294.82"/>
<polygon fill="#669933" stroke="black" points="1294.96,-1291.5 1284.4,-1292.32 1293.17,-1298.26 1294.96,-1291.5"/>
</g>
<!-- plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="node7" class="node">
<title>plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="558.91,-1428.1 419.16,-1428.1 419.16,-1369.1 558.91,-1369.1 558.91,-1428.1"/>
<text text-anchor="middle" x="489.04" y="-1414.6" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="489.04" y="-1401.85" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="489.04" y="-1389.1" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="489.04" y="-1376.35" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dependency_orchestrator</text>
</g>
<!-- plugginger__internal_builder_phases_dependency_orchestrator&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge11" class="edge">
<title>plugginger__internal_builder_phases_dependency_orchestrator&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M559.16,-1386.6C706.87,-1363.44 1047.45,-1310.04 1181.94,-1288.96"/>
<polygon fill="blue" stroke="black" points="1182.33,-1292.44 1191.67,-1287.43 1181.25,-1285.52 1182.33,-1292.44"/>
</g>
<!-- plugginger__internal_builder_phases_interface_registrar -->
<g id="node8" class="node">
<title>plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1349.04,-131 1243.04,-131 1243.04,-72 1349.04,-72 1349.04,-131"/>
<text text-anchor="middle" x="1296.04" y="-117.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1296.04" y="-104.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1296.04" y="-92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="1296.04" y="-79.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">interface_registrar</text>
</g>
<!-- plugginger__internal_builder_phases_interface_registrar&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge12" class="edge">
<title>plugginger__internal_builder_phases_interface_registrar&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1296.04,-131.21C1296.04,-167.11 1296.04,-230.44 1296.04,-284.69 1296.04,-672.41 1296.04,-672.41 1296.04,-672.41 1296.04,-734.81 1298.37,-750.38 1299.04,-812.78 1299.32,-839 1301.14,-845.65 1299.04,-871.78 1296.36,-904.99 1293.3,-913.06 1287.04,-945.78 1268.8,-1041.13 1230.23,-1065.45 1236.98,-1156.32"/>
<polygon fill="blue" stroke="black" points="1233.49,-1156.59 1237.9,-1166.23 1240.46,-1155.95 1233.49,-1156.59"/>
</g>
<!-- plugginger__internal_builder_phases_plugin_instantiator -->
<g id="node9" class="node">
<title>plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="756.79,-226 649.29,-226 649.29,-167 756.79,-167 756.79,-226"/>
<text text-anchor="middle" x="703.04" y="-212.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="703.04" y="-199.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="703.04" y="-187" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="703.04" y="-174.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin_instantiator</text>
</g>
<!-- plugginger__internal_builder_phases_plugin_instantiator&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge13" class="edge">
<title>plugginger__internal_builder_phases_plugin_instantiator&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M703.04,-226.43C703.04,-261.92 703.04,-323.92 703.04,-377.08 703.04,-470.91 703.04,-470.91 703.04,-470.91 703.04,-593.35 893.04,-547.96 893.04,-670.41 893.04,-754.66 893.04,-754.66 893.04,-754.66 893.04,-990 1181.35,-935.11 1235.56,-1156.63"/>
<polygon fill="blue" stroke="black" points="1232.12,-1157.26 1237.71,-1166.26 1238.95,-1155.74 1232.12,-1157.26"/>
<path fill="none" stroke="black" d="M1238.04,-1169.74C1243.94,-1199.08 1242.51,-1233.8 1240.58,-1255.9"/>
</g>
<!-- plugginger__internal_graph -->
<g id="node10" class="node">
<title>plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75bc2f" stroke="black" cx="812.04" cy="-1822.26" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="812.04" y="-1831.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="812.04" y="-1819.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="812.04" y="-1806.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">graph</text>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge14" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M463.04,-1701.82C425.64,-1671.42 470.99,-1640.93 463.04,-1593.4"/>
<path fill="none" stroke="black" d="M463.04,-1591.4C456.41,-1551.77 454.13,-1540.18 463.04,-1501.01"/>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge15" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M781.44,-1796.14C772.72,-1790.13 762.9,-1784.36 753.04,-1780.54 641.78,-1737.48 586.41,-1810.55 487.04,-1744.54 469.54,-1732.92 479.34,-1717.07 463.04,-1703.82"/>
<path fill="none" stroke="black" d="M463.04,-1701.82C433.63,-1677.92 400.78,-1650.72 375.33,-1629.55"/>
<polygon fill="#75bc2f" stroke="black" points="377.74,-1627.01 367.81,-1623.3 373.26,-1632.38 377.74,-1627.01"/>
</g>
<!-- plugginger__internal_proxy -->
<g id="node11" class="node">
<title>plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="459.04,-401.2 389.04,-401.2 389.04,-354.95 459.04,-354.95 459.04,-401.2"/>
<text text-anchor="middle" x="424.04" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="424.04" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="424.04" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">proxy</text>
</g>
<!-- plugginger__internal_proxy&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge16" class="edge">
<title>plugginger__internal_proxy&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M415.66,-354.71C409.29,-333.4 404.58,-302.31 424.04,-286.69"/>
</g>
<!-- plugginger__internal_runtime -->
<g id="node12" class="node">
<title>plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1584.04,-776.78 1514.04,-776.78 1514.04,-730.53 1584.04,-730.53 1584.04,-776.78"/>
<text text-anchor="middle" x="1549.04" y="-763.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1549.04" y="-750.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1549.04" y="-737.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime</text>
</g>
<!-- plugginger__internal_runtime_facade -->
<g id="node17" class="node">
<title>plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1922.29,-694.53 1829.79,-694.53 1829.79,-648.28 1922.29,-648.28 1922.29,-694.53"/>
<text text-anchor="middle" x="1876.04" y="-681.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1876.04" y="-668.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1876.04" y="-655.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime_facade</text>
</g>
<!-- plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge17" class="edge">
<title>plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1584.34,-743.99C1640.5,-730.21 1750.74,-703.16 1818.45,-686.54"/>
<polygon fill="blue" stroke="black" points="1819,-690.01 1827.87,-684.23 1817.33,-683.21 1819,-690.01"/>
</g>
<!-- plugginger__internal_runtime_dispatcher -->
<g id="node13" class="node">
<title>plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1515.04,-1621.9 1445.04,-1621.9 1445.04,-1562.9 1515.04,-1562.9 1515.04,-1621.9"/>
<text text-anchor="middle" x="1480.04" y="-1608.4" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1480.04" y="-1595.65" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1480.04" y="-1582.9" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1480.04" y="-1570.15" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dispatcher</text>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime -->
<g id="edge18" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1486.18,-1562.61C1501.41,-1490.93 1539.97,-1308.25 1543.04,-1280.16"/>
<path fill="none" stroke="black" d="M1543.04,-1278.16C1543.28,-1084.7 1537.26,-1035.15 1562.04,-843.28"/>
<path fill="none" stroke="black" d="M1562.04,-841.28C1560.75,-823.59 1557.93,-804.03 1555.25,-787.98"/>
<polygon fill="blue" stroke="black" points="1558.76,-787.74 1553.6,-778.48 1551.86,-788.93 1558.76,-787.74"/>
</g>
<!-- plugginger__internal_runtime_executors -->
<g id="node14" class="node">
<title>plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#669933" stroke="black" cx="1658.04" cy="-1279.16" rx="49.5" ry="41.72"/>
<text text-anchor="middle" x="1658.04" y="-1295.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1658.04" y="-1282.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_internal.</text>
<text text-anchor="middle" x="1658.04" y="-1269.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">runtime.</text>
<text text-anchor="middle" x="1658.04" y="-1256.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">executors</text>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime -->
<g id="edge19" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1633.06,-1242.76C1608.3,-1204.14 1574.04,-1139.55 1574.04,-1077.91 1574.04,-1077.91 1574.04,-1077.91 1574.04,-944.78 1574.04,-899.36 1556.83,-888.41 1562.04,-843.28"/>
</g>
<!-- plugginger__internal_runtime_fault_policy -->
<g id="node15" class="node">
<title>plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#6bac2b" stroke="black" cx="1501.04" cy="-1702.82" rx="51.62" ry="41.72"/>
<text text-anchor="middle" x="1501.04" y="-1718.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1501.04" y="-1706.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1501.04" y="-1693.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">runtime.</text>
<text text-anchor="middle" x="1501.04" y="-1680.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">fault_policy</text>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime -->
<g id="edge20" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1514.79,-1662.43C1518.4,-1650.57 1521.88,-1637.43 1524.04,-1625.1 1550.49,-1473.86 1536.14,-1433.54 1543.04,-1280.16"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge21" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1493.16,-1661.14C1491.39,-1652.01 1489.52,-1642.34 1487.75,-1633.25"/>
<polygon fill="#6bac2b" stroke="black" points="1491.23,-1632.77 1485.89,-1623.61 1484.35,-1634.1 1491.23,-1632.77"/>
</g>
<!-- plugginger__internal_runtime_lifecycle -->
<g id="node16" class="node">
<title>plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1257.04,-871.78 1187.04,-871.78 1187.04,-812.78 1257.04,-812.78 1257.04,-871.78"/>
<text text-anchor="middle" x="1222.04" y="-858.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1222.04" y="-845.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1222.04" y="-832.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1222.04" y="-820.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">lifecycle</text>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime -->
<g id="edge22" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1257.32,-835.39C1310.71,-825.97 1414.85,-805.59 1500.04,-776.78 1501.16,-776.4 1502.3,-776 1503.44,-775.59"/>
<polygon fill="blue" stroke="black" points="1504.31,-779.01 1512.36,-772.12 1501.77,-772.49 1504.31,-779.01"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge23" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1869.34,-647.86C1859.18,-617.21 1837.16,-562.4 1801.04,-529.03 1708.92,-443.94 1513.07,-494.52 1562.04,-379.08"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app -->
<g id="edge24" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1869.18,-647.84C1863.72,-634.44 1854.53,-618.82 1840.04,-612.28"/>
<path fill="none" stroke="black" d="M1840.04,-611.28C1823.33,-603.75 1806.48,-592.72 1792.36,-582.29"/>
<polygon fill="blue" stroke="black" points="1794.79,-579.74 1784.71,-576.48 1790.56,-585.32 1794.79,-579.74"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder -->
<g id="edge25" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1876.47,-694.81C1877.06,-727.15 1878.04,-788.8 1878.04,-841.28 1878.04,-908.78 1878.04,-908.78 1878.04,-908.78 1878.04,-957.25 1843.45,-960.25 1810.03,-990.85"/>
<polygon fill="blue" stroke="black" points="1807.75,-988.19 1803.11,-997.72 1812.68,-993.16 1807.75,-988.19"/>
</g>
<!-- plugginger__internal_validation -->
<g id="node18" class="node">
<title>plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85db2f" stroke="black" cx="1072.04" cy="-1592.4" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1072.04" y="-1602.02" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1072.04" y="-1589.27" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1072.04" y="-1576.52" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation</text>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge26" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1028.22,-1576.98C930.69,-1544.9 694.52,-1467.2 569.6,-1426.1"/>
<polygon fill="#85db2f" stroke="black" points="571,-1422.88 560.4,-1423.08 568.81,-1429.53 571,-1422.88"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_events -->
<g id="edge27" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1040.66,-1566.64C1023.5,-1550.08 1005.91,-1526.69 1007.04,-1501.01"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_service -->
<g id="edge28" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1107.68,-1569.23C1128.69,-1553.09 1150.88,-1529.19 1149.04,-1501.01"/>
<path fill="none" stroke="black" d="M1149.04,-1499.01C1147.82,-1480.32 1150.09,-1459.8 1153.13,-1442.29"/>
<polygon fill="#85db2f" stroke="black" points="1156.52,-1443.22 1154.94,-1432.75 1149.64,-1441.92 1156.52,-1443.22"/>
</g>
<!-- plugginger__internal_validation_dependency_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge29" class="edge">
<title>plugginger__internal_validation_dependency_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M397.09,-1569.52C426.2,-1555.4 455.66,-1533.47 463.04,-1501.01"/>
<path fill="none" stroke="black" d="M463.04,-1499.01C467.53,-1479.22 473.17,-1457.39 478.04,-1439.24"/>
<polygon fill="blue" stroke="black" points="481.4,-1440.22 480.64,-1429.65 474.64,-1438.39 481.4,-1440.22"/>
</g>
<!-- plugginger__internal_validation_name_validation -->
<g id="node20" class="node">
<title>plugginger__internal_validation_name_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75a446" stroke="black" cx="675.04" cy="-1822.26" rx="69.12" ry="41.72"/>
<text text-anchor="middle" x="675.04" y="-1838.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="675.04" y="-1825.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="675.04" y="-1812.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation.</text>
<text text-anchor="middle" x="675.04" y="-1800.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">name_validation</text>
</g>
<!-- plugginger__internal_validation_plugin_validation -->
<g id="node21" class="node">
<title>plugginger__internal_validation_plugin_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75bc2f" stroke="black" cx="1018.04" cy="-1702.82" rx="70.71" ry="41.72"/>
<text text-anchor="middle" x="1018.04" y="-1718.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1018.04" y="-1706.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1018.04" y="-1693.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation.</text>
<text text-anchor="middle" x="1018.04" y="-1680.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugin_validation</text>
</g>
<!-- plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_events -->
<g id="edge30" class="edge">
<title>plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1010.84,-1661.06C1006.86,-1640.58 1001.42,-1615.49 995.04,-1593.4"/>
<path fill="none" stroke="black" d="M995.04,-1591.4C983.78,-1552.47 1005.26,-1541.49 1007.04,-1501.01"/>
<path fill="none" stroke="black" d="M1007.04,-1499.01C1007.94,-1478.41 1015.42,-1456.96 1023.58,-1439.27"/>
<polygon fill="#75bc2f" stroke="black" points="1026.53,-1441.22 1027.76,-1430.69 1020.24,-1438.15 1026.53,-1441.22"/>
</g>
<!-- plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_service -->
<g id="edge31" class="edge">
<title>plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1069,-1673.49C1089.51,-1660.5 1112.43,-1643.81 1130.04,-1625.1 1141.29,-1613.14 1147.25,-1609.73 1149.04,-1593.4"/>
<path fill="none" stroke="black" d="M1149.04,-1591.4C1153.41,-1551.46 1151.65,-1541.09 1149.04,-1501.01"/>
</g>
<!-- plugginger_api -->
<g id="node22" class="node">
<title>plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="712.54,-1017.78 627.54,-1017.78 627.54,-981.78 712.54,-981.78 712.54,-1017.78"/>
<text text-anchor="middle" x="670.04" y="-996.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.api</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge32" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M429.04,-1077.91C346.15,-1154.21 428.89,-1305.74 469.45,-1368.91"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge33" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M627.13,-992.91C553.83,-981.67 411.17,-954.16 399.04,-908.78"/>
<path fill="none" stroke="black" d="M399.04,-907.78C289.85,-773.67 260.7,-741.63 147.04,-611.28 124.25,-585.16 106.96,-585.71 95.04,-553.16"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge34" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M323.04,-752.66C225.06,-600.08 255.25,-501.76 347.04,-345.38 368.82,-308.27 390.49,-313.63 424.04,-286.69"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_proxy -->
<g id="edge35" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M627.24,-997.33C569.84,-992.84 475.04,-975.34 475.04,-908.78 475.04,-908.78 475.04,-908.78 475.04,-670.41 475.04,-615.63 425.27,-606.66 437.04,-553.16"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge36" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M712.72,-997C819.99,-991.6 1096.71,-971.73 1159.04,-908.78"/>
<path fill="none" stroke="black" d="M1159.04,-907.78C1167.62,-898.75 1176.96,-889.1 1185.76,-880.08"/>
<polygon fill="blue" stroke="black" points="1188.09,-882.7 1192.58,-873.1 1183.09,-877.8 1188.09,-882.7"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge37" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M712.85,-983.76C760.65,-966.7 840.09,-937.5 907.04,-908.78 929.69,-899.07 1083.75,-820.88 1107.04,-812.78 1185.57,-785.48 1390.27,-745.6 1472.04,-730.53 1594.75,-707.92 1739.79,-688.91 1818.39,-679.25"/>
<polygon fill="blue" stroke="black" points="1818.49,-682.77 1827.99,-678.08 1817.64,-675.82 1818.49,-682.77"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge38" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M627.19,-1004.47C577.79,-1010.63 495.98,-1026.81 438.11,-1068.92"/>
<polygon fill="blue" stroke="black" points="436.03,-1066.11 430.24,-1074.99 440.29,-1071.66 436.03,-1066.11"/>
<path fill="none" stroke="black" d="M429.04,-1077.91C277.75,-1197.78 310.93,-1473.05 326.45,-1562.65"/>
</g>
<!-- plugginger_cli_cmd_core_freeze -->
<g id="node32" class="node">
<title>plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2202.66,-308.81 2103.41,-308.81 2103.41,-262.56 2202.66,-262.56 2202.66,-308.81"/>
<text text-anchor="middle" x="2153.04" y="-295.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2153.04" y="-282.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="2153.04" y="-269.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_core_freeze</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge39" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M675.84,-981.31C684.84,-952.79 701.31,-894.31 703.04,-843.28"/>
<path fill="none" stroke="black" d="M703.04,-841.28C717.34,-691.98 733.95,-643.36 831.04,-529.03 850.56,-506.04 912.66,-461.43 939.04,-446.78 1050.01,-385.16 1081.93,-372.02 1206.04,-345.38 1467.23,-289.31 1539.41,-326.09 1806.04,-309.38 1906.2,-303.1 2022.61,-295.4 2091.92,-290.78"/>
<polygon fill="blue" stroke="black" points="2091.79,-294.3 2101.54,-290.14 2091.33,-287.32 2091.79,-294.3"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate -->
<g id="node34" class="node">
<title>plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1658.66,-124.62 1541.41,-124.62 1541.41,-78.38 1658.66,-78.38 1658.66,-124.62"/>
<text text-anchor="middle" x="1600.04" y="-111.12" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1600.04" y="-98.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="1600.04" y="-85.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_stubs_generate</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge40" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M399.04,-907.78C372.59,-870.37 380.05,-853.49 359.04,-812.78 345.1,-785.78 341.77,-778.59 323.04,-754.66"/>
<path fill="none" stroke="black" d="M323.04,-752.66C304.71,-722.88 286.98,-725.11 270.04,-694.53 251.64,-661.33 247.04,-650.24 247.04,-612.28 247.04,-612.28 247.04,-612.28 247.04,-551.16 247.04,-306.91 401.72,-220.52 640.04,-167 951.78,-96.99 1039.64,-157.7 1358.04,-131 1415.99,-126.14 1481.49,-118.28 1529.72,-112.04"/>
<polygon fill="blue" stroke="black" points="1530.01,-115.54 1539.47,-110.77 1529.1,-108.6 1530.01,-115.54"/>
</g>
<!-- plugginger_cli_utils -->
<g id="node35" class="node">
<title>plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75c823" stroke="black" cx="1639.04" cy="-378.08" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1639.04" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1639.04" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cli.</text>
<text text-anchor="middle" x="1639.04" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">utils</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_utils -->
<g id="edge41" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M703.04,-841.28C715.91,-719.81 855.04,-734.44 855.04,-612.28"/>
<path fill="none" stroke="black" d="M855.04,-611.28C852.65,-594.28 844.2,-592.16 841.04,-575.28 837.25,-555.08 829.27,-545.89 841.04,-529.03 867.59,-490.98 895.92,-510.17 939.04,-493.03 987.56,-473.75 996.27,-459.01 1047.04,-446.78 1276.14,-391.62 1348.74,-472.98 1576.04,-410.78 1581.43,-409.31 1586.91,-407.35 1592.27,-405.12"/>
<polygon fill="blue" stroke="black" points="1593.31,-408.49 1600.99,-401.2 1590.44,-402.11 1593.31,-408.49"/>
</g>
<!-- plugginger_stubgen -->
<g id="node50" class="node">
<title>plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#669933" stroke="black" cx="834.04" cy="-285.69" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="834.04" y="-288.94" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="834.04" y="-276.19" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">stubgen</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_stubgen -->
<g id="edge42" class="edge">
<title>plugginger_api&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M661.8,-981.48C650.58,-957.36 630.49,-912.07 618.04,-871.78 588.16,-775.14 567.65,-749.17 575.04,-648.28 578.16,-605.66 589.04,-595.89 589.04,-553.16 589.04,-553.16 589.04,-553.16 589.04,-468.91 589.04,-374.49 707.63,-323.25 779.79,-300.81"/>
<polygon fill="blue" stroke="black" points="780.5,-304.25 789.07,-298.02 778.48,-297.55 780.5,-304.25"/>
</g>
<!-- plugginger_testing_helpers -->
<g id="node53" class="node">
<title>plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2685.04,-308.81 2615.04,-308.81 2615.04,-262.56 2685.04,-262.56 2685.04,-308.81"/>
<text text-anchor="middle" x="2650.04" y="-295.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2650.04" y="-282.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing.</text>
<text text-anchor="middle" x="2650.04" y="-269.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">helpers</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_testing_helpers -->
<g id="edge43" class="edge">
<title>plugginger_api&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M855.04,-611.28C858.64,-562.24 888.35,-556.66 929.04,-529.03 950.8,-514.26 1125.31,-452.28 1151.04,-446.78 1388.87,-396.01 1463.77,-479.56 1697.04,-410.78 1724.84,-402.59 1725.27,-382.67 1754.04,-379.08"/>
<path fill="none" stroke="black" d="M1754.04,-377.08C1782.8,-373.45 1783.25,-353.63 1811.04,-345.38 1849.24,-334.03 2432.75,-299.34 2603.46,-289.38"/>
<polygon fill="blue" stroke="black" points="2603.5,-292.89 2613.28,-288.81 2603.1,-285.9 2603.5,-292.89"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge44" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1721.62,-545.37C1678.88,-537.48 1605.61,-521.04 1548.04,-493.03 1392.24,-417.24 1395.09,-325.88 1234.04,-262 1150.72,-228.96 891.17,-209.07 768.5,-201.29"/>
<polygon fill="blue" stroke="black" points="768.78,-197.8 758.58,-200.67 768.34,-204.79 768.78,-197.8"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_proxy -->
<g id="edge45" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1721.93,-541.67C1704.61,-537.28 1683.34,-532.33 1664.04,-529.03 1512.5,-503.19 1466.36,-540.15 1320.04,-493.03 1280.58,-480.33 1278.63,-459.07 1239.04,-446.78 1202.12,-435.33 638.24,-394.37 470.66,-382.39"/>
<polygon fill="blue" stroke="black" points="471.02,-378.91 460.8,-381.69 470.52,-385.89 471.02,-378.91"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api -->
<g id="edge46" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1721.77,-562.47C1680.12,-573.59 1608.8,-592.98 1548.04,-611.28 1497.03,-626.65 1484.43,-631.02 1434.04,-648.28 1236.13,-716.08 1162.29,-689.85 993.04,-812.78 965.68,-832.65 971.92,-851.3 945.04,-871.78 884.06,-918.24 848.33,-900.13 788.45,-938.42"/>
<polygon fill="blue" stroke="black" points="786.62,-935.44 780.29,-943.94 790.54,-941.24 786.62,-935.44"/>
</g>
<!-- plugginger_api_app_plugin -->
<g id="node24" class="node">
<title>plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1890.04,-401.2 1820.04,-401.2 1820.04,-354.95 1890.04,-354.95 1890.04,-401.2"/>
<text text-anchor="middle" x="1855.04" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1855.04" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1855.04" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app_plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_app_plugin -->
<g id="edge47" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1775.53,-518.69C1793.04,-487.95 1819.18,-442.04 1836.65,-411.36"/>
<polygon fill="blue" stroke="black" points="1772.5,-516.93 1770.6,-527.35 1778.59,-520.39 1772.5,-516.93"/>
<polygon fill="blue" stroke="black" points="1839.56,-413.32 1841.47,-402.9 1833.48,-409.85 1839.56,-413.32"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_builder -->
<g id="edge48" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1771.27,-575.54C1784.46,-598.44 1802.04,-635.52 1802.04,-670.41 1802.04,-908.78 1802.04,-908.78 1802.04,-908.78 1802.04,-944.56 1822.99,-964.15 1808.66,-989.53"/>
<polygon fill="blue" stroke="black" points="1805.89,-987.39 1802.92,-997.55 1811.59,-991.46 1805.89,-987.39"/>
<path fill="none" stroke="black" d="M1802.04,-1000.78C1784.56,-1020.97 1761.04,-1039.62 1741.78,-1053.29"/>
</g>
<!-- plugginger_api_plugin -->
<g id="node29" class="node">
<title>plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1230.04,-493.03 1160.04,-493.03 1160.04,-446.78 1230.04,-446.78 1230.04,-493.03"/>
<text text-anchor="middle" x="1195.04" y="-479.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1195.04" y="-466.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1195.04" y="-454.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_plugin -->
<g id="edge49" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1710.96,-538.77C1696.08,-535.16 1679.44,-531.51 1664.04,-529.03 1479.06,-499.27 1425.34,-540.26 1244.04,-493.03 1243.05,-492.78 1242.05,-492.5 1241.06,-492.21"/>
<polygon fill="blue" stroke="black" points="1709.94,-542.12 1720.48,-541.15 1711.63,-535.33 1709.94,-542.12"/>
<polygon fill="blue" stroke="black" points="1242.41,-488.97 1231.82,-489.04 1240.14,-495.59 1242.41,-488.97"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_testing_helpers -->
<g id="edge50" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1792.32,-540.71C1932.08,-499.32 2446.91,-346.85 2604.13,-300.28"/>
<polygon fill="blue" stroke="black" points="2604.78,-303.74 2613.37,-297.55 2602.79,-297.03 2604.78,-303.74"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge51" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1819.87,-362.28C1765.99,-338.57 1661.34,-288.32 1586.04,-226 1573.28,-215.44 1574.09,-208.85 1562.04,-197.5"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api -->
<g id="edge52" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1819.62,-394.07C1804.05,-400.11 1785.39,-406.62 1768.04,-410.78 1639.16,-441.67 1593.78,-394.43 1472.04,-446.78 1366.05,-492.37 1368.23,-548.59 1272.04,-612.28 1176.86,-675.31 1139.57,-668.05 1044.04,-730.53 920.5,-811.33 908.38,-862.44 788.62,-938.77"/>
<polygon fill="blue" stroke="black" points="786.93,-935.7 780.32,-943.98 790.65,-941.62 786.93,-935.7"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge54" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1822,-401.51C1787.09,-427.47 1734.23,-473.8 1713.04,-529.03 1652.71,-686.29 1726.04,-739.35 1726.04,-907.78 1726.04,-945.78 1726.04,-945.78 1726.04,-945.78 1726.04,-978.85 1719.96,-1016.14 1714.67,-1042.31"/>
<polygon fill="blue" stroke="black" points="1711.27,-1041.5 1712.64,-1052 1718.12,-1042.94 1711.27,-1041.5"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge55" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1841.68,-354.78C1829.8,-335.76 1811.35,-308.17 1792.04,-286.69"/>
</g>
<!-- plugginger_api_background -->
<g id="node25" class="node">
<title>plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75b03a" stroke="black" cx="547.04" cy="-1168.74" rx="51.62" ry="32.7"/>
<text text-anchor="middle" x="547.04" y="-1178.36" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="547.04" y="-1165.61" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="547.04" y="-1152.86" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">background</text>
</g>
<!-- plugginger_api_background&#45;&gt;plugginger_api -->
<g id="edge56" class="edge">
<title>plugginger_api_background&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M561.59,-1137.15C573.68,-1113.27 592.22,-1079.88 613.04,-1053.78 620.95,-1043.86 630.74,-1034.08 639.95,-1025.67"/>
<polygon fill="#75b03a" stroke="black" points="642.23,-1028.33 647.4,-1019.08 637.59,-1023.09 642.23,-1028.33"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_api -->
<g id="edge57" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1671.93,-1072.86C1620.74,-1068.44 1522.6,-1060.11 1439.04,-1053.78 1170.85,-1033.47 849.8,-1012.39 724.17,-1004.26"/>
<polygon fill="blue" stroke="black" points="724.7,-1000.79 714.5,-1003.64 724.25,-1007.77 724.7,-1000.79"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge58" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1742.42,-1076.48C1851.22,-1077.58 2178.63,-1075.89 2264.04,-1017.78 2360.55,-952.12 2435.04,-583.59 2435.04,-553.16 2435.04,-553.16 2435.04,-553.16 2435.04,-468.91 2435.04,-364.56 2297.13,-317.04 2214.2,-297.9"/>
<polygon fill="blue" stroke="black" points="2215.13,-294.52 2204.6,-295.77 2213.61,-301.36 2215.13,-294.52"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge59" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1524.04,-551.16C1501.42,-454.76 1628.02,-481.78 1697.04,-410.78 1745.45,-360.98 1838.47,-338.34 1792.04,-286.69"/>
<path fill="none" stroke="black" d="M1792.04,-284.69C1739.19,-207.29 1605.1,-291.08 1600.04,-197.5"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_utils -->
<g id="edge60" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1703.05,-1053.54C1692.7,-997.75 1662.62,-848.43 1617.04,-730.53 1584.94,-647.51 1543.15,-640.09 1524.04,-553.16"/>
<path fill="none" stroke="black" d="M1524.04,-551.16C1506.34,-507.83 1485.82,-486.84 1510.04,-446.78 1511.25,-444.78 1554.22,-422.44 1590.23,-403.96"/>
<polygon fill="blue" stroke="black" points="1591.52,-407.24 1598.82,-399.56 1588.32,-401.01 1591.52,-407.24"/>
</g>
<!-- plugginger_api_depends -->
<g id="node27" class="node">
<title>plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75e505" stroke="black" cx="964.04" cy="-1932.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="964.04" y="-1942.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="964.04" y="-1929.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="964.04" y="-1916.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">depends</text>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge61" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M539.04,-1701.82C520.57,-1691.8 526.79,-1678.51 515.04,-1661.1 493.81,-1629.65 469.29,-1630.82 463.04,-1593.4"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge62" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M539.04,-1701.82C520.57,-1691.8 530.13,-1675.71 515.04,-1661.1 491.02,-1637.87 469.76,-1651.31 449.04,-1625.1 362.12,-1515.15 398.08,-1457.32 366.04,-1320.88 330.91,-1171.33 368.68,-1118.71 299.04,-981.78 261.04,-907.07 171.04,-927.1 171.04,-843.28 171.04,-843.28 171.04,-843.28 171.04,-752.66 171.04,-561.82 181.96,-489.52 307.04,-345.38 345.16,-301.44 378.9,-323.39 424.04,-286.69"/>
<path fill="none" stroke="black" d="M424.04,-284.69C456.07,-256.65 565.95,-227.92 637.88,-211.44"/>
<polygon fill="#75e505" stroke="black" points="638.62,-214.86 647.6,-209.24 637.08,-208.03 638.62,-214.86"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge63" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M956.52,-1900.01C949.32,-1872.11 937.98,-1834.26 927.04,-1823.26"/>
<path fill="none" stroke="black" d="M927.04,-1821.26C912.22,-1806.36 920.7,-1791.91 903.04,-1780.54 775.26,-1698.3 690.82,-1826.78 563.04,-1744.54 545.37,-1733.17 557.5,-1713.84 539.04,-1703.82"/>
<path fill="none" stroke="black" d="M539.04,-1701.82C520.57,-1691.8 531.29,-1674.4 515.04,-1661.1 477.18,-1630.13 456.86,-1642.22 411.04,-1625.1 409.89,-1624.68 408.74,-1624.24 407.59,-1623.81"/>
<polygon fill="#75e505" stroke="black" points="409.04,-1620.62 398.45,-1620.31 406.54,-1627.15 409.04,-1620.62"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api -->
<g id="edge64" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M927.04,-1821.26C911.37,-1805.51 917.03,-1792.06 898.04,-1780.54 798.66,-1720.29 735.72,-1809.02 639.04,-1744.54 621.56,-1732.88 626.94,-1721.13 615.04,-1703.82"/>
<path fill="none" stroke="black" d="M615.04,-1701.82C587.79,-1662.21 633.1,-1135.16 651.04,-1053.78 652.87,-1045.48 655.65,-1036.7 658.52,-1028.72"/>
<polygon fill="#75e505" stroke="black" points="661.73,-1030.13 662.02,-1019.53 655.19,-1027.64 661.73,-1030.13"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_builder -->
<g id="edge65" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M968.22,-1899.68C970.75,-1866.07 969.17,-1813.58 941.04,-1780.54 907.14,-1740.73 859.84,-1786.79 829.04,-1744.54 724.96,-1601.77 779.57,-1467.87 917.04,-1356.88 982.17,-1304.29 1231.98,-1379.2 1292.04,-1320.88 1340.61,-1273.71 1261.03,-1215.37 1311.04,-1169.74"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_plugin -->
<g id="edge66" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M615.04,-1701.82C609.02,-1691.68 665.04,-1291.96 665.04,-1280.16 665.04,-1280.16 665.04,-1280.16 665.04,-1167.74 665.04,-1079.03 1088.13,-597.91 1144.04,-529.03 1151.33,-520.04 1159.5,-510.51 1167.13,-501.83"/>
<polygon fill="#75e505" stroke="black" points="1169.51,-504.42 1173.52,-494.61 1164.27,-499.78 1169.51,-504.42"/>
</g>
<!-- plugginger_api_events&#45;&gt;plugginger_api -->
<g id="edge67" class="edge">
<title>plugginger_api_events&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1015.86,-1372.76C953.92,-1321.08 809.32,-1196.9 703.04,-1077.91"/>
<path fill="none" stroke="black" d="M703.04,-1075.91C690.71,-1062.88 682.53,-1044.49 677.39,-1029.17"/>
<polygon fill="#5c8a2e" stroke="black" points="680.75,-1028.18 674.48,-1019.64 674.05,-1030.23 680.75,-1028.18"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge68" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1159.87,-472.68C1106.96,-476.61 1005,-489.16 929.04,-529.03 856.77,-566.97 456.61,-983.55 415.04,-1053.78 354.49,-1156.06 347.34,-1215.37 402.04,-1320.88 410.02,-1336.28 422.48,-1350 435.5,-1361.43"/>
<polygon fill="blue" stroke="black" points="433.01,-1363.91 442.93,-1367.64 437.5,-1358.54 433.01,-1363.91"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge69" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1220.04,-284.69C1187.76,-239.41 1226.91,-178.36 1259.93,-139.7"/>
<polygon fill="blue" stroke="black" points="1262.31,-142.29 1266.28,-132.47 1257.05,-137.67 1262.31,-142.29"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge70" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1199.46,-446.55C1203.54,-428.01 1210.44,-401.21 1220.04,-379.08"/>
<path fill="none" stroke="black" d="M1220.04,-377.08C1236.02,-340.22 1243.35,-319.4 1220.04,-286.69"/>
<path fill="none" stroke="black" d="M1220.04,-284.69C1168.54,-212.45 895.75,-199.67 768.5,-197.68"/>
<polygon fill="blue" stroke="black" points="768.58,-194.19 758.53,-197.55 768.48,-201.19 768.58,-194.19"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge71" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1181.37,-493.34C1166.26,-520.32 1144.04,-567.46 1144.04,-611.28 1144.04,-672.41 1144.04,-672.41 1144.04,-672.41 1144.04,-720.76 1170.72,-770.32 1192.87,-803.13"/>
<polygon fill="blue" stroke="black" points="1189.82,-804.88 1198.4,-811.1 1195.57,-800.89 1189.82,-804.88"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge72" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1230.49,-491.41C1232.01,-492 1233.53,-492.55 1235.04,-493.03 1364.11,-534.63 1406.88,-494.54 1538.04,-529.03 1642.85,-556.6 1757.87,-610.1 1823.08,-642.8"/>
<polygon fill="blue" stroke="black" points="1821.25,-645.8 1831.76,-647.18 1824.41,-639.55 1821.25,-645.8"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge73" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1159.83,-470.6C1097.28,-471.43 964.14,-479.46 866.04,-529.03 591.22,-667.9 587.91,-795.32 375.04,-1017.78 324.35,-1070.75 287.62,-1068.11 260.04,-1136.03 194.92,-1296.39 246.6,-1358.13 297.04,-1523.69 300.01,-1533.44 304.36,-1543.45 308.99,-1552.69"/>
<polygon fill="blue" stroke="black" points="305.81,-1554.17 313.57,-1561.38 312.01,-1550.91 305.81,-1554.17"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api -->
<g id="edge74" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1159.77,-478.22C1109.46,-489.13 1020.17,-510.37 993.04,-529.03 867.97,-615.02 848.8,-668.08 803.04,-812.78 786.25,-865.86 820.15,-902.02 787.14,-937.27"/>
<polygon fill="blue" stroke="black" points="785.1,-934.39 780.15,-943.75 789.86,-939.52 785.1,-934.39"/>
<path fill="none" stroke="black" d="M779.04,-945.78C759.39,-961.82 734.09,-974.5 712.81,-983.43"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin -->
<g id="edge76" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1230.24,-464.89C1270.36,-460.35 1337.89,-452.8 1396.04,-446.78 1561.26,-429.7 1606.04,-447.51 1768.04,-410.78 1781.63,-407.7 1796,-403.07 1809.06,-398.29"/>
<polygon fill="blue" stroke="black" points="1810.14,-401.62 1818.25,-394.81 1807.66,-395.08 1810.14,-401.62"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge77" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1230.28,-491.68C1323.25,-546.77 1569.56,-694.81 1593.04,-730.53 1620.46,-772.27 1612.04,-791.34 1612.04,-841.28 1612.04,-945.78 1612.04,-945.78 1612.04,-945.78 1612.04,-986.04 1642.06,-1022.16 1668.35,-1046.07"/>
<polygon fill="blue" stroke="black" points="1665.75,-1048.44 1675.58,-1052.4 1670.36,-1043.18 1665.75,-1048.44"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge78" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1230.34,-465.02C1343.79,-452.5 1692.03,-413.87 1697.04,-410.78 1726.65,-392.53 1710.66,-364.01 1740.04,-345.38 1797.25,-309.09 1991.42,-294.5 2091.81,-289.27"/>
<polygon fill="blue" stroke="black" points="2091.96,-292.77 2101.77,-288.77 2091.6,-285.77 2091.96,-292.77"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge79" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1220.04,-377.08C1294.37,-205.71 1577.27,-382.91 1600.04,-197.5"/>
<path fill="none" stroke="black" d="M1600.04,-195.5C1598.98,-175.98 1598.92,-154.13 1599.16,-136.51"/>
<polygon fill="blue" stroke="black" points="1602.65,-136.64 1599.33,-126.58 1595.65,-136.52 1602.65,-136.64"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_stubgen -->
<g id="edge80" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1159.8,-464.25C1085.67,-453.61 917.89,-424.87 887.04,-379.08"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_testing_helpers -->
<g id="edge81" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1230.25,-465C1270.38,-460.55 1337.92,-453.09 1396.04,-446.78 1470.91,-438.66 1668.67,-449.9 1733.04,-410.78 1747.48,-402.01 1737.26,-381.17 1754.04,-379.08"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_api -->
<g id="edge82" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1131.54,-1373.03C1069.87,-1325.32 928.29,-1217.64 803.04,-1136.03 759.96,-1107.97 738.36,-1115.25 703.04,-1077.91"/>
</g>
<!-- plugginger_cli -->
<g id="node31" class="node">
<title>plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2139.66,-36 2058.41,-36 2058.41,0 2139.66,0 2139.66,-36"/>
<text text-anchor="middle" x="2099.04" y="-14.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.cli</text>
</g>
<!-- plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli -->
<g id="edge83" class="edge">
<title>plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2203.14,-271.44C2335.29,-235.89 2681.12,-138.65 2650.04,-102.5"/>
<path fill="none" stroke="black" d="M2650.04,-100.5C2630.59,-54.59 2284.86,-29.76 2151.19,-21.84"/>
<polygon fill="blue" stroke="black" points="2151.76,-18.36 2141.57,-21.28 2151.35,-25.35 2151.76,-18.36"/>
</g>
<!-- plugginger_cli_cmd_project_run -->
<g id="node33" class="node">
<title>plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2838.54,-308.81 2741.54,-308.81 2741.54,-262.56 2838.54,-262.56 2838.54,-308.81"/>
<text text-anchor="middle" x="2790.04" y="-295.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2790.04" y="-282.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="2790.04" y="-269.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_project_run</text>
</g>
<!-- plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli -->
<g id="edge84" class="edge">
<title>plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2771.83,-262.32C2735.67,-217.73 2657.66,-120.5 2650.04,-102.5"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli -->
<g id="edge85" class="edge">
<title>plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1658.74,-90.91C1757.05,-74.85 1953.38,-42.79 2047.02,-27.49"/>
<polygon fill="blue" stroke="black" points="2047.39,-30.98 2056.7,-25.92 2046.26,-24.07 2047.39,-30.98"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge86" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1675.82,-355.99C1684.21,-351.91 1693.26,-348.07 1702.04,-345.38 1837.12,-303.91 2003.75,-291.74 2091.79,-288.17"/>
<polygon fill="#75c823" stroke="black" points="2091.8,-291.67 2101.66,-287.8 2091.53,-284.68 2091.8,-291.67"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge87" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1685.85,-366.58C1720.29,-359.31 1768.33,-350.13 1811.04,-345.38 2215.96,-300.33 2327.13,-387.31 2727.04,-309.38 2728.06,-309.18 2729.09,-308.96 2730.12,-308.74"/>
<polygon fill="#75c823" stroke="black" points="2730.98,-312.13 2739.8,-306.26 2729.24,-305.35 2730.98,-312.13"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge88" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1625.57,-346.2C1611.65,-310.63 1592.87,-250.3 1600.04,-197.5"/>
</g>
<!-- plugginger_config -->
<g id="node36" class="node">
<title>plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75e505" stroke="black" cx="2403.04" cy="-1702.82" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2403.04" y="-1706.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2403.04" y="-1693.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge89" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2399.39,-1678.88C2394.66,-1655.29 2384.47,-1618.55 2364.04,-1593.4"/>
<path fill="none" stroke="black" d="M2364.04,-1591.4C2285.35,-1494.5 1913.75,-1434.03 1740.44,-1410.64"/>
<polygon fill="#75e505" stroke="black" points="1740.94,-1407.18 1730.56,-1409.32 1740.01,-1414.12 1740.94,-1407.18"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge90" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2440.04,-1591.4C2441.82,-1341.7 2668.04,-1327.61 2668.04,-1077.91 2668.04,-1077.91 2668.04,-1077.91 2668.04,-841.28 2668.04,-664.91 2678.84,-614.75 2625.04,-446.78 2614.51,-413.92 2593.76,-412.92 2587.04,-379.08"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge91" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2364.04,-1591.4C2353.38,-1577.3 2351.24,-1573.36 2340.04,-1559.69 2317.95,-1532.74 2307.72,-1529.76 2288.04,-1501.01"/>
<path fill="none" stroke="black" d="M2288.04,-1499.01C2231.25,-1416.04 2203.7,-1398.19 2112.04,-1356.88 1980.6,-1297.65 1809.04,-1283.8 1719.12,-1280.78"/>
<polygon fill="#75e505" stroke="black" points="1719.38,-1277.29 1709.28,-1280.49 1719.18,-1284.29 1719.38,-1277.29"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge92" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2415.82,-1679.72C2426.93,-1658.32 2441.08,-1624.61 2440.04,-1593.4"/>
<path fill="none" stroke="black" d="M2440.04,-1591.4C2438.77,-1553.48 2402.1,-1558.93 2388.04,-1523.69 2347.73,-1422.68 2393.15,-1384.95 2364.04,-1280.16"/>
<path fill="none" stroke="black" d="M2364.04,-1278.16C2323.89,-1151.07 2305.02,-1122.2 2250.04,-1000.78"/>
<path fill="none" stroke="black" d="M2250.04,-998.78C2226.39,-965.39 2222.42,-946.37 2236.04,-907.78 2248.29,-873.06 2282.89,-879.75 2288.04,-843.28"/>
<path fill="none" stroke="black" d="M2288.04,-841.28C2292.19,-762.75 2221.1,-764.22 2150.04,-730.53 2080.15,-697.41 1991.34,-683.01 1934.1,-676.85"/>
<polygon fill="#75e505" stroke="black" points="1934.54,-673.38 1924.24,-675.85 1933.83,-680.34 1934.54,-673.38"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_app -->
<g id="edge93" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2364.04,-1278.16C2353.01,-1188.25 2413.07,-1167.82 2402.04,-1077.91"/>
<path fill="none" stroke="black" d="M2402.04,-1075.91C2393.97,-1042.2 2364.73,-1049.18 2350.04,-1017.78 2316.85,-946.88 2315.1,-920.8 2326.04,-843.28"/>
<path fill="none" stroke="black" d="M2326.04,-841.28C2317.14,-766.76 2380.36,-724.2 2326.04,-672.41"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_builder -->
<g id="edge94" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2288.04,-1499.01C2214.44,-1390.1 2255.58,-1315.77 2150.04,-1237.44 2060.35,-1170.88 1933.67,-1278.44 1908.04,-1169.74"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge95" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2402.04,-1075.91C2394.06,-997.18 2609.36,-430.29 2549.04,-379.08"/>
<path fill="none" stroke="black" d="M2549.04,-377.08C2498.44,-335.28 2312.08,-306.48 2214.12,-293.87"/>
<polygon fill="#75e505" stroke="black" points="2214.78,-290.43 2204.42,-292.64 2213.9,-297.37 2214.78,-290.43"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge96" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2423.39,-1680.79C2491.32,-1609.69 2706.04,-1377.29 2706.04,-1280.16 2706.04,-1280.16 2706.04,-1280.16 2706.04,-907.78 2706.04,-822.94 2793.66,-835.3 2820.04,-754.66"/>
<path fill="none" stroke="black" d="M2820.04,-752.66C2862.16,-574.8 2962.84,-506.76 2877.04,-345.38 2870.42,-332.94 2859.87,-322.66 2848.35,-314.37"/>
<polygon fill="#75e505" stroke="black" points="2850.5,-311.59 2840.23,-308.98 2846.63,-317.43 2850.5,-311.59"/>
</g>
<!-- plugginger_config_models -->
<g id="node37" class="node">
<title>plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#70db05" stroke="black" cx="2327.04" cy="-1822.26" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2327.04" y="-1831.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2327.04" y="-1819.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config.</text>
<text text-anchor="middle" x="2327.04" y="-1806.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">models</text>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge97" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2308.52,-1791.63C2284.75,-1755.88 2240.29,-1696.22 2188.04,-1661.1 2146.74,-1633.35 2122.16,-1654.52 2082.04,-1625.1 2052.95,-1603.78 2059.45,-1585.28 2034.04,-1559.69 1990.31,-1515.66 1977.7,-1503.75 1922.04,-1476.32 1863.46,-1447.45 1792.25,-1427.82 1737.53,-1415.65"/>
<polygon fill="#70db05" stroke="black" points="1738.45,-1412.27 1727.94,-1413.56 1736.96,-1419.11 1738.45,-1412.27"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge98" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2357.62,-1796.34C2365.06,-1790.81 2373.15,-1785.21 2381.04,-1780.54 2414.59,-1760.68 2429.49,-1767.45 2461.04,-1744.54 2480.82,-1730.17 2485.02,-1724.52 2498.04,-1703.82"/>
<path fill="none" stroke="black" d="M2498.04,-1701.82C2504.71,-1691.21 2505.35,-1488.84 2506.04,-1476.32 2532.71,-988.06 2645.52,-864.56 2587.04,-379.08"/>
<path fill="none" stroke="black" d="M2587.04,-377.08C2565.4,-227.12 2698.9,-329.6 2212.04,-262 2067.93,-241.99 1039.54,-208.2 768.42,-199.56"/>
<polygon fill="#70db05" stroke="black" points="768.83,-196.07 758.72,-199.25 768.61,-203.07 768.83,-196.07"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge99" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2330.39,-1789.19C2332.01,-1765.34 2332.43,-1732.16 2326.04,-1703.82"/>
<path fill="none" stroke="black" d="M2326.04,-1701.82C2317.09,-1662.13 2282.02,-1675.68 2244.04,-1661.1 2188.47,-1639.77 2163.11,-1660.2 2115.04,-1625.1 2086.94,-1604.59 2095.14,-1585.71 2072.04,-1559.69 1965.62,-1439.87 1797.11,-1347.38 1711.61,-1305.2"/>
<polygon fill="#70db05" stroke="black" points="1713.23,-1302.1 1702.71,-1300.85 1710.16,-1308.39 1713.23,-1302.1"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge100" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2326.04,-1701.82C2318.4,-1671.17 2329.2,-1590.86 2324.04,-1559.69 2308.53,-1466.18 2291.73,-1445.48 2258.04,-1356.88 2246.01,-1325.26 2136.04,-1111.74 2136.04,-1077.91 2136.04,-1077.91 2136.04,-1077.91 2136.04,-841.28 2136.04,-745.35 2010.01,-701.14 1933.44,-683.13"/>
<polygon fill="#70db05" stroke="black" points="1934.52,-679.79 1923.99,-681 1932.98,-686.62 1934.52,-679.79"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_app -->
<g id="edge101" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2498.04,-1701.82C2526.91,-1655.91 2531.44,-1642.7 2554.04,-1593.4"/>
<path fill="none" stroke="black" d="M2554.04,-1591.4C2567.4,-1562.24 2540.1,-1553.8 2529.04,-1523.69 2489.3,-1415.52 2440.04,-1395.4 2440.04,-1280.16 2440.04,-1280.16 2440.04,-1280.16 2440.04,-1167.74 2440.04,-1115.98 2431.62,-1103.14 2416.04,-1053.78 2385.4,-956.76 2329.77,-944.96 2326.04,-843.28"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_builder -->
<g id="edge102" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2326.04,-1701.82C2311.75,-1639.37 2327.82,-1618.34 2302.04,-1559.69 2293.88,-1541.14 2285.56,-1540.37 2274.04,-1523.69 2213.95,-1436.79 2195.03,-1416.47 2150.04,-1320.88 2141.89,-1303.56 2147.51,-1295.47 2136.04,-1280.16"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge103" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2371.21,-1806.72C2409.7,-1793.18 2466.36,-1771.12 2512.04,-1744.54 2734.49,-1615.09 2858.04,-1537.54 2858.04,-1280.16 2858.04,-1280.16 2858.04,-1280.16 2858.04,-1167.74 2858.04,-1052.13 2897.95,-1019.18 2867.04,-907.78 2857.87,-874.76 2575.46,-400.91 2549.04,-379.08"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge104" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2554.04,-1591.4C2595.93,-1517.83 2628.62,-1514.15 2670.04,-1440.32 2697.87,-1390.71 2696.38,-1374.26 2716.04,-1320.88 2735.48,-1268.08 2744.77,-1256.12 2758.04,-1201.44 2805.3,-1006.62 2750.97,-942.86 2820.04,-754.66"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_config -->
<g id="edge105" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2346.21,-1791.62C2357.42,-1774.31 2371.54,-1752.48 2382.91,-1734.92"/>
<polygon fill="#70db05" stroke="black" points="2385.59,-1737.23 2388.08,-1726.93 2379.71,-1733.43 2385.59,-1737.23"/>
</g>
<!-- plugginger_core -->
<g id="node38" class="node">
<title>plugginger_core</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="917.04" cy="-2135.5" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="917.04" y="-2138.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="917.04" y="-2126" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge106" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M578.04,-1821.26C583.89,-1802.17 580.59,-1791.87 597.04,-1780.54 688.97,-1717.24 1008.04,-1810.57 1098.04,-1744.54 1130.19,-1720.95 1099.9,-1686.02 1131.04,-1661.1 1211.47,-1596.72 1274.05,-1681.86 1360.04,-1625.1 1388.67,-1606.2 1382.12,-1588.32 1401.04,-1559.69 1425.92,-1522.03 1423.7,-1504.41 1459.04,-1476.32 1491.01,-1450.91 1532.48,-1432.93 1568.7,-1420.8"/>
<polygon fill="#85f910" stroke="black" points="1569.38,-1424.26 1577.82,-1417.84 1567.22,-1417.6 1569.38,-1424.26"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge107" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M315.04,-1931.68C298.28,-1831.52 345.06,-1804.54 332.04,-1703.82"/>
<path fill="none" stroke="black" d="M332.04,-1701.82C324.67,-1655.02 275.77,-1669.03 258.04,-1625.1 247.15,-1598.15 241.14,-1583.35 258.04,-1559.69 286.71,-1519.55 321.08,-1549.65 363.04,-1523.69 400.5,-1500.52 435.54,-1464.34 459.12,-1437.02"/>
<polygon fill="#85f910" stroke="black" points="461.58,-1439.53 465.38,-1429.64 456.24,-1435 461.58,-1439.53"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge108" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M867.59,-2132.44C684.9,-2124.63 56.99,-2095.77 26.04,-2066.79 -17.59,-2025.95 7.04,-1993.44 7.04,-1933.68 7.04,-1933.68 7.04,-1933.68 7.04,-1499.01 7.04,-1328.39 55.57,-1288.56 132.04,-1136.03 151.86,-1096.5 155.28,-1083.49 188.04,-1053.78 224.43,-1020.78 270.41,-1047.68 285.04,-1000.78"/>
<path fill="none" stroke="black" d="M285.04,-998.78C293.1,-958.4 241.7,-970.87 209.04,-945.78"/>
<path fill="none" stroke="black" d="M209.04,-944.78C164.34,-910.46 133.04,-899.64 133.04,-843.28 133.04,-843.28 133.04,-843.28 133.04,-752.66 133.04,-690.27 143.45,-673.8 133.04,-612.28"/>
<path fill="none" stroke="black" d="M133.04,-611.28C126.92,-581.03 103.95,-582.71 95.04,-553.16"/>
<path fill="none" stroke="black" d="M95.04,-551.16C49.39,-399.8 27.69,-315.38 133.04,-197.5"/>
<path fill="none" stroke="black" d="M133.04,-195.5C214.95,-121.63 999.46,-106.14 1231.62,-103.16"/>
<polygon fill="#85f910" stroke="black" points="1231.43,-106.66 1241.39,-103.04 1231.34,-99.66 1231.43,-106.66"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge109" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M133.04,-611.28C113.17,-483.52 161.5,-438.65 251.04,-345.38 306.29,-287.81 335.08,-289.35 410.04,-262 431.27,-254.25 558.42,-227.36 638.09,-210.84"/>
<polygon fill="#85f910" stroke="black" points="638.44,-214.34 647.52,-208.89 637.02,-207.49 638.44,-214.34"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_graph -->
<g id="edge110" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M803.04,-2033.09C788.89,-1986.51 859.45,-1981.24 849.04,-1933.68"/>
<path fill="none" stroke="black" d="M849.04,-1931.68C844.08,-1909.06 835.98,-1884.59 828.62,-1864.63"/>
<polygon fill="#85f910" stroke="black" points="831.96,-1863.58 825.16,-1855.46 825.41,-1866.05 831.96,-1863.58"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_proxy -->
<g id="edge111" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M285.04,-998.78C296.58,-960.48 285.04,-948.78 285.04,-908.78 285.04,-908.78 285.04,-908.78 285.04,-841.28 285.04,-790.92 288.53,-776.54 309.04,-730.53 348.61,-641.74 407.07,-645.64 437.04,-553.16"/>
<path fill="none" stroke="black" d="M437.04,-551.16C448.05,-513.26 484.93,-509.11 475.04,-470.91"/>
<path fill="none" stroke="black" d="M475.04,-468.91C469.66,-448.16 458.18,-427.2 447.48,-410.75"/>
<polygon fill="#85f910" stroke="black" points="450.66,-409.21 442.16,-402.88 444.86,-413.12 450.66,-409.21"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge112" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M578.04,-1821.26C583.92,-1802.17 580.56,-1791.83 597.04,-1780.54 695.15,-1713.31 1018.51,-1783.06 1131.04,-1744.54 1193.88,-1723.03 1194.48,-1688.41 1255.04,-1661.1 1312.82,-1635.04 1332.52,-1643.97 1393.04,-1625.1 1406.46,-1620.91 1420.88,-1615.88 1434.03,-1611.08"/>
<polygon fill="#85f910" stroke="black" points="1435.15,-1614.4 1443.32,-1607.64 1432.72,-1607.83 1435.15,-1614.4"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge113" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M315.04,-2033.09C278.89,-2007.68 321.73,-1977.35 315.04,-1933.68"/>
<path fill="none" stroke="black" d="M315.04,-1931.68C289.52,-1765.23 170.92,-1711.87 243.04,-1559.69 253.86,-1536.86 269.63,-1542.01 287.04,-1523.69 350.51,-1456.94 328.48,-1399.69 410.04,-1356.88 522.93,-1297.61 1432.26,-1347.12 1557.04,-1320.88 1573.27,-1317.46 1590.24,-1311.61 1605.54,-1305.38"/>
<polygon fill="#85f910" stroke="black" points="1606.58,-1308.74 1614.44,-1301.63 1603.86,-1302.29 1606.58,-1308.74"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge114" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M578.04,-1821.26C583.92,-1802.17 580.48,-1791.71 597.04,-1780.54 660.32,-1737.86 1207.24,-1753.57 1283.04,-1744.54 1336.56,-1738.17 1396.73,-1726.59 1440.28,-1717.4"/>
<polygon fill="#85f910" stroke="black" points="1441.01,-1720.82 1450.06,-1715.32 1439.55,-1713.98 1441.01,-1720.82"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge115" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M867.29,-2134.25C742.85,-2132.78 421.78,-2123.44 334.04,-2066.79 320.23,-2057.88 328.47,-2044.54 315.04,-2035.09"/>
<path fill="none" stroke="black" d="M315.04,-2033.09C260.04,-1994.43 242.82,-1986.34 201.04,-1933.68"/>
<path fill="none" stroke="black" d="M201.04,-1931.68C195.01,-1924.09 149.28,-1873.11 146.04,-1863.98 139.62,-1845.89 147.42,-1674.3 150.04,-1661.1 191.04,-1454.58 221.79,-1394.48 362.04,-1237.44 365,-1234.12 481.95,-1137.8 486.04,-1136.03 596.7,-1088.03 636.79,-1123.84 755.04,-1100.03 763.61,-1098.31 1051.56,-1022.33 1059.04,-1017.78 1115.21,-983.63 1113.74,-956.43 1159.04,-908.78"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge116" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M159.04,-2033.09C128.63,-2010.6 142.15,-1901.74 140.04,-1863.98 127.31,-1636.82 116.58,-1564.31 210.04,-1356.88 263.82,-1237.5 285.07,-1188.52 405.04,-1136.03 532.92,-1080.09 594.47,-1166.83 717.04,-1100.03 740.4,-1087.3 734.06,-1070.15 755.04,-1053.78 843.43,-984.8 885.76,-1001.54 983.04,-945.78 1074.05,-893.61 1081.03,-852.72 1178.04,-812.78 1259.53,-779.23 1287.86,-799.42 1373.04,-776.78 1433.11,-760.82 1444.75,-745.68 1505.04,-730.53 1614.12,-703.12 1744.67,-686.26 1818.21,-678.17"/>
<polygon fill="#85f910" stroke="black" points="1818.44,-681.67 1828.01,-677.12 1817.69,-674.71 1818.44,-681.67"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge117" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M332.04,-1701.82C329.13,-1679.35 328.96,-1654.04 329.59,-1633.53"/>
<polygon fill="#85f910" stroke="black" points="333.08,-1633.7 329.98,-1623.57 326.09,-1633.43 333.08,-1633.7"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation_name_validation -->
<g id="edge118" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation_name_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M675.04,-1931.68C662.83,-1915.86 660.74,-1894.52 662.49,-1874.95"/>
<polygon fill="#85f910" stroke="black" points="665.94,-1875.55 663.72,-1865.19 658.99,-1874.67 665.94,-1875.55"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation_plugin_validation -->
<g id="edge119" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation_plugin_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M578.04,-1821.26C583.92,-1802.17 580.87,-1792.26 597.04,-1780.54 651.04,-1741.39 829.94,-1759.09 895.04,-1744.54 912.58,-1740.62 931.15,-1735.18 948.41,-1729.55"/>
<polygon fill="#85f910" stroke="black" points="949.15,-1732.99 957.53,-1726.51 946.93,-1726.35 949.15,-1732.99"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app -->
<g id="edge120" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M121.04,-1499.01C139.9,-1375.66 152.44,-1340.33 223.04,-1237.44 236.02,-1218.52 246.02,-1219.61 260.04,-1201.44 280.43,-1174.99 270.42,-1156.22 297.04,-1136.03 350.29,-1095.65 378.52,-1117.49 443.04,-1100.03 567.83,-1066.27 602.88,-1067.93 722.04,-1017.78 844.54,-966.23 868.55,-939.31 983.04,-871.78 1025.85,-846.53 1034.67,-837.06 1078.04,-812.78 1149.19,-772.95 1166.15,-760.38 1242.04,-730.53 1295.04,-709.68 1310.89,-712.2 1365.04,-694.53 1491.29,-653.33 1638.05,-598.52 1710.98,-570.81"/>
<polygon fill="#85f910" stroke="black" points="1712.1,-574.13 1720.2,-567.3 1709.61,-567.59 1712.1,-574.13"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app_plugin -->
<g id="edge121" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M913.5,-2111.68C910.36,-2082.91 909.49,-2033.36 934.04,-2001.39 959.77,-1967.87 998.48,-2000.47 1022.04,-1965.39 1038.24,-1941.25 1030.57,-1927.77 1022.04,-1899.98 1003.21,-1838.67 992.42,-1818.93 941.04,-1780.54 898.81,-1748.99 867.77,-1779.18 828.04,-1744.54 783.7,-1705.89 784.02,-1682.47 771.04,-1625.1 744.06,-1505.96 770.15,-1462.78 831.04,-1356.88 926.24,-1191.3 999.62,-1183.24 1095.04,-1017.78 1144.14,-932.63 1120.59,-892.55 1178.04,-812.78 1291.47,-655.28 1345.44,-631.91 1510.04,-529.03 1562.77,-496.07 1727.98,-429.16 1809.13,-397.06"/>
<polygon fill="#85f910" stroke="black" points="1810.11,-400.43 1818.13,-393.51 1807.54,-393.92 1810.11,-400.43"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_background -->
<g id="edge122" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M201.04,-1931.68C109.29,-1793.94 156.91,-1713.49 218.04,-1559.69 225.83,-1540.07 237.27,-1541.23 249.04,-1523.69 295.41,-1454.6 282.98,-1422.58 334.04,-1356.88 383.63,-1293.06 457.22,-1234.12 503.63,-1200.06"/>
<polygon fill="#85f910" stroke="black" points="505.57,-1202.98 511.6,-1194.27 501.45,-1197.32 505.57,-1202.98"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_builder -->
<g id="edge123" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M922.56,-2111.46C930.57,-2082.46 947.95,-2032.67 979.04,-2001.39 1005.38,-1974.87 1035.05,-1996.97 1055.04,-1965.39 1070.58,-1940.82 1063.96,-1927.65 1055.04,-1899.98 1034.96,-1837.72 1021.38,-1819.76 969.04,-1780.54 930.56,-1751.71 894.8,-1783.78 867.04,-1744.54 784.94,-1628.5 840.93,-1468.97 988.04,-1356.88 1046.01,-1312.71 1242.95,-1336.99 1314.04,-1320.88 1423.76,-1296 1444.37,-1266.6 1553.04,-1237.44 1630.6,-1216.63 1925,-1248.24 1908.04,-1169.74"/>
<path fill="none" stroke="black" d="M1908.04,-1167.74C1890.49,-1104.98 1807.17,-1085.97 1753.46,-1080.27"/>
<polygon fill="#85f910" stroke="black" points="1753.94,-1076.8 1743.66,-1079.36 1753.29,-1083.77 1753.94,-1076.8"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_depends -->
<g id="edge124" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M803.04,-2033.09C795.33,-2007.71 861.43,-1974.78 911.28,-1953.94"/>
<polygon fill="#85f910" stroke="black" points="912.32,-1957.3 920.23,-1950.26 909.65,-1950.82 912.32,-1957.3"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_events -->
<g id="edge125" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M882.76,-2118.16C853.65,-2101.9 814.79,-2073.78 803.04,-2035.09"/>
<path fill="none" stroke="black" d="M803.04,-2033.09C782.1,-1964.17 719.05,-1990.7 675.04,-1933.68"/>
<path fill="none" stroke="black" d="M675.04,-1931.68C635.53,-1880.5 558.87,-1885.01 578.04,-1823.26"/>
<path fill="none" stroke="black" d="M578.04,-1821.26C597.81,-1757.55 482.01,-1802.52 449.04,-1744.54 430.7,-1712.31 428.74,-1692.14 449.04,-1661.1 512.19,-1564.49 853.84,-1455.78 989.99,-1415.62"/>
<polygon fill="#85f910" stroke="black" points="990.91,-1418.99 999.52,-1412.82 988.94,-1412.28 990.91,-1418.99"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_plugin -->
<g id="edge126" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M867.33,-2133.71C717.78,-2130.46 275.16,-2114.22 159.04,-2035.09"/>
<path fill="none" stroke="black" d="M159.04,-2033.09C112.64,-2001.48 106.02,-1575.2 121.04,-1501.01"/>
<path fill="none" stroke="black" d="M121.04,-1499.01C105.24,-1337.22 90.44,-1279.94 166.04,-1136.03 202.82,-1066.01 249.38,-1079.35 299.04,-1017.78 391.06,-903.7 364.75,-841.04 461.04,-730.53 500.67,-685.04 518.66,-681.48 569.04,-648.28 658.98,-589.01 678.02,-563.61 780.04,-529.03 908.49,-485.5 1069.94,-474.54 1148.21,-471.81"/>
<polygon fill="#85f910" stroke="black" points="1148.19,-475.31 1158.07,-471.5 1147.97,-468.31 1148.19,-475.31"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_service -->
<g id="edge127" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M578.04,-1821.26C648.47,-1594.78 829.59,-1663.7 1021.04,-1523.69 1059.24,-1495.76 1099.57,-1459.81 1127.45,-1433.8"/>
<polygon fill="#85f910" stroke="black" points="1129.69,-1436.5 1134.58,-1427.1 1124.89,-1431.39 1129.69,-1436.5"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_models -->
<g id="edge128" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M954.41,-2119.56C986.33,-2106.5 1033.25,-2086.57 1073.04,-2066.79 1106.84,-2049.99 1332.85,-1910.75 1369.04,-1899.98 1486.65,-1864.98 1797.75,-1874.09 1920.04,-1863.98 2043.48,-1853.77 2187.64,-1838.57 2266.99,-1829.92"/>
<polygon fill="#85f910" stroke="black" points="2267.15,-1833.42 2276.71,-1828.86 2266.39,-1826.47 2267.15,-1833.42"/>
</g>
<!-- plugginger_implementations_container -->
<g id="node44" class="node">
<title>plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#70a838" stroke="black" cx="1483.04" cy="-1932.68" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1483.04" y="-1942.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1483.04" y="-1929.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations.</text>
<text text-anchor="middle" x="1483.04" y="-1916.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">container</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_container -->
<g id="edge129" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M965.29,-2129.23C1030.41,-2121.08 1149.28,-2102.48 1245.04,-2066.79 1264.14,-2059.67 1365.32,-2001.85 1429.46,-1964.79"/>
<polygon fill="#85f910" stroke="black" points="1431.2,-1967.83 1438.1,-1959.79 1427.69,-1961.77 1431.2,-1967.83"/>
</g>
<!-- plugginger_implementations_events -->
<g id="node45" class="node">
<title>plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#619130" stroke="black" cx="1681.04" cy="-1592.4" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1681.04" y="-1602.02" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1681.04" y="-1589.27" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="1681.04" y="-1576.52" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_events -->
<g id="edge130" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M578.04,-1821.26C583.92,-1802.17 580.51,-1791.76 597.04,-1780.54 653.2,-1742.4 1140.51,-1758.12 1207.04,-1744.54 1314.81,-1722.54 1335.25,-1694.54 1440.04,-1661.1 1496.02,-1643.24 1560.1,-1625.36 1608.1,-1612.46"/>
<polygon fill="#85f910" stroke="black" points="1608.8,-1615.9 1617.56,-1609.93 1606.99,-1609.13 1608.8,-1615.9"/>
</g>
<!-- plugginger_implementations_services -->
<g id="node46" class="node">
<title>plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#669933" stroke="black" cx="1844.04" cy="-1592.4" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1844.04" y="-1602.02" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1844.04" y="-1589.27" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="1844.04" y="-1576.52" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_services -->
<g id="edge131" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M936.92,-2113.43C964.95,-2084.77 1018.75,-2033.38 1073.04,-2001.39 1112.29,-1978.25 1129.83,-1988.61 1169.04,-1965.39 1207.38,-1942.68 1208.88,-1926.04 1245.04,-1899.98 1329.03,-1839.44 1350.43,-1822.61 1445.04,-1780.54 1494.75,-1758.43 1511.82,-1765.49 1562.04,-1744.54 1645.46,-1709.74 1736.77,-1658.11 1792.23,-1625.11"/>
<polygon fill="#85f910" stroke="black" points="1793.74,-1628.28 1800.53,-1620.15 1790.15,-1622.28 1793.74,-1628.28"/>
</g>
<!-- plugginger_interfaces_events -->
<g id="node48" class="node">
<title>plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75bc2f" stroke="black" cx="1736.04" cy="-1932.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1736.04" y="-1942.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1736.04" y="-1929.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="1736.04" y="-1916.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_events -->
<g id="edge132" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M965.92,-2130.65C1072.68,-2121.4 1334.04,-2094.18 1545.04,-2035.09"/>
<path fill="none" stroke="black" d="M1545.04,-2033.09C1562.05,-2028.32 1555.01,-2012.14 1569.04,-2001.39 1601.64,-1976.4 1644.72,-1959.23 1678.96,-1948.44"/>
<polygon fill="#85f910" stroke="black" points="1679.75,-1951.86 1688.3,-1945.6 1677.71,-1945.16 1679.75,-1951.86"/>
</g>
<!-- plugginger_interfaces_services -->
<g id="node49" class="node">
<title>plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75bc2f" stroke="black" cx="1853.04" cy="-1932.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1853.04" y="-1942.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1853.04" y="-1929.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="1853.04" y="-1916.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_services -->
<g id="edge133" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1545.04,-2033.09C1562.05,-2028.32 1553.88,-2010.48 1569.04,-2001.39 1655.86,-1949.25 1698.09,-1997.81 1794.04,-1965.39 1798.25,-1963.96 1802.53,-1962.25 1806.75,-1960.38"/>
<polygon fill="#85f910" stroke="black" points="1808.19,-1963.57 1815.7,-1956.1 1805.17,-1957.25 1808.19,-1963.57"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_stubgen -->
<g id="edge134" class="edge">
<title>plugginger_core&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M209.04,-944.78C137.46,-884.85 361.04,-646.51 361.04,-553.16 361.04,-553.16 361.04,-553.16 361.04,-468.91 361.04,-413.36 340.29,-384.18 380.04,-345.38 434.55,-292.16 662.03,-285.73 772.84,-285.86"/>
<polygon fill="#85f910" stroke="black" points="772.63,-289.36 782.64,-285.9 772.65,-282.36 772.63,-289.36"/>
</g>
<!-- plugginger_core_config -->
<g id="node39" class="node">
<title>plugginger_core_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85e722" stroke="black" cx="2006.04" cy="-2034.09" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2006.04" y="-2043.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2006.04" y="-2030.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2006.04" y="-2018.21" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge135" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1992.07,-2002.39C1976.59,-1971.82 1948.73,-1925.84 1911.04,-1899.98 1800.39,-1824.07 1718.66,-1913.14 1619.04,-1823.26"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_api_app -->
<g id="edge136" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2147.04,-1931.68C2241.04,-1874.6 2289.38,-1918.23 2385.04,-1863.98 2501.37,-1798 2538.95,-1755.64 2568.04,-1625.1 2615.54,-1411.9 2488.39,-818.54 2326.04,-672.41"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_config_models -->
<g id="edge137" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2037.95,-2008.69C2065.99,-1987.72 2108.25,-1957.23 2147.04,-1933.68"/>
<path fill="none" stroke="black" d="M2147.04,-1931.68C2192.5,-1904.08 2244.56,-1872.75 2280.96,-1850.89"/>
<polygon fill="#85e722" stroke="black" points="2282.72,-1853.92 2289.49,-1845.77 2279.12,-1847.91 2282.72,-1853.92"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_implementations_events -->
<g id="edge138" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2007.24,-2001.26C2007.68,-1981.73 2007.72,-1956.23 2006.04,-1933.68"/>
</g>
<!-- plugginger_core_constants -->
<g id="node40" class="node">
<title>plugginger_core_constants</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="1302.04" cy="-1592.4" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1302.04" y="-1602.02" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1302.04" y="-1589.27" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1302.04" y="-1576.52" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">constants</text>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge139" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1330.38,-1565.42C1348.64,-1548.02 1372.37,-1524.15 1391.04,-1501.01"/>
<path fill="none" stroke="black" d="M1391.04,-1499.01C1508.91,-1352.85 1476.21,-1258.56 1425.04,-1077.91"/>
<path fill="none" stroke="black" d="M1425.04,-1075.91C1413.63,-1049.6 1403.42,-1045.74 1397.04,-1017.78 1393.47,-1002.18 1395.87,-997.74 1397.04,-981.78 1414.69,-740.7 1380,-659.39 1495.04,-446.78 1515.18,-409.55 1551.98,-420.2 1562.04,-379.08"/>
<path fill="none" stroke="black" d="M1562.04,-377.08C1558.58,-297.34 1624.49,-247.2 1562.04,-197.5"/>
<path fill="none" stroke="black" d="M1562.04,-195.5C1501.58,-150.75 1417.79,-126.21 1360.51,-113.74"/>
<polygon fill="#85f910" stroke="black" points="1361.41,-110.36 1350.9,-111.72 1359.96,-117.2 1361.41,-110.36"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge140" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1505.04,-1397.6C1537.82,-1366.02 1578.93,-1334.86 1610.27,-1312.59"/>
<polygon fill="#85f910" stroke="black" points="1612.27,-1315.46 1618.43,-1306.84 1608.24,-1309.74 1612.27,-1315.46"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_builder -->
<g id="edge141" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1391.04,-1499.01C1400.25,-1487.58 1404.11,-1486.12 1415.04,-1476.32 1454.15,-1441.2 1467.18,-1436.07 1505.04,-1399.6"/>
<path fill="none" stroke="black" d="M1505.04,-1397.6C1534.7,-1369.02 1536.01,-1356.3 1557.04,-1320.88 1578.33,-1285.01 1566.97,-1262.88 1600.04,-1237.44 1711.13,-1151.99 1940.44,-1306.1 1908.04,-1169.74"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_events -->
<g id="edge142" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1255.54,-1580.18C1219.86,-1569.87 1170.8,-1551.85 1135.04,-1523.69 1106.31,-1501.08 1082.71,-1466.83 1067.05,-1440.03"/>
<polygon fill="#85f910" stroke="black" points="1070.17,-1438.45 1062.19,-1431.48 1064.09,-1441.91 1070.17,-1438.45"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_plugin -->
<g id="edge143" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1425.04,-1075.91C1375.34,-862.61 1339.57,-815.56 1258.04,-612.28"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_service -->
<g id="edge144" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1254.16,-1583.38C1210.48,-1572.97 1152.23,-1549.96 1149.04,-1501.01"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_stubgen -->
<g id="edge145" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1303.77,-1559.45C1304.75,-1510.77 1299.87,-1417.31 1254.04,-1356.88 1232.9,-1329 1207,-1347.27 1184.04,-1320.88 895.92,-989.79 1132.27,-743.07 887.04,-379.08"/>
</g>
<!-- plugginger_core_exceptions -->
<g id="node41" class="node">
<title>plugginger_core_exceptions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="1326.04" cy="-2135.5" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1326.04" y="-2145.12" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1326.04" y="-2132.37" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1326.04" y="-2119.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">exceptions</text>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge146" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1343.81,-2104.79C1355.35,-2085.19 1370.51,-2058.85 1383.04,-2035.09"/>
<path fill="none" stroke="black" d="M1383.04,-2033.09C1403.64,-1994.01 1375.75,-1977.26 1383.04,-1933.68"/>
<path fill="none" stroke="black" d="M1383.04,-1931.68C1410.58,-1766.97 1350.73,-1703.26 1436.04,-1559.69 1469.95,-1502.61 1532.84,-1459.43 1581.88,-1432.39"/>
<polygon fill="#85f910" stroke="black" points="1583.33,-1435.58 1590.47,-1427.75 1580.01,-1429.42 1583.33,-1435.58"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge147" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1284.13,-2117.74C1222.51,-2090.27 1111.26,-2028.97 1079.04,-1933.68"/>
<path fill="none" stroke="black" d="M1079.04,-1931.68C1031.02,-1854.42 1021.94,-1822.12 941.04,-1780.54 820.81,-1718.75 755.54,-1813.09 639.04,-1744.54 610.26,-1727.61 455.64,-1533.56 463.04,-1501.01"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge148" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1308.62,-2104.6C1297.22,-2084.92 1282.09,-2058.56 1269.04,-2035.09"/>
<path fill="none" stroke="black" d="M1269.04,-2033.09C1244.19,-1988.41 1212.15,-2001.57 1176.04,-1965.39 1104.36,-1893.56 1128.27,-1834.02 1042.04,-1780.54 960.07,-1729.71 902.12,-1806.3 828.04,-1744.54 765.41,-1692.33 703.04,-1481.13 703.04,-1399.6 703.04,-1399.6 703.04,-1399.6 703.04,-1278.16 703.04,-1146.17 757.98,-1108.79 722.04,-981.78 647.47,-718.34 515.96,-708.5 347.04,-493.03 297.01,-429.22 94.16,-268.66 133.04,-197.5"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge149" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M881.04,-1701.82C844.25,-1639.72 845.06,-1125.39 836.04,-1053.78 829.88,-1004.92 831.36,-991.59 816.04,-944.78 756.84,-764 548.32,-464.09 468.04,-345.38 449.77,-318.37 399.5,-308.16 424.04,-286.69"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_graph -->
<g id="edge150" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1276.08,-2135.12C1161.73,-2133.45 887.83,-2110.76 849.04,-1933.68"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy -->
<g id="edge151" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1269.04,-2033.09C1191.04,-1895.76 1151.75,-1866.16 1019.04,-1780.54 980.39,-1755.61 960.78,-1769.3 922.04,-1744.54 900.4,-1730.71 895.59,-1724.98 881.04,-1703.82"/>
<path fill="none" stroke="black" d="M881.04,-1701.82C715.16,-1406.49 896.2,-1267.42 793.04,-944.78 772.42,-880.3 756.75,-867.6 717.04,-812.78 681.91,-764.3 569.47,-662.61 537.04,-612.28 499.87,-554.61 492.24,-537.33 475.04,-470.91"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge152" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1383.04,-1931.68C1385.75,-1915.48 1392.21,-1913.15 1402.04,-1899.98 1427.44,-1865.93 1446.09,-1863.72 1459.04,-1823.26"/>
<path fill="none" stroke="black" d="M1459.04,-1821.26C1469.74,-1787.8 1444.29,-1779.41 1440.04,-1744.54 1435.55,-1707.73 1430.26,-1696.87 1440.04,-1661.1 1442.79,-1651.04 1447.45,-1640.96 1452.65,-1631.76"/>
<polygon fill="#85f910" stroke="black" points="1455.49,-1633.84 1457.65,-1623.47 1449.49,-1630.23 1455.49,-1633.84"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge153" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1459.04,-1821.26C1466.14,-1799.06 1474.7,-1774.76 1482.23,-1754.06"/>
<polygon fill="#85f910" stroke="black" points="1485.5,-1755.29 1485.66,-1744.7 1478.93,-1752.89 1485.5,-1755.29"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge154" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1269.04,-2033.09C1229.07,-1947.15 1281.1,-1903.73 1231.04,-1823.26"/>
<path fill="none" stroke="black" d="M1231.04,-1821.26C1221.13,-1802.74 1224.54,-1792.15 1207.04,-1780.54 1106.51,-1713.87 1017.47,-1835.31 938.04,-1744.54 913.61,-1716.63 935.02,-1698.06 938.04,-1661.1 949.18,-1524.53 943.64,-1486.51 988.04,-1356.88 1021.71,-1258.56 1180.13,-1045.01 1211.04,-945.78 1217.25,-925.83 1220.08,-902.76 1221.33,-883.56"/>
<polygon fill="#85f910" stroke="black" points="1224.82,-883.84 1221.84,-873.68 1217.83,-883.49 1224.82,-883.84"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge155" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1276.72,-2130.35C1207.25,-2122.34 1079.45,-2099.37 993.04,-2035.09"/>
<path fill="none" stroke="black" d="M993.04,-2033.09C953.72,-2003.85 931.68,-2007.13 906.04,-1965.39 862.22,-1894.07 931.78,-1837.05 870.04,-1780.54 813.18,-1728.5 589.67,-1786.55 525.04,-1744.54 489.17,-1721.22 508.55,-1690.04 477.04,-1661.1 465.59,-1650.6 436.49,-1636.35 407.32,-1623.64"/>
<polygon fill="#85f910" stroke="black" points="409,-1620.55 398.43,-1619.81 406.23,-1626.98 409,-1620.55"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_name_validation -->
<g id="edge156" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_name_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1276.76,-2131.45C1175.03,-2124.46 945.63,-2104.87 878.04,-2066.79 867.03,-2060.6 767.49,-1938.14 712,-1869.29"/>
<polygon fill="#85f910" stroke="black" points="714.75,-1867.13 705.76,-1861.53 709.3,-1871.52 714.75,-1867.13"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_plugin_validation -->
<g id="edge157" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_plugin_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1231.04,-1821.26C1220.48,-1803.1 1222.94,-1794.26 1207.04,-1780.54 1188.61,-1764.64 1135.46,-1743.69 1090.21,-1727.7"/>
<polygon fill="#85f910" stroke="black" points="1091.52,-1724.45 1080.93,-1724.46 1089.21,-1731.06 1091.52,-1724.45"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app -->
<g id="edge158" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1269.04,-2033.09C1254.81,-2003.78 1318.14,-1776.99 1321.04,-1744.54 1324.34,-1707.6 1340.95,-1692.39 1321.04,-1661.1 1300.75,-1629.23 1264.32,-1656.97 1244.04,-1625.1 1228.43,-1600.58 1238.95,-1588.32 1244.04,-1559.69 1276.31,-1377.94 1387.04,-1354.34 1387.04,-1169.74 1387.04,-1169.74 1387.04,-1169.74 1387.04,-998.78 1387.04,-966.22 1484.41,-755.73 1505.04,-730.53 1535.66,-693.12 1648.63,-620.14 1712.42,-580.45"/>
<polygon fill="#85f910" stroke="black" points="1713.9,-583.65 1720.56,-575.4 1710.21,-577.7 1713.9,-583.65"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin -->
<g id="edge159" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1375.9,-2133.3C1528.34,-2129.18 1986.37,-2113.15 2127.04,-2066.79 2353.52,-1992.15 2435.94,-1950.56 2556.04,-1744.54 2672.83,-1544.19 2724.08,-1464.94 2679.04,-1237.44 2615.15,-914.77 2489.11,-868.95 2397.04,-553.16"/>
<path fill="none" stroke="black" d="M2397.04,-551.16C2363.32,-454.17 2026.57,-401.19 1901.49,-384.73"/>
<polygon fill="#85f910" stroke="black" points="1902.27,-381.3 1891.91,-383.49 1901.38,-388.25 1902.27,-381.3"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_background -->
<g id="edge160" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1079.04,-1931.68C1042.28,-1866.09 1063.5,-1825.23 1003.04,-1780.54 926.18,-1723.73 866.94,-1802.62 791.04,-1744.54 614.51,-1609.46 564.23,-1322.05 551.21,-1212.96"/>
<polygon fill="#85f910" stroke="black" points="554.71,-1212.8 550.1,-1203.26 547.76,-1213.6 554.71,-1212.8"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_builder -->
<g id="edge161" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1375.87,-2134.42C1521.21,-2133.5 1942,-2125.68 2064.04,-2066.79 2277.74,-1963.68 2291.06,-1789.26 2231.04,-1559.69 2215.65,-1500.83 2184.46,-1497.63 2164.04,-1440.32 2151.91,-1406.28 2156.88,-1309.67 2136.04,-1280.16"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_depends -->
<g id="edge162" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M993.04,-2033.09C975.71,-2020.2 968.26,-1997.53 965.23,-1977.09"/>
<polygon fill="#85f910" stroke="black" points="968.72,-1976.77 964.11,-1967.22 961.76,-1977.56 968.72,-1976.77"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_events -->
<g id="edge163" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1231.04,-1821.26C1220.1,-1803.32 1220.12,-1796.97 1207.04,-1780.54 1192.54,-1762.34 1185.04,-1761.42 1169.04,-1744.54 1135.54,-1709.2 1137.23,-1690 1098.04,-1661.1 1065.34,-1637 1044.63,-1651.82 1014.04,-1625.1 1001.66,-1614.3 999.6,-1609.18 995.04,-1593.4"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_plugin -->
<g id="edge164" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1231.04,-1821.26C1134.67,-1638.69 1303.38,-1546.18 1221.04,-1356.88 1211.88,-1335.84 1192.96,-1342.02 1184.04,-1320.88 1169.61,-1286.72 1179.95,-1274.3 1184.04,-1237.44 1202.37,-1071.9 1247.7,-1037.32 1266.04,-871.78 1272.15,-816.61 1267.11,-701.95 1258.04,-612.28"/>
<path fill="none" stroke="black" d="M1258.04,-611.28C1253.24,-571.78 1232.69,-530.72 1216.35,-503.29"/>
<polygon fill="#85f910" stroke="black" points="1219.33,-501.46 1211.12,-494.76 1213.37,-505.12 1219.33,-501.46"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_service -->
<g id="edge165" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1231.04,-1821.26C1224.28,-1809.63 1147.57,-1606.77 1149.04,-1593.4"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_container -->
<g id="edge166" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1383.04,-2033.09C1396.16,-2008.19 1417.67,-1985.6 1437.38,-1968.27"/>
<polygon fill="#85f910" stroke="black" points="1439.5,-1971.07 1444.83,-1961.92 1434.95,-1965.75 1439.5,-1971.07"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_events -->
<g id="edge167" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1375.61,-2132.79C1427.64,-2128.4 1508.73,-2113.83 1559.04,-2066.79 1622.3,-2007.64 1662.23,-1741.9 1675.83,-1636.59"/>
<polygon fill="#85f910" stroke="black" points="1679.26,-1637.3 1677.05,-1626.94 1672.32,-1636.42 1679.26,-1637.3"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_services -->
<g id="edge168" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1374.56,-2128.06C1502.99,-2109.9 1846.36,-2053.81 1911.04,-1965.39 1948.35,-1914.37 1907.32,-1886.45 1906.04,-1823.26"/>
</g>
<!-- plugginger_core_types -->
<g id="node42" class="node">
<title>plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="2083.04" cy="-2135.5" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2083.04" y="-2145.12" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2083.04" y="-2132.37" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2083.04" y="-2119.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">types</text>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge169" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2034.48,-2128.55C1948.9,-2115.4 1771.47,-2075.72 1678.04,-1965.39 1623.39,-1900.86 1664.99,-1858.82 1633.04,-1780.54 1609.7,-1723.37 1602.22,-1708 1562.04,-1661.1 1551.01,-1648.23 1537.34,-1635.88 1524.35,-1625.34"/>
<polygon fill="#85f910" stroke="black" points="1526.71,-1622.74 1516.69,-1619.28 1522.36,-1628.23 1526.71,-1622.74"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge170" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2125.41,-2118.23C2149.8,-2106.99 2179.41,-2089.9 2199.04,-2066.79 2240.05,-2018.51 2250.04,-1997.04 2250.04,-1933.68 2250.04,-1933.68 2250.04,-1933.68 2250.04,-1701.82 2250.04,-1638.09 2266.25,-1612.81 2231.04,-1559.69 2132.96,-1411.77 2048.51,-1430.54 1887.04,-1356.88 1830.71,-1331.18 1763.17,-1309.61 1716.1,-1295.94"/>
<polygon fill="#85f910" stroke="black" points="1717.22,-1292.62 1706.64,-1293.23 1715.29,-1299.35 1717.22,-1292.62"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge171" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1583.04,-2033.09C1508.08,-1975.35 1689.87,-1885.99 1619.04,-1823.26"/>
<path fill="none" stroke="black" d="M1619.04,-1821.26C1591.27,-1797.28 1562.04,-1768.22 1539.61,-1745"/>
<polygon fill="#85f910" stroke="black" points="1542.17,-1742.61 1532.72,-1737.82 1537.12,-1747.46 1542.17,-1742.61"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge172" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2132.75,-2134.74C2227.98,-2133.14 2440.15,-2119.52 2592.04,-2035.09"/>
<path fill="none" stroke="black" d="M2592.04,-2033.09C2669.91,-1989.8 2540.51,-1312.2 2492.04,-1237.44 2437.28,-1152.99 2388.4,-1165.59 2312.04,-1100.03 2289.56,-1080.74 2290.25,-1067.58 2264.04,-1053.78 1977.38,-902.99 1867.61,-971.48 1550.04,-907.78 1449.86,-887.69 1332.36,-864.73 1268.72,-852.35"/>
<polygon fill="#85f910" stroke="black" points="1269.46,-848.93 1258.97,-850.45 1268.12,-855.8 1269.46,-848.93"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge173" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2592.04,-2033.09C2703.75,-1975.22 2782.04,-1949.07 2782.04,-1823.26 2782.04,-1823.26 2782.04,-1823.26 2782.04,-1701.82 2782.04,-1486.51 2740.78,-1429.79 2644.04,-1237.44 2537.97,-1026.55 2363.41,-1066.98 2288.04,-843.28"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_app_plugin -->
<g id="edge174" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2132.67,-2132.97C2309.18,-2126.19 2896.04,-2091.79 2896.04,-1933.68 2896.04,-1933.68 2896.04,-1933.68 2896.04,-1167.74 2896.04,-998.48 2848.63,-955.76 2758.04,-812.78 2638.06,-623.44 2611.23,-547.62 2411.04,-446.78 2322.04,-401.95 2019.15,-385.51 1901.91,-380.73"/>
<polygon fill="#85f910" stroke="black" points="1902.15,-377.24 1892.02,-380.34 1901.87,-384.24 1902.15,-377.24"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_events -->
<g id="edge175" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1944.04,-1701.82C1916.43,-1644.38 1963,-1610.89 1925.04,-1559.69 1902.05,-1528.7 1880.51,-1541.02 1846.04,-1523.69 1807.04,-1504.1 1801.98,-1488.34 1760.04,-1476.32 1479.78,-1395.97 1380.9,-1534.65 1105.04,-1440.32 1098.39,-1438.05 1091.81,-1434.8 1085.58,-1431.11"/>
<polygon fill="#85f910" stroke="black" points="1087.71,-1428.32 1077.41,-1425.86 1083.93,-1434.21 1087.71,-1428.32"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_service -->
<g id="edge176" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2085.15,-2102.52C2089.64,-2008.51 2090.51,-1729 1958.04,-1559.69 1937.92,-1533.99 1920.1,-1543.33 1894.04,-1523.69 1870.09,-1505.66 1873.09,-1489.23 1846.04,-1476.32 1832,-1469.62 1387.79,-1422.94 1223.41,-1405.85"/>
<polygon fill="#85f910" stroke="black" points="1223.88,-1402.38 1213.58,-1404.83 1223.16,-1409.34 1223.88,-1402.38"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_container -->
<g id="edge177" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2033.57,-2131.99C1951.17,-2126.68 1781.38,-2110.92 1645.04,-2066.79 1615.59,-2057.26 1607.55,-2053.98 1583.04,-2035.09"/>
<path fill="none" stroke="black" d="M1583.04,-2033.09C1569.04,-2022.3 1571.02,-2014.37 1559.04,-2001.39 1548.82,-1990.32 1536.97,-1979.11 1525.72,-1969.09"/>
<polygon fill="#85f910" stroke="black" points="1528.23,-1966.63 1518.41,-1962.67 1523.62,-1971.9 1528.23,-1966.63"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_events -->
<g id="edge178" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1929.04,-2033.09C1897.06,-1987.26 2010.19,-1989.41 2006.04,-1933.68"/>
<path fill="none" stroke="black" d="M2006.04,-1931.68C2002.86,-1889.05 1919.45,-1808.41 1887.04,-1780.54 1834.71,-1735.54 1791.61,-1760.36 1752.04,-1703.82"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_services -->
<g id="edge179" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1929.04,-2033.09C1919.92,-2019.42 1935.99,-2012.55 1948.04,-2001.39 1974.28,-1977.07 2001.23,-1995.82 2020.04,-1965.39 2064.54,-1893.38 1975.34,-1764.76 1944.04,-1703.82"/>
<path fill="none" stroke="black" d="M1944.04,-1701.82C1934.94,-1682.89 1933.37,-1677.33 1920.04,-1661.1 1910.56,-1649.57 1899.06,-1638.28 1887.91,-1628.32"/>
<polygon fill="#85f910" stroke="black" points="1890.47,-1625.91 1880.63,-1621.98 1885.87,-1631.19 1890.47,-1625.91"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_events -->
<g id="edge180" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2039.09,-2119.8C2010.65,-2108.64 1974.18,-2091.04 1948.04,-2066.79 1935.99,-2055.62 1938.15,-2048.76 1929.04,-2035.09"/>
<path fill="none" stroke="black" d="M1929.04,-2033.09C1892.02,-1977.57 1854.21,-1996.23 1795.04,-1965.39 1791.37,-1963.47 1787.58,-1961.48 1783.77,-1959.46"/>
<polygon fill="#85f910" stroke="black" points="1785.54,-1956.44 1775.07,-1954.83 1782.25,-1962.62 1785.54,-1956.44"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_services -->
<g id="edge181" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1929.04,-2033.09C1914.81,-2011.75 1897.54,-1988.95 1883.06,-1970.58"/>
<polygon fill="#85f910" stroke="black" points="1885.89,-1968.51 1876.93,-1962.86 1880.41,-1972.87 1885.89,-1968.51"/>
</g>
<!-- plugginger_implementations -->
<g id="node43" class="node">
<title>plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#6ba135" stroke="black" cx="1681.04" cy="-1500.01" rx="70.18" ry="23.69"/>
<text text-anchor="middle" x="1681.04" y="-1503.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1681.04" y="-1490.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations</text>
</g>
<!-- plugginger_implementations_container&#45;&gt;plugginger_implementations -->
<g id="edge182" class="edge">
<title>plugginger_implementations_container&#45;&gt;plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1498.55,-1900.28C1516.1,-1863.66 1544.52,-1800.95 1562.04,-1744.54 1586.91,-1664.44 1554.55,-1630.16 1600.04,-1559.69 1608.52,-1546.55 1621.08,-1535.38 1633.78,-1526.43"/>
<polygon fill="#70a838" stroke="black" points="1635.34,-1529.6 1641.73,-1521.15 1631.47,-1523.77 1635.34,-1529.6"/>
</g>
<!-- plugginger_implementations_events&#45;&gt;plugginger_implementations -->
<g id="edge183" class="edge">
<title>plugginger_implementations_events&#45;&gt;plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1681.04,-1559.27C1681.04,-1551.52 1681.04,-1543.21 1681.04,-1535.36"/>
<polygon fill="#619130" stroke="black" points="1684.54,-1535.46 1681.04,-1525.46 1677.54,-1535.46 1684.54,-1535.46"/>
</g>
<!-- plugginger_implementations_services&#45;&gt;plugginger_implementations -->
<g id="edge184" class="edge">
<title>plugginger_implementations_services&#45;&gt;plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1799.06,-1566.46C1776.59,-1554 1749.45,-1538.94 1726.86,-1526.42"/>
<polygon fill="#669933" stroke="black" points="1728.6,-1523.38 1718.16,-1521.59 1725.21,-1529.5 1728.6,-1523.38"/>
</g>
<!-- plugginger_interfaces -->
<g id="node47" class="node">
<title>plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#70bf21" stroke="black" cx="1829.04" cy="-1822.26" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1829.04" y="-1825.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1829.04" y="-1812.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces</text>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_events -->
<g id="edge185" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1815.2,-1799.38C1800.05,-1775.61 1774.87,-1736.65 1752.04,-1703.82"/>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_services -->
<g id="edge186" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853.71,-1801.68C1877.7,-1780.16 1910,-1743.36 1906.04,-1703.82"/>
<path fill="none" stroke="black" d="M1906.04,-1701.82C1903.51,-1676.6 1890.37,-1651.81 1876.83,-1632.32"/>
<polygon fill="#70bf21" stroke="black" points="1879.8,-1630.45 1871.08,-1624.42 1874.13,-1634.56 1879.8,-1630.45"/>
</g>
<!-- plugginger_interfaces_events&#45;&gt;plugginger_implementations_events -->
<g id="edge187" class="edge">
<title>plugginger_interfaces_events&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1742.97,-1900.22C1754.31,-1845.28 1773.55,-1734.75 1752.04,-1703.82"/>
<path fill="none" stroke="black" d="M1752.04,-1701.82C1736.51,-1679.64 1719.9,-1654.36 1706.65,-1633.8"/>
<polygon fill="#75bc2f" stroke="black" points="1709.6,-1631.91 1701.25,-1625.39 1703.71,-1635.69 1709.6,-1631.91"/>
</g>
<!-- plugginger_interfaces_events&#45;&gt;plugginger_interfaces -->
<g id="edge188" class="edge">
<title>plugginger_interfaces_events&#45;&gt;plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1759.99,-1903.76C1773.34,-1888.2 1789.96,-1868.82 1803.55,-1852.97"/>
<polygon fill="#75bc2f" stroke="black" points="1805.91,-1855.6 1809.76,-1845.73 1800.59,-1851.04 1805.91,-1855.6"/>
</g>
<!-- plugginger_interfaces_services&#45;&gt;plugginger_implementations_services -->
<g id="edge189" class="edge">
<title>plugginger_interfaces_services&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1875.88,-1903.58C1890.5,-1882.69 1906.64,-1852.8 1906.04,-1823.26"/>
<path fill="none" stroke="black" d="M1906.04,-1821.26C1904.97,-1769.08 1911.24,-1755.76 1906.04,-1703.82"/>
</g>
<!-- plugginger_interfaces_services&#45;&gt;plugginger_interfaces -->
<g id="edge190" class="edge">
<title>plugginger_interfaces_services&#45;&gt;plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1846.03,-1900.03C1843.05,-1886.55 1839.56,-1870.81 1836.53,-1857.1"/>
<polygon fill="#75bc2f" stroke="black" points="1840.01,-1856.65 1834.43,-1847.64 1833.18,-1858.16 1840.01,-1856.65"/>
</g>
<!-- plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge191" class="edge">
<title>plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M877.97,-274.24C1005.26,-243.96 1375.04,-156.01 1530.21,-119.11"/>
<polygon fill="#669933" stroke="black" points="1530.61,-122.61 1539.53,-116.89 1528.99,-115.8 1530.61,-122.61"/>
</g>
<!-- plugginger_testing -->
<g id="node51" class="node">
<title>plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2685.04,-214.5 2615.04,-214.5 2615.04,-178.5 2685.04,-178.5 2685.04,-214.5"/>
<text text-anchor="middle" x="2650.04" y="-199.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2650.04" y="-187" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing</text>
</g>
<!-- plugginger_testing_collectors -->
<g id="node52" class="node">
<title>plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85ce3b" stroke="black" cx="2819.04" cy="-378.08" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2819.04" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2819.04" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="2819.04" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">collectors</text>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing -->
<g id="edge192" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2783.01,-355.19C2759.57,-339.09 2730.27,-315.13 2713.04,-286.69"/>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers -->
<g id="edge193" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2780.96,-356.6C2774,-352.86 2766.8,-349 2760.04,-345.38 2738.75,-333.98 2715.13,-321.37 2695.25,-310.77"/>
<polygon fill="#85ce3b" stroke="black" points="2697.03,-307.75 2686.56,-306.14 2693.74,-313.93 2697.03,-307.75"/>
</g>
<!-- plugginger_testing_helpers&#45;&gt;plugginger_testing -->
<g id="edge194" class="edge">
<title>plugginger_testing_helpers&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2650.04,-262.23C2650.04,-251.3 2650.04,-238.02 2650.04,-226.35"/>
<polygon fill="blue" stroke="black" points="2653.54,-226.44 2650.04,-216.44 2646.54,-226.44 2653.54,-226.44"/>
</g>
<!-- plugginger_testing_mock_app -->
<g id="node54" class="node">
<title>plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85ce3b" stroke="black" cx="2702.04" cy="-378.08" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2702.04" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2702.04" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="2702.04" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">mock_app</text>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing -->
<g id="edge195" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2715.02,-346.14C2720.64,-327.65 2723.77,-304.4 2713.04,-286.69"/>
<path fill="none" stroke="black" d="M2713.04,-284.69C2700.13,-263.4 2683.58,-240.64 2670.68,-223.71"/>
<polygon fill="#85ce3b" stroke="black" points="2673.74,-221.94 2664.86,-216.15 2668.19,-226.21 2673.74,-221.94"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers -->
<g id="edge196" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2684.72,-346.98C2679.57,-338.03 2673.9,-328.18 2668.67,-319.08"/>
<polygon fill="#85ce3b" stroke="black" points="2671.77,-317.46 2663.75,-310.53 2665.71,-320.95 2671.77,-317.46"/>
</g>
<!-- pydantic -->
<g id="node55" class="node">
<title>pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#06f9f9" stroke="black" cx="2630.04" cy="-2135.5" rx="35.49" ry="18"/>
<text text-anchor="middle" x="2630.04" y="-2132.37" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic</text>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge197" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2602.08,-2124.07C2541.97,-2100.17 2399.97,-2035.53 2327.04,-1933.68"/>
<path fill="none" stroke="black" d="M2327.04,-1931.68C2207.2,-1786.2 2150.6,-1773.21 2034.04,-1625.1 2012.67,-1597.95 2016.83,-1583.75 1992.04,-1559.69 1969.71,-1538.04 1956.61,-1542.75 1932.04,-1523.69 1908.35,-1505.32 1909.61,-1491.95 1884.04,-1476.32 1858.09,-1460.46 1788.59,-1438.43 1732.2,-1422.04"/>
<polygon fill="#06f9f9" stroke="black" points="1733.51,-1418.77 1722.93,-1419.36 1731.57,-1425.5 1733.51,-1418.77"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge198" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2934.04,-377.08C2911.68,-317.27 2904.72,-291.39 2848.04,-262 2783.9,-228.74 1620.25,-228.22 1548.04,-226 1257.28,-217.07 910.29,-204.89 768.39,-199.84"/>
<polygon fill="#06f9f9" stroke="black" points="768.54,-196.34 758.42,-199.48 768.29,-203.34 768.54,-196.34"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge199" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2630.79,-2117.01C2633.33,-2045.64 2637.83,-1770.25 2568.04,-1559.69 2541.88,-1480.78 2442.5,-1289.93 2378.04,-1237.44 2195.65,-1088.94 1461.27,-901.82 1268.54,-854.53"/>
<polygon fill="#06f9f9" stroke="black" points="1269.43,-851.15 1258.89,-852.17 1267.77,-857.95 1269.43,-851.15"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge200" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2665.76,-2133.41C2722.09,-2130.14 2833.06,-2117.23 2910.04,-2066.79 2924.82,-2057.11 2929.7,-2052.22 2934.04,-2035.09"/>
<path fill="none" stroke="black" d="M2934.04,-2033.09C2938.58,-2015.16 2776.54,-1760.97 2768.04,-1744.54 2652.82,-1521.89 2664.96,-1442.7 2521.04,-1237.44 2485.75,-1187.12 2472.51,-1177.38 2427.04,-1136.03 2390.41,-1102.73 2273.85,-1044.18 2250.04,-1000.78"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_app_plugin -->
<g id="edge201" class="edge">
<title>pydantic&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2934.04,-1931.68C2924.59,-1830.85 2934.04,-1805.09 2934.04,-1703.82 2934.04,-1703.82 2934.04,-1703.82 2934.04,-1397.6 2934.04,-1296.33 2961.8,-1267.13 2934.04,-1169.74"/>
<path fill="none" stroke="black" d="M2934.04,-1167.74C2877.2,-995.51 2530.84,-675.59 2397.04,-553.16"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_plugin -->
<g id="edge202" class="edge">
<title>pydantic&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2934.04,-2033.09C2955.21,-1994.32 2941.32,-1977.26 2934.04,-1933.68"/>
<path fill="none" stroke="black" d="M2934.04,-1931.68C2916.43,-1826.39 2858.04,-1810.58 2858.04,-1703.82 2858.04,-1703.82 2858.04,-1703.82 2858.04,-1591.4 2858.04,-1384.19 2850.46,-1321.48 2758.04,-1136.03 2742.12,-1104.09 2370.64,-666.63 2340.04,-648.28 2149.07,-533.81 1432.22,-484.68 1241.75,-473.48"/>
<polygon fill="#06f9f9" stroke="black" points="1242.19,-470 1232,-472.92 1241.78,-476.99 1242.19,-470"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge203" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2934.04,-611.28C2900.55,-513.66 2967.57,-476.68 2934.04,-379.08"/>
<path fill="none" stroke="black" d="M2934.04,-377.08C2920.47,-340.78 2882.94,-317.94 2849.6,-304.3"/>
<polygon fill="#06f9f9" stroke="black" points="2850.96,-301.07 2840.37,-300.73 2848.44,-307.6 2850.96,-301.07"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_models -->
<g id="edge204" class="edge">
<title>pydantic&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2327.04,-1931.68C2313.66,-1913 2313.04,-1887.49 2316.05,-1865.9"/>
<polygon fill="#06f9f9" stroke="black" points="2319.47,-1866.65 2317.72,-1856.2 2312.57,-1865.46 2319.47,-1866.65"/>
</g>
<!-- pydantic&#45;&gt;plugginger_testing_helpers -->
<g id="edge205" class="edge">
<title>pydantic&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2934.04,-1167.74C2921.78,-1094.55 2934.04,-1074.99 2934.04,-1000.78 2934.04,-1000.78 2934.04,-1000.78 2934.04,-752.66 2934.04,-690.27 2961.85,-668.13 2934.04,-612.28"/>
<path fill="none" stroke="black" d="M2934.04,-611.28C2852.57,-477.43 2716.8,-549.56 2644.04,-410.78 2629.38,-382.83 2633.44,-346.28 2639.61,-320.1"/>
<polygon fill="#06f9f9" stroke="black" points="2642.93,-321.24 2642.05,-310.69 2636.15,-319.49 2642.93,-321.24"/>
</g>
<!-- typing_extensions -->
<g id="node56" class="node">
<title>typing_extensions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#8553b6" stroke="black" cx="795.04" cy="-469.91" rx="64.4" ry="18"/>
<text text-anchor="middle" x="795.04" y="-466.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">typing_extensions</text>
</g>
<!-- typing_extensions&#45;&gt;plugginger_stubgen -->
<g id="edge206" class="edge">
<title>typing_extensions&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M829.76,-454.36C863.72,-437.94 907.55,-409.53 887.04,-379.08"/>
<path fill="none" stroke="black" d="M887.04,-377.08C874.38,-358.29 861.49,-336.38 851.59,-318.85"/>
<polygon fill="#8553b6" stroke="black" points="854.81,-317.42 846.87,-310.4 848.7,-320.84 854.81,-317.42"/>
</g>
</g>
</svg>
