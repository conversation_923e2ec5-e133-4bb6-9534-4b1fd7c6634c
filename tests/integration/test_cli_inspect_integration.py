"""
Integration tests for the CLI inspect command.

These tests verify the complete inspect functionality including
JSON output validation and schema compliance.
"""

import json
from typing import Any

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase
from plugginger.api.service import service
from plugginger.cli.cmd_inspect import AppInspector
from plugginger.schemas.json.app_graph import validate_app_graph


class SimplePlugin(PluginBase):
    """Simple test plugin."""

    @service()
    async def simple_service(self, data: str) -> str:
        """A simple service for testing."""
        return f"processed: {data}"


class EventPlugin(PluginBase):
    """Plugin with event listeners."""

    @on_event("user.created")
    async def on_user_created(self, user_id: int, username: str) -> None:
        """Handle user creation events."""
        pass

    @service()
    async def emit_event(self, event_type: str) -> str:
        """Emit a test event."""
        return f"emitted: {event_type}"


class DependencyPlugin(PluginBase):
    """Plugin with dependencies."""

    needs = [Depends("simple_plugin")]

    @service()
    async def dependent_service(self, input_data: str) -> dict[str, Any]:
        """Service that depends on another plugin."""
        return {"result": input_data, "processed_by": "dependency_plugin"}


def create_test_app() -> PluggingerAppBuilder:
    """Create a test application with multiple plugins."""
    builder = PluggingerAppBuilder("integration_test_app")
    builder.include(SimplePlugin)
    builder.include(EventPlugin)
    builder.include(DependencyPlugin)
    return builder


class TestCLIInspectIntegration:
    """Integration tests for CLI inspect functionality."""

    def test_basic_app_inspection(self) -> None:
        """Test basic application inspection."""
        test_app = create_test_app()
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Verify basic structure
        assert isinstance(result, dict)
        assert "app" in result
        assert "plugins" in result
        assert "dependency_graph" in result
        assert "metadata" in result

    def test_inspect_json_output_structure(self) -> None:
        """Test that JSON output has the correct structure."""
        # Use AppInspector directly instead of cmd_inspect to avoid CLI complexity
        from plugginger.cli.cmd_inspect import AppInspector

        # Create test app
        test_app = create_test_app()
        assert test_app is not None
        assert test_app._app_name == "integration_test_app"

        # Use AppInspector directly
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Verify top-level structure
        assert "app" in result
        assert "plugins" in result
        assert "dependency_graph" in result
        assert "metadata" in result

        # Verify app info
        app_info = result["app"]
        assert app_info["name"] == "integration_test_app"
        assert app_info["plugin_count"] == 3

    def test_service_signatures_in_json_output(self) -> None:
        """Test that service signatures include detailed type information and docstrings."""
        from plugginger.cli.cmd_inspect import AppInspector

        # Create test app
        test_app = create_test_app()
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Find a plugin with services
        plugins = result["plugins"]
        service_plugin = None
        for plugin in plugins:
            if plugin["services"]:
                service_plugin = plugin
                break

        assert service_plugin is not None, "No plugin with services found"

        # Check service signature structure
        service = service_plugin["services"][0]
        assert "signature" in service

        signature = service["signature"]
        assert "parameters" in signature
        assert "return_type" in signature
        assert "docstring" in signature

        # Check parameter details
        if signature["parameters"]:
            param = signature["parameters"][0]
            assert "name" in param
            assert "type" in param
            assert "default" in param
            assert "kind" in param

        # Docstring can be None for undocumented services
        if signature["docstring"] is not None:
            docstring = signature["docstring"]
            assert "summary" in docstring
            assert "raw" in docstring

    def test_dependency_graph_structure(self) -> None:
        """Test dependency graph structure in JSON output."""
        test_app = create_test_app()
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Check dependency graph
        dep_graph = result["dependency_graph"]
        assert "nodes" in dep_graph
        assert "edges" in dep_graph

        # Check nodes
        nodes = dep_graph["nodes"]
        assert len(nodes) == 3  # simple, event, dependency plugins

        # Verify node structure
        for node in nodes:
            assert "id" in node
            assert "type" in node
            assert node["type"] == "plugin"
            if "metadata" in node:
                metadata = node["metadata"]
                assert "class_name" in metadata
                assert "module" in metadata

        # Check edges (DependencyPlugin depends on SimplePlugin)
        edges = dep_graph["edges"]
        dependency_edges = [e for e in edges if "DependencyPlugin" in e["from"]]
        assert len(dependency_edges) >= 1

        # Find the dependency edge
        edge = dependency_edges[0]
        assert edge["type"] == "depends_on"
        assert edge["optional"] is False

    def test_cycle_detection_integration(self) -> None:
        """Test cycle detection in real dependency scenarios."""
        # Create app with circular dependency
        builder = PluggingerAppBuilder("cycle_test_app")

        class PluginA(PluginBase):
            needs = [Depends("PluginB")]

            @service()
            async def service_a(self) -> str:
                return "a"

        class PluginB(PluginBase):
            needs = [Depends("PluginA")]

            @service()
            async def service_b(self) -> str:
                return "b"

        builder.include(PluginA)
        builder.include(PluginB)

        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Should detect the cycle
        dep_graph = result["dependency_graph"]
        if "cycles" in dep_graph:
            cycles = dep_graph["cycles"]
            assert len(cycles) > 0
            cycle = cycles[0]
            assert cycle["type"] == "circular_dependency"
            assert len(cycle["nodes"]) >= 2

    def test_json_schema_validation(self) -> None:
        """Test that generated JSON validates against the schema."""
        test_app = create_test_app()
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Validate against schema
        is_valid, errors = validate_app_graph(result)

        # Skip validation if jsonschema not available
        if errors == ["jsonschema not available - validation skipped"]:
            pytest.skip("jsonschema not available")

        assert is_valid, f"JSON validation failed: {errors}"

    def test_json_serialization_roundtrip(self) -> None:
        """Test JSON serialization and deserialization."""
        test_app = create_test_app()
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Serialize to JSON
        json_str = json.dumps(result, indent=2)
        assert len(json_str) > 0

        # Deserialize back
        parsed = json.loads(json_str)

        # Verify structure is preserved
        assert parsed["app"]["name"] == "integration_test_app"
        assert len(parsed["plugins"]) == 3
        assert "dependency_graph" in parsed
        assert "metadata" in parsed

    def test_empty_app_inspection(self) -> None:
        """Test inspecting an app with no plugins."""
        builder = PluggingerAppBuilder("empty_app")
        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Should still have valid structure
        assert result["app"]["name"] == "empty_app"
        assert result["app"]["plugin_count"] == 0
        assert len(result["plugins"]) == 0
        assert len(result["dependency_graph"]["nodes"]) == 0
        assert len(result["dependency_graph"]["edges"]) == 0

    def test_plugin_metadata_extraction(self) -> None:
        """Test extraction of plugin metadata."""
        from plugginger.api.plugin import plugin

        @plugin(name="test_metadata_plugin", version="1.2.3")
        class MetadataPlugin(PluginBase):
            @service()
            async def test_service(self) -> str:
                return "test"

        builder = PluggingerAppBuilder("metadata_test")
        builder.include(MetadataPlugin)

        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Find the plugin
        plugins = result["plugins"]
        metadata_plugin = next(p for p in plugins if p["class_name"] == "MetadataPlugin")

        # Check metadata
        metadata = metadata_plugin["metadata"]
        assert metadata["name"] == "test_metadata_plugin"
        assert metadata["version"] == "1.2.3"
        assert metadata["description"] == "Test plugin"

    def test_event_listener_extraction(self) -> None:
        """Test extraction of event listener information."""
        test_app = create_test_app()
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Find plugin with event listeners
        event_plugin = next(p for p in result["plugins"] if p["class_name"] == "EventPlugin")

        # Check event listeners
        listeners = event_plugin["event_listeners"]
        assert len(listeners) == 1

        listener = listeners[0]
        assert listener["method_name"] == "on_user_created"
        assert listener["event_pattern"] == "user.created"
        assert "signature" in listener

    def test_large_app_performance(self) -> None:
        """Test performance with a larger number of plugins."""
        import time

        # Create app with many plugins
        builder = PluggingerAppBuilder("large_app")

        for i in range(20):  # Create 20 plugins
            class_name = f"Plugin{i}"
            plugin_class = type(class_name, (PluginBase,), {
                "test_service": service()(lambda self: f"result_{i}")
            })
            builder.include(plugin_class)

        # Measure analysis time
        start_time = time.time()
        inspector = AppInspector(builder)
        result = inspector.analyze()
        end_time = time.time()

        # Should complete in reasonable time (< 5 seconds)
        analysis_time = end_time - start_time
        assert analysis_time < 5.0, f"Analysis took too long: {analysis_time:.2f}s"

        # Verify all plugins were analyzed
        assert len(result["plugins"]) == 20
        assert result["app"]["plugin_count"] == 20
