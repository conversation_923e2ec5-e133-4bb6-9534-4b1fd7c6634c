"""
Integration tests for the inspect CLI command.

These tests verify the end-to-end functionality of the `plugginger inspect` command
with real app factories and plugin configurations.
"""

import json
import tempfile
from pathlib import Path
from typing import Any

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


@plugin(name="cache_plugin", version="1.0.0")
class CachePlugin(PluginBase):
    """Cache plugin for integration tests."""

    needs = [Depends("logger_plugin"), Depends("config", optional=True)]

    @service()
    async def get(self, key: str) -> str | None:
        """Get value from cache."""
        return f"cached_{key}"

    @service()
    async def set(self, key: str, value: str, ttl: int = 300) -> None:
        """Set value in cache."""
        pass

    @on_event("app.startup")
    async def initialize_cache(self, event_data: dict[str, Any]) -> None:
        """Initialize cache on startup."""
        pass

    @on_event("cache.clear")
    async def clear_cache(self, event_data: dict[str, Any]) -> None:
        """Clear cache on demand."""
        pass


@plugin(name="logger_plugin", version="2.1.0")
class LoggerPlugin(PluginBase):
    """Logger plugin for integration tests."""

    @service()
    async def log(self, level: str, message: str, **kwargs: Any) -> None:
        """Log a message."""
        pass

    @service()
    async def debug(self, message: str) -> None:
        """Log debug message."""
        pass


@plugin(name="metrics_plugin", version="1.5.0")
class MetricsPlugin(PluginBase):
    """Metrics plugin for integration tests."""

    needs = [Depends("logger_plugin")]

    @service()
    async def increment(self, metric: str, value: int = 1) -> None:
        """Increment a metric."""
        pass

    @on_event("*.error")
    async def track_errors(self, event_data: dict[str, Any]) -> None:
        """Track error events."""
        pass


def create_test_app() -> PluggingerAppBuilder:
    """Create a test app with multiple plugins."""
    builder = PluggingerAppBuilder("integration_test_app")
    builder.include(LoggerPlugin)  # Will be registered as "logger_plugin" from @plugin decorator
    builder.include(CachePlugin)   # Will be registered as "cache_plugin" from @plugin decorator
    builder.include(MetricsPlugin) # Will be registered as "metrics_plugin" from @plugin decorator
    return builder


class TestCLIInspectIntegration:
    """Integration tests for the inspect CLI command."""

    def test_inspect_json_output_structure(self) -> None:
        """Test that JSON output has the correct structure."""
        # Use AppInspector directly instead of cmd_inspect to avoid CLI complexity
        from plugginger.cli.cmd_inspect import AppInspector

        # Create test app
        test_app = create_test_app()
        assert test_app is not None
        assert test_app._app_name == "integration_test_app"

        # Use AppInspector directly
        inspector = AppInspector(test_app)
        result = inspector.analyze()

        # Verify top-level structure
        assert "app" in result
        assert "plugins" in result
        assert "dependency_graph" in result
        assert "metadata" in result

        # Verify app info
        app_info = result["app"]
        assert app_info["name"] == "integration_test_app"
        assert app_info["plugin_count"] == 3

        # Verify plugins
        plugins = result["plugins"]
        assert len(plugins) == 3

        plugin_names = [p["registration_name"] for p in plugins]
        assert "logger_plugin" in plugin_names
        assert "cache_plugin" in plugin_names
        assert "metrics_plugin" in plugin_names

    def test_inspect_plugin_details(self) -> None:
        """Test detailed plugin information extraction."""
        from plugginger.cli.cmd_inspect import AppInspector

        builder = create_test_app()
        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Find cache plugin
        cache_plugin = next(
            p for p in result["plugins"]
            if p["registration_name"] == "cache_plugin"
        )

        # Verify cache plugin details
        assert cache_plugin["class_name"] == "CachePlugin"
        assert len(cache_plugin["services"]) == 2  # get, set
        assert len(cache_plugin["event_listeners"]) == 2  # startup, cache.clear
        assert len(cache_plugin["dependencies"]) == 2  # logger, config

        # Verify service details
        service_names = [s["name"] for s in cache_plugin["services"]]
        assert "get" in service_names
        assert "set" in service_names

        # Verify event listener details
        event_patterns = [listener["event_pattern"] for listener in cache_plugin["event_listeners"]]
        assert "app.startup" in event_patterns
        assert "cache.clear" in event_patterns

        # Verify dependency details
        dep_names = [d["name"] for d in cache_plugin["dependencies"]]
        assert "logger_plugin" in dep_names
        assert "config" in dep_names

        # Check optional dependency
        config_dep = next(d for d in cache_plugin["dependencies"] if d["name"] == "config")
        assert config_dep["optional"] is True

    def test_inspect_dependency_graph(self) -> None:
        """Test dependency graph extraction."""
        from plugginger.cli.cmd_inspect import AppInspector

        builder = create_test_app()
        inspector = AppInspector(builder)
        result = inspector.analyze()

        graph = result["dependency_graph"]

        # Verify nodes
        assert len(graph["nodes"]) == 3
        node_ids = [node["id"] for node in graph["nodes"]]
        assert "logger_plugin" in node_ids
        assert "cache_plugin" in node_ids
        assert "metrics_plugin" in node_ids

        # Verify edges
        edges = graph["edges"]
        assert len(edges) > 0

        # Check cache_plugin dependencies
        cache_edges = [e for e in edges if e["from"] == "cache_plugin"]
        assert len(cache_edges) == 2

        cache_deps = [e["to"] for e in cache_edges]
        assert "logger_plugin" in cache_deps
        assert "config" in cache_deps

        # Check optional dependency marking
        config_edge = next(e for e in cache_edges if e["to"] == "config")
        assert config_edge["optional"] is True

    def test_inspect_service_signatures(self) -> None:
        """Test service signature extraction."""
        from plugginger.cli.cmd_inspect import AppInspector

        builder = create_test_app()
        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Find cache plugin
        cache_plugin = next(
            p for p in result["plugins"]
            if p["registration_name"] == "cache_plugin"
        )

        # Find the 'set' service
        set_service = next(
            s for s in cache_plugin["services"]
            if s["name"] == "set"
        )

        # Verify signature
        signature = set_service["signature"]
        assert "parameters" in signature
        assert "return_type" in signature

        # Check parameters
        params = signature["parameters"]
        param_names = [p["name"] for p in params]
        assert "key" in param_names
        assert "value" in param_names
        assert "ttl" in param_names

        # Check parameter with default value
        ttl_param = next(p for p in params if p["name"] == "ttl")
        assert ttl_param["default"] == "300"

    def test_inspect_metadata_generation(self) -> None:
        """Test metadata generation in output."""
        from plugginger.cli.cmd_inspect import AppInspector

        builder = create_test_app()
        inspector = AppInspector(builder)
        result = inspector.analyze()

        metadata = result["metadata"]
        assert metadata["schema_version"] == "1.0.0"
        assert metadata["generated_by"] == "plugginger inspect"
        assert "generated_at" in metadata

        # Verify timestamp format (ISO format)
        timestamp = metadata["generated_at"]
        assert "T" in timestamp
        assert timestamp.endswith("Z") or "+" in timestamp

    def test_inspect_file_output(self) -> None:
        """Test writing output to file."""
        from unittest.mock import patch

        from plugginger.cli.cmd_inspect import cmd_inspect

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as tmp_file:
            output_file = Path(tmp_file.name)

        try:
            with patch('plugginger.cli.cmd_inspect.resolve_app_factory', return_value=create_test_app()):
                cmd_inspect("test.module:create_test_app", output_json=True, output_file=output_file)

                # Verify file was created and contains valid JSON
                assert output_file.exists()
                content = output_file.read_text(encoding='utf-8')
                result = json.loads(content)

                assert result["app"]["name"] == "integration_test_app"
                assert len(result["plugins"]) == 3
        finally:
            output_file.unlink(missing_ok=True)

    def test_inspect_human_readable_format(self) -> None:
        """Test human-readable output format."""
        from unittest.mock import patch

        from plugginger.cli.cmd_inspect import cmd_inspect

        with patch('plugginger.cli.cmd_inspect.resolve_app_factory', return_value=create_test_app()):
            with patch('builtins.print') as mock_print:
                cmd_inspect("test.module:create_test_app", output_json=False, output_file=None)

                # Collect all printed output
                printed_lines = [call[0][0] for call in mock_print.call_args_list]
                output_text = '\n'.join(printed_lines)

                # Verify key information is present
                assert "integration_test_app" in output_text
                assert "cache_plugin" in output_text
                assert "logger_plugin" in output_text
                assert "metrics_plugin" in output_text

                # Verify services are listed
                assert "get" in output_text
                assert "set" in output_text
                assert "log" in output_text

                # Verify events are listed
                assert "app.startup" in output_text
                assert "*.error" in output_text

    def test_inspect_empty_app(self) -> None:
        """Test inspecting an app with no plugins."""
        from unittest.mock import patch

        from plugginger.cli.cmd_inspect import cmd_inspect

        def create_empty_app() -> PluggingerAppBuilder:
            return PluggingerAppBuilder("empty_app")

        with patch('plugginger.cli.cmd_inspect.resolve_app_factory', return_value=create_empty_app()):
            with patch('builtins.print') as mock_print:
                cmd_inspect("test.module:create_empty_app", output_json=True, output_file=None)

                json_output = mock_print.call_args[0][0]
                result = json.loads(json_output)

                assert result["app"]["name"] == "empty_app"
                assert result["app"]["plugin_count"] == 0
                assert result["plugins"] == []
                assert result["dependency_graph"]["nodes"] == []
                assert result["dependency_graph"]["edges"] == []

    def test_inspect_error_handling(self) -> None:
        """Test error handling in inspect command."""
        from unittest.mock import patch

        from plugginger.cli.cmd_inspect import cmd_inspect

        # Test with invalid factory path
        with patch('plugginger.cli.cmd_inspect.resolve_app_factory', side_effect=ImportError("Module not found")):
            with pytest.raises(ImportError):
                cmd_inspect("invalid.module:factory", output_json=True, output_file=None)

    def test_inspect_plugin_metadata_extraction(self) -> None:
        """Test extraction of plugin metadata from decorators."""
        from plugginger.cli.cmd_inspect import AppInspector

        builder = create_test_app()
        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Find cache plugin and check metadata
        cache_plugin = next(
            p for p in result["plugins"]
            if p["registration_name"] == "cache_plugin"
        )

        metadata = cache_plugin["metadata"]
        assert metadata["name"] == "cache_plugin"
        assert metadata["version"] == "1.0.0"
