# tests/integration/test_core_application_lifecycle.py

"""
Integration tests for core application lifecycle in Plugginger framework.

Tests the complete flow from PluggingerAppBuilder through PluggingerAppInstance
to RuntimeFacade, covering plugin registration, instantiation, dependency injection,
setup, and teardown processes.

Priority 1: CRITICAL - These are the most fundamental integration points.
"""

from __future__ import annotations

import asyncio
from typing import Any
from unittest.mock import AsyncMock, Mock

import pytest
from pydantic import BaseModel

from plugginger import (
    Depends,
    PluggingerAppBuilder,
    PluggingerAppInstance,
    PluginBase,
    on_event,
    plugin,
    service,
)
from plugginger.config.models import GlobalAppConfig
from plugginger.core.config import LogLevel


# Test Plugin Classes
@plugin(name="test_basic_plugin", version="1.0.0")
class BasicTestPlugin(PluginBase):
    """Basic plugin for testing core lifecycle."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.setup_called = False
        self.teardown_called = False

    async def setup(self, plugin_config: BaseModel) -> None:
        """Plugin setup hook."""
        self.setup_called = True

    async def teardown(self) -> None:
        """Plugin teardown hook."""
        self.teardown_called = True

    @service()
    async def get_status(self) -> str:
        """Test service method."""
        return "active"


@plugin(name="dependent_plugin", version="1.0.0")
class DependentTestPlugin(PluginBase):
    """Plugin with dependencies for testing DI integration."""

    needs: list[Depends] = [Depends("test_basic_plugin")]

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.setup_called = False
        self.teardown_called = False

    async def setup(self, plugin_config: BaseModel) -> None:
        """Plugin setup hook."""
        self.setup_called = True

    async def teardown(self) -> None:
        """Plugin teardown hook."""
        self.teardown_called = True

    @service()
    async def get_dependency_status(self) -> str:
        """Service that uses injected dependency."""
        # Access the injected dependency through the DI system
        basic_plugin = getattr(self, "test_basic_plugin", None)
        if basic_plugin is not None:
            return await basic_plugin.get_status()
        return "dependency_not_found"


class TestPluginConfig(BaseModel):
    """Test configuration model."""
    test_value: str = "default"
    enabled: bool = True


@plugin(name="configured_plugin", version="1.0.0", config_schema=TestPluginConfig)
class ConfiguredTestPlugin(PluginBase):
    """Plugin with configuration for testing config integration."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.setup_called = False
        self.teardown_called = False

    async def setup(self, plugin_config: TestPluginConfig) -> None:
        """Plugin setup hook."""
        self.setup_called = True
        self.config = plugin_config

    async def teardown(self) -> None:
        """Plugin teardown hook."""
        self.teardown_called = True

    @service()
    async def get_config_value(self) -> str:
        """Service that returns config value."""
        return self.config.test_value


class TestCoreApplicationLifecycle:
    """Test core application lifecycle integration."""

    def test_builder_basic_construction(self) -> None:
        """Test basic PluggingerAppBuilder construction."""
        builder = PluggingerAppBuilder(app_name="test_app")

        assert builder._app_name == "test_app"
        assert builder._instance_id_prefix is None  # Top-level app has no prefix
        assert builder._current_fractal_depth == 0
        assert len(builder._registered_item_classes) == 0

    def test_builder_plugin_registration(self) -> None:
        """Test plugin registration with builder."""
        builder = PluggingerAppBuilder(app_name="test_app")

        # Register plugins
        builder.include(BasicTestPlugin)
        builder.include(DependentTestPlugin)

        assert len(builder._registered_item_classes) == 2
        # Check by plugin name since the dict is keyed by name
        assert "test_basic_plugin" in builder._registered_item_classes
        assert "dependent_plugin" in builder._registered_item_classes

    def test_builder_build_creates_app_instance(self) -> None:
        """Test that builder.build() creates PluggingerAppInstance."""
        builder = PluggingerAppBuilder(app_name="test_app")
        builder.include(BasicTestPlugin)
        
        app = builder.build()
        
        assert isinstance(app, PluggingerAppInstance)
        assert app._app_name == "test_app"
        assert app._runtime_facade is not None
        assert app._global_config is not None

    def test_builder_with_global_config(self) -> None:
        """Test builder with custom global configuration."""
        config = GlobalAppConfig(
            app_name="custom_app",
            log_level=LogLevel.DEBUG,
            max_fractal_depth=5
        )
        
        builder = PluggingerAppBuilder(app_name="test_app")
        builder.include(BasicTestPlugin)
        
        app = builder.build(config)
        
        assert app._global_config.app_name == "custom_app"
        assert app._global_config.log_level == LogLevel.DEBUG
        assert app._global_config.max_fractal_depth == 5

    @pytest.mark.asyncio
    async def test_app_startup_and_shutdown_lifecycle(self) -> None:
        """Test complete app startup and shutdown lifecycle."""
        builder = PluggingerAppBuilder(app_name="lifecycle_test")
        builder.include(BasicTestPlugin)
        
        app = builder.build()

        # Start the app (setup plugins)
        await app.start_all_plugins()

        # Get plugin instance for verification (only available after start)
        plugin_instance = app.get_plugin_instance("lifecycle_test:test_basic_plugin")
        assert plugin_instance is not None
        assert isinstance(plugin_instance, BasicTestPlugin)

        # Verify setup was called
        assert plugin_instance.setup_called
        assert not plugin_instance.teardown_called

        # Stop the app (teardown plugins)
        await app.stop_all_plugins()

        # Verify teardown was called
        assert plugin_instance.teardown_called

    @pytest.mark.asyncio
    async def test_dependency_injection_integration(self) -> None:
        """Test dependency injection between plugins."""
        builder = PluggingerAppBuilder(app_name="di_test")
        builder.include(BasicTestPlugin)
        builder.include(DependentTestPlugin)
        
        app = builder.build()

        # Start plugins first (plugins are only available after start)
        await app.start_all_plugins()

        # Get plugin instances (only available after start)
        basic_plugin = app.get_plugin_instance("di_test:test_basic_plugin")
        dependent_plugin = app.get_plugin_instance("di_test:dependent_plugin")

        assert basic_plugin is not None
        assert dependent_plugin is not None
        assert isinstance(basic_plugin, BasicTestPlugin)
        assert isinstance(dependent_plugin, DependentTestPlugin)

        # Verify dependency injection worked (check for injected proxy)
        assert hasattr(dependent_plugin, "test_basic_plugin")
        injected_dependency = getattr(dependent_plugin, "test_basic_plugin")
        assert injected_dependency is not None

        # Test service call through dependency
        result = await app.call_service("dependent_plugin.get_dependency_status")
        assert result == "active"

    @pytest.mark.asyncio
    async def test_plugin_configuration_integration(self) -> None:
        """Test plugin configuration integration."""
        builder = PluggingerAppBuilder(app_name="config_test")
        builder.include(ConfiguredTestPlugin)
        
        # Build with plugin-specific config
        plugin_configs = {
            "configured_plugin": {
                "test_value": "custom_value",
                "enabled": True
            }
        }
        
        config = GlobalAppConfig(
            app_name="config_test",
            plugin_configs=plugin_configs
        )
        app = builder.build(config)
        
        # Start plugins
        await app.start_all_plugins()
        
        # Test that config was applied
        result = await app.call_service("configured_plugin.get_config_value")
        assert result == "custom_value"

    @pytest.mark.asyncio
    async def test_service_registration_and_discovery(self) -> None:
        """Test service registration and discovery integration."""
        builder = PluggingerAppBuilder(app_name="service_test")
        builder.include(BasicTestPlugin)
        
        app = builder.build()
        await app.start_all_plugins()
        
        # Test service discovery
        services = app.list_services()
        assert "test_basic_plugin.get_status" in services
        
        # Test service call
        result = await app.call_service("test_basic_plugin.get_status")
        assert result == "active"
        
        # Test service existence check
        assert app.has_service("test_basic_plugin.get_status")
        assert not app.has_service("nonexistent.service")

    @pytest.mark.asyncio
    async def test_error_handling_during_lifecycle(self) -> None:
        """Test error handling during plugin lifecycle operations."""
        
        @plugin(name="failing_plugin", version="1.0.0")
        class FailingPlugin(PluginBase):
            async def setup(self, plugin_config: BaseModel) -> None:
                raise RuntimeError("Setup failed")
        
        builder = PluggingerAppBuilder(app_name="error_test")
        builder.include(FailingPlugin)
        
        app = builder.build()
        
        # Setup should handle errors gracefully
        with pytest.raises(Exception):  # ConfigurationError wraps the RuntimeError
            await app.start_all_plugins()

    @pytest.mark.asyncio
    async def test_multiple_plugins_lifecycle_order(self) -> None:
        """Test that multiple plugins are handled in correct order."""
        builder = PluggingerAppBuilder(app_name="order_test")
        builder.include(BasicTestPlugin)
        builder.include(DependentTestPlugin)  # Depends on BasicTestPlugin
        
        app = builder.build()

        # Start plugins first (plugins are only available after start)
        await app.start_all_plugins()

        # Get plugin instances (only available after start)
        basic_plugin = app.get_plugin_instance("order_test:test_basic_plugin")
        dependent_plugin = app.get_plugin_instance("order_test:dependent_plugin")

        assert basic_plugin is not None
        assert dependent_plugin is not None

        # Both should be set up
        assert basic_plugin.setup_called
        assert dependent_plugin.setup_called

        # Stop plugins
        await app.stop_all_plugins()

        # Both should be torn down
        assert basic_plugin.teardown_called
        assert dependent_plugin.teardown_called
