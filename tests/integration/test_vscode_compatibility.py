"""
VSCode-compatible version of integration tests.

This module provides simplified versions of integration tests that work
reliably in the VSCode Test Explorer.
"""

import json
from pathlib import Path

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.events import event_listener
from plugginger.api.plugin import plugin
from plugginger.api.service import service


@plugin("logger_plugin", version="1.0.0")
class LoggerPlugin:
    """Simple logger plugin for testing."""

    @service("log")
    def log_message(self, level: str, message: str) -> None:
        """Log a message."""
        pass

    @event_listener("app.startup")
    def on_startup(self, event_data: dict) -> None:
        """Handle startup event."""
        pass


@plugin("cache_plugin", version="1.0.0")
class CachePlugin:
    """Cache plugin for testing."""
    
    needs = [Depends("logger_plugin"), Depends("config", optional=True)]

    @service("get")
    def get_value(self, key: str) -> str | None:
        """Get cached value."""
        return None

    @service("set")
    def set_value(self, key: str, value: str, ttl: int = 300) -> None:
        """Set cached value."""
        pass

    @event_listener("app.startup")
    def on_startup(self, event_data: dict) -> None:
        """Handle startup event."""
        pass

    @event_listener("cache.clear")
    def on_cache_clear(self, event_data: dict) -> None:
        """Handle cache clear event."""
        pass


def create_simple_test_app() -> PluggingerAppBuilder:
    """Create a simple test app for VSCode testing."""
    builder = PluggingerAppBuilder("vscode_test_app")
    builder.include(LoggerPlugin)
    builder.include(CachePlugin)
    return builder


class TestVSCodeCompatibility:
    """VSCode-compatible integration tests."""

    def test_app_inspector_basic_functionality(self) -> None:
        """Test basic AppInspector functionality."""
        from plugginger.cli.cmd_inspect import AppInspector

        # Create test app
        app = create_simple_test_app()
        
        # Create inspector
        inspector = AppInspector(app)
        
        # Analyze app
        result = inspector.analyze()
        
        # Basic structure checks
        assert isinstance(result, dict)
        assert "app" in result
        assert "plugins" in result
        assert "dependency_graph" in result
        assert "metadata" in result

    def test_app_info_extraction(self) -> None:
        """Test app information extraction."""
        from plugginger.cli.cmd_inspect import AppInspector

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        app_info = result["app"]
        assert app_info["name"] == "vscode_test_app"
        assert app_info["plugin_count"] == 2

    def test_plugin_discovery(self) -> None:
        """Test plugin discovery."""
        from plugginger.cli.cmd_inspect import AppInspector

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        plugins = result["plugins"]
        assert len(plugins) == 2
        
        plugin_names = [p["registration_name"] for p in plugins]
        assert "logger_plugin" in plugin_names
        assert "cache_plugin" in plugin_names

    def test_service_discovery(self) -> None:
        """Test service discovery."""
        from plugginger.cli.cmd_inspect import AppInspector

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        # Find cache plugin
        cache_plugin = next(
            p for p in result["plugins"] 
            if p["registration_name"] == "cache_plugin"
        )
        
        # Check services
        services = cache_plugin["services"]
        assert len(services) == 2
        
        service_names = [s["name"] for s in services]
        assert "get" in service_names
        assert "set" in service_names

    def test_dependency_discovery(self) -> None:
        """Test dependency discovery."""
        from plugginger.cli.cmd_inspect import AppInspector

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        # Find cache plugin
        cache_plugin = next(
            p for p in result["plugins"] 
            if p["registration_name"] == "cache_plugin"
        )
        
        # Check dependencies
        dependencies = cache_plugin["dependencies"]
        assert len(dependencies) == 2
        
        dep_names = [d["name"] for d in dependencies]
        assert "logger_plugin" in dep_names
        assert "config" in dep_names

    def test_json_schema_validation(self) -> None:
        """Test JSON schema validation."""
        from plugginger.cli.cmd_inspect import AppInspector
        from plugginger.schemas.json import validate_app_graph

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        # Validate against schema
        is_valid, errors = validate_app_graph(result)
        
        # Should be valid (or validation skipped if jsonschema not available)
        if errors != ["jsonschema not available - validation skipped"]:
            assert is_valid, f"Validation errors: {errors}"

    def test_json_output_format(self) -> None:
        """Test JSON output format."""
        from plugginger.cli.cmd_inspect import AppInspector

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        # Should be serializable to JSON
        json_str = json.dumps(result, indent=2)
        assert len(json_str) > 0
        
        # Should be parseable back
        parsed = json.loads(json_str)
        assert parsed == result

    def test_metadata_generation(self) -> None:
        """Test metadata generation."""
        from plugginger.cli.cmd_inspect import AppInspector

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        metadata = result["metadata"]
        assert metadata["schema_version"] == "1.0.0"
        assert metadata["generated_by"] == "plugginger inspect"
        assert "generated_at" in metadata
        
        # Check timestamp format
        timestamp = metadata["generated_at"]
        assert "T" in timestamp
        assert timestamp.endswith("Z") or "+" in timestamp

    def test_dependency_graph_structure(self) -> None:
        """Test dependency graph structure."""
        from plugginger.cli.cmd_inspect import AppInspector

        app = create_simple_test_app()
        inspector = AppInspector(app)
        result = inspector.analyze()
        
        graph = result["dependency_graph"]
        assert "nodes" in graph
        assert "edges" in graph
        
        # Check nodes
        nodes = graph["nodes"]
        assert len(nodes) == 2
        
        node_ids = [node["id"] for node in nodes]
        assert "logger_plugin" in node_ids
        assert "cache_plugin" in node_ids
        
        # Check edges
        edges = graph["edges"]
        cache_edges = [e for e in edges if e["from"] == "cache_plugin"]
        assert len(cache_edges) > 0

    def test_schema_export_command(self) -> None:
        """Test schema export command."""
        from plugginger.cli.cmd_schema import cmd_schema
        from unittest.mock import patch

        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)
            
            # Should print JSON schema
            assert mock_print.called
            output = mock_print.call_args[0][0]
            
            # Should be valid JSON
            schema = json.loads(output)
            assert schema["$schema"] == "https://json-schema.org/draft/2020-12/schema"
            assert schema["title"] == "Plugginger App Graph"
