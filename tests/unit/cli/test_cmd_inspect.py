"""
Tests for the cmd_inspect module.

These tests verify the AppInspector functionality for analyzing
Plugginger applications and generating JSON output.
"""

import json
from typing import Any

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.cli.cmd_inspect import AppInspector


@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin for inspection tests."""

    @service()
    async def get_data(self, param: str = "default") -> str:
        """Get some test data."""
        return f"data:{param}"


@plugin(name="dependent_plugin", version="1.0.0")
class DependentPlugin(PluginBase):
    """Plugin that depends on TestPlugin."""

    needs = [Depends("test_plugin")]

    @service()
    async def process_data(self, input_data: str) -> dict[str, Any]:
        """Process input data."""
        return {"processed": input_data}

    @on_event("test.event")
    async def handle_test_event(self, event_data: dict[str, Any]) -> None:
        """Handle test events."""
        pass


class TestAppInspector:
    """Test the AppInspector class."""

    def test_inspector_initialization(self) -> None:
        """Test AppInspector initialization."""
        builder = PluggingerAppBuilder("test")
        inspector = AppInspector(builder)

        assert inspector.app == builder
        assert inspector.logger is not None

    def test_analyze_basic_app(self) -> None:
        """Test analyzing a basic application."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPlugin)

        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Check top-level structure
        assert "app" in result
        assert "plugins" in result
        assert "dependency_graph" in result
        assert "metadata" in result

        # Check app info
        app_info = result["app"]
        assert app_info["name"] == "test_app"
        assert app_info["plugin_count"] == 1

        # Check plugins
        plugins = result["plugins"]
        assert len(plugins) == 1
        plugin = plugins[0]
        assert plugin["class_name"] == "TestPlugin"
        # Module name might be just the filename when run in test context
        assert plugin["module"] in ["tests.unit.cli.test_cmd_inspect", "test_cmd_inspect", "__main__"]

    def test_extract_services(self) -> None:
        """Test extracting service definitions."""
        inspector = AppInspector(PluggingerAppBuilder("test"))
        services = inspector._extract_services(TestPlugin)

        assert len(services) == 1
        service_info = services[0]
        assert service_info["name"] == "get_data"
        assert "signature" in service_info
        assert "metadata" in service_info

    def test_extract_method_signature_with_docstring(self) -> None:
        """Test extracting method signature with docstring information."""
        from plugginger.api.plugin import PluginBase
        from plugginger.api.service import service

        class TestServicePlugin(PluginBase):
            @service()
            async def documented_service(self, param1: str, param2: int = 42) -> str:
                """
                This is a test service.

                This service demonstrates docstring extraction
                with multiple lines and parameters.
                """
                return f"{param1}:{param2}"

        inspector = AppInspector(PluggingerAppBuilder("test"))
        signature = inspector._extract_method_signature(TestServicePlugin.documented_service)

        # Check basic signature
        assert "parameters" in signature
        assert "return_type" in signature
        assert "docstring" in signature

        # Check parameters
        params = signature["parameters"]
        assert len(params) == 2

        param1 = params[0]
        assert param1["name"] == "param1"
        assert param1["type"] == "<class 'str'>"
        assert param1["default"] is None
        assert param1["kind"] == "POSITIONAL_OR_KEYWORD"

        param2 = params[1]
        assert param2["name"] == "param2"
        assert param2["type"] == "<class 'int'>"
        assert param2["default"] == "42"
        assert param2["kind"] == "POSITIONAL_OR_KEYWORD"

        # Check return type
        assert signature["return_type"] == "<class 'str'>"

        # Check docstring
        docstring = signature["docstring"]
        assert docstring is not None
        assert docstring["summary"] == "This is a test service."
        assert "demonstrates docstring extraction" in docstring["description"]
        assert "This is a test service." in docstring["raw"]

    def test_extract_method_signature_without_docstring(self) -> None:
        """Test extracting method signature without docstring."""
        from plugginger.api.plugin import PluginBase
        from plugginger.api.service import service

        class TestServicePlugin(PluginBase):
            @service()
            async def undocumented_service(self, param: str) -> None:
                pass

        inspector = AppInspector(PluggingerAppBuilder("test"))
        signature = inspector._extract_method_signature(TestServicePlugin.undocumented_service)

        assert signature["docstring"] is None

    def test_extract_docstring_info_complex(self) -> None:
        """Test docstring extraction with complex formatting."""
        from plugginger.api.plugin import PluginBase
        from plugginger.api.service import service

        class TestServicePlugin(PluginBase):
            @service()
            async def complex_service(self) -> None:
                """
                Complex service with detailed documentation.

                This service has multiple paragraphs in its description.
                It demonstrates how the docstring parser handles
                various formatting scenarios.

                Args:
                    None

                Returns:
                    None
                """
                pass

        inspector = AppInspector(PluggingerAppBuilder("test"))
        docstring_info = inspector._extract_docstring_info(TestServicePlugin.complex_service)

        assert docstring_info is not None
        assert docstring_info["summary"] == "Complex service with detailed documentation."
        assert "multiple paragraphs" in docstring_info["description"]
        assert "Args:" in docstring_info["description"]
        assert "Returns:" in docstring_info["description"]

    def test_extract_event_listeners(self) -> None:
        """Test extracting event listener definitions."""
        inspector = AppInspector(PluggingerAppBuilder("test"))
        listeners = inspector._extract_event_listeners(DependentPlugin)

        assert len(listeners) == 1
        listener = listeners[0]
        assert listener["method_name"] == "handle_test_event"
        assert listener["event_pattern"] == "test.event"
        assert "signature" in listener

    def test_extract_dependencies(self) -> None:
        """Test extracting dependency declarations."""
        inspector = AppInspector(PluggingerAppBuilder("test"))
        dependencies = inspector._extract_dependencies(DependentPlugin)

        assert len(dependencies) == 1
        dep = dependencies[0]
        assert dep["plugin_identifier"] == "test_plugin"
        assert dep["optional"] is False

    def test_extract_dependency_graph(self) -> None:
        """Test extracting dependency graph structure."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPlugin)
        builder.include(DependentPlugin)

        app = builder.build()
        inspector = AppInspector(app)
        graph = inspector._extract_dependency_graph(app)

        # Check nodes
        assert "nodes" in graph
        assert "edges" in graph
        nodes = graph["nodes"]
        assert len(nodes) == 2

        # Check edges
        edges = graph["edges"]
        assert len(edges) == 1
        edge = edges[0]
        assert edge["from"] == "dependent_plugin"
        assert edge["to"] == "test_plugin"
        assert edge["type"] == "depends_on"
        assert edge["optional"] is False

    def test_detect_dependency_cycles(self) -> None:
        """Test cycle detection in dependency graphs."""
        inspector = AppInspector(PluggingerAppBuilder("test"))

        # Create a simple cycle: A -> B -> A
        nodes = [
            {"id": "plugin_a", "type": "plugin"},
            {"id": "plugin_b", "type": "plugin"}
        ]
        edges = [
            {"from": "plugin_a", "to": "plugin_b", "type": "depends_on", "optional": False},
            {"from": "plugin_b", "to": "plugin_a", "type": "depends_on", "optional": False}
        ]

        cycles = inspector._detect_dependency_cycles(nodes, edges)

        assert len(cycles) == 1
        cycle = cycles[0]
        assert cycle["type"] == "circular_dependency"
        assert len(cycle["nodes"]) >= 2  # Should contain the cycle

    def test_detect_no_cycles(self) -> None:
        """Test cycle detection with no cycles."""
        inspector = AppInspector(PluggingerAppBuilder("test"))

        # Create a linear dependency: A -> B -> C
        nodes = [
            {"id": "plugin_a", "type": "plugin"},
            {"id": "plugin_b", "type": "plugin"},
            {"id": "plugin_c", "type": "plugin"}
        ]
        edges = [
            {"from": "plugin_a", "to": "plugin_b", "type": "depends_on", "optional": False},
            {"from": "plugin_b", "to": "plugin_c", "type": "depends_on", "optional": False}
        ]

        cycles = inspector._detect_dependency_cycles(nodes, edges)

        assert len(cycles) == 0

    def test_generate_metadata(self) -> None:
        """Test metadata generation."""
        inspector = AppInspector(PluggingerAppBuilder("test"))
        metadata = inspector._generate_metadata()

        assert "generated_at" in metadata
        assert "generated_by" in metadata
        assert "schema_version" in metadata
        assert metadata["generated_by"] == "plugginger-inspect"
        assert metadata["schema_version"] == "1.0.0"

    def test_json_serialization(self) -> None:
        """Test that the analysis result can be serialized to JSON."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPlugin)

        inspector = AppInspector(builder)
        result = inspector.analyze()

        # Should not raise an exception
        json_str = json.dumps(result, indent=2)
        assert len(json_str) > 0

        # Should be able to parse back
        parsed = json.loads(json_str)
        assert parsed["app"]["name"] == "test_app"
