"""
Unit tests for the inspect CLI command.

These tests verify the functionality of the `plugginger inspect` command
and the AppInspector class.
"""

import json
import tempfile
from pathlib import Path
from typing import Any
from unittest.mock import Mock, patch

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.cli.cmd_inspect import AppInspector, cmd_inspect
from plugginger.core.exceptions import PluginRegistrationError


@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin for inspection tests."""
    
    needs = [Depends("logger")]

    @service()
    async def get_data(self, key: str) -> str:
        """Get data by key."""
        return f"data_{key}"

    @on_event("app.startup")
    async def on_startup(self, event_data: dict[str, Any]) -> None:
        """Handle startup event."""
        pass


@plugin(name="simple_plugin", version="2.0.0")
class SimplePlugin(PluginBase):
    """Simple plugin without services or events."""
    pass


class TestAppInspector:
    """Test the AppInspector class."""

    def test_init(self) -> None:
        """Test AppInspector initialization."""
        builder = PluggingerAppBuilder("test_app")
        inspector = AppInspector(builder)
        
        assert inspector.app_builder is builder
        assert inspector.logger is not None

    def test_analyze_empty_app(self) -> None:
        """Test analyzing an app with no plugins."""
        builder = PluggingerAppBuilder("empty_app")
        inspector = AppInspector(builder)
        
        result = inspector.analyze()
        
        assert result["app"]["name"] == "empty_app"
        assert result["app"]["plugin_count"] == 0
        assert result["plugins"] == []
        assert result["dependency_graph"]["nodes"] == []
        assert result["dependency_graph"]["edges"] == []
        assert "metadata" in result
        assert result["metadata"]["schema_version"] == "1.0.0"

    def test_analyze_app_with_plugins(self) -> None:
        """Test analyzing an app with plugins."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPlugin)
        builder.include(SimplePlugin)
        
        inspector = AppInspector(builder)
        result = inspector.analyze()
        
        # Check app info
        assert result["app"]["name"] == "test_app"
        assert result["app"]["plugin_count"] == 2
        
        # Check plugins
        assert len(result["plugins"]) == 2
        
        # Find test plugin
        test_plugin_info = next(
            p for p in result["plugins"] 
            if p["registration_name"] == "test_plugin"
        )
        
        assert test_plugin_info["class_name"] == "TestPlugin"
        assert test_plugin_info["module"] == __name__
        assert len(test_plugin_info["services"]) == 1
        assert len(test_plugin_info["event_listeners"]) == 1
        assert len(test_plugin_info["dependencies"]) == 1
        
        # Check service info
        service_info = test_plugin_info["services"][0]
        assert service_info["name"] == "get_data"
        assert "signature" in service_info
        
        # Check event listener info
        listener_info = test_plugin_info["event_listeners"][0]
        assert listener_info["method_name"] == "on_startup"
        assert listener_info["event_pattern"] == "app.startup"
        
        # Check dependency info
        dep_info = test_plugin_info["dependencies"][0]
        assert dep_info["name"] == "logger"
        assert dep_info["optional"] is False

    def test_extract_app_info(self) -> None:
        """Test extracting app information."""
        builder = PluggingerAppBuilder("my_app")
        builder.include(TestPlugin)
        
        inspector = AppInspector(builder)
        app_info = inspector._extract_app_info()
        
        assert app_info["name"] == "my_app"
        assert app_info["plugin_count"] == 1
        assert "max_fractal_depth" in app_info

    def test_extract_services(self) -> None:
        """Test extracting service definitions."""
        inspector = AppInspector(PluggingerAppBuilder("test"))
        services = inspector._extract_services(TestPlugin)
        
        assert len(services) == 1
        service_info = services[0]
        assert service_info["name"] == "get_data"
        assert "signature" in service_info
        assert "metadata" in service_info

    def test_extract_event_listeners(self) -> None:
        """Test extracting event listener definitions."""
        inspector = AppInspector(PluggingerAppBuilder("test"))
        listeners = inspector._extract_event_listeners(TestPlugin)
        
        assert len(listeners) == 1
        listener_info = listeners[0]
        assert listener_info["method_name"] == "on_startup"
        assert listener_info["event_pattern"] == "app.startup"
        assert "signature" in listener_info

    def test_extract_method_signature(self) -> None:
        """Test extracting method signatures."""
        inspector = AppInspector(PluggingerAppBuilder("test"))
        
        # Test with TestPlugin.get_data method
        method = TestPlugin.get_data
        signature = inspector._extract_method_signature(method)
        
        assert "parameters" in signature
        assert "return_type" in signature
        
        # Should have one parameter (key: str)
        params = signature["parameters"]
        assert len(params) == 1
        assert params[0]["name"] == "key"
        assert "str" in params[0]["type"]

    def test_extract_dependency_graph(self) -> None:
        """Test extracting dependency graph."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPlugin)
        builder.include(SimplePlugin)
        
        inspector = AppInspector(builder)
        graph = inspector._extract_dependency_graph()
        
        # Should have 2 nodes
        assert len(graph["nodes"]) == 2
        node_ids = [node["id"] for node in graph["nodes"]]
        assert "test_plugin" in node_ids
        assert "simple_plugin" in node_ids
        
        # Should have dependency edges for TestPlugin
        edges = graph["edges"]
        test_plugin_edges = [e for e in edges if e["from"] == "test_plugin"]
        assert len(test_plugin_edges) >= 1  # At least the logger dependency

    def test_analyze_with_error(self) -> None:
        """Test analyze method with error handling."""
        builder = PluggingerAppBuilder("test_app")
        inspector = AppInspector(builder)
        
        # Mock a method to raise an exception
        with patch.object(inspector, '_extract_app_info', side_effect=Exception("Test error")):
            with pytest.raises(PluginRegistrationError, match="Failed to analyze app"):
                inspector.analyze()


class TestCmdInspect:
    """Test the cmd_inspect function."""

    def test_cmd_inspect_json_output(self) -> None:
        """Test inspect command with JSON output."""
        # Create a mock app factory
        def mock_factory() -> PluggingerAppBuilder:
            builder = PluggingerAppBuilder("test_app")
            builder.include(TestPlugin)
            return builder
        
        with patch('plugginger.cli.cmd_inspect.resolve_app_factory', return_value=mock_factory()):
            with patch('builtins.print') as mock_print:
                cmd_inspect("test.module:factory", output_json=True, output_file=None)
                
                # Should print JSON output
                mock_print.assert_called_once()
                output = mock_print.call_args[0][0]
                
                # Verify it's valid JSON
                parsed = json.loads(output)
                assert parsed["app"]["name"] == "test_app"
                assert len(parsed["plugins"]) == 1

    def test_cmd_inspect_human_readable_output(self) -> None:
        """Test inspect command with human-readable output."""
        # Create a mock app factory
        def mock_factory() -> PluggingerAppBuilder:
            builder = PluggingerAppBuilder("test_app")
            builder.include(TestPlugin)
            return builder
        
        with patch('plugginger.cli.cmd_inspect.resolve_app_factory', return_value=mock_factory()):
            with patch('builtins.print') as mock_print:
                cmd_inspect("test.module:factory", output_json=False, output_file=None)
                
                # Should print human-readable output
                assert mock_print.call_count > 1  # Multiple print calls for formatting

    def test_cmd_inspect_output_to_file(self) -> None:
        """Test inspect command with file output."""
        # Create a mock app factory
        def mock_factory() -> PluggingerAppBuilder:
            builder = PluggingerAppBuilder("test_app")
            builder.include(TestPlugin)
            return builder
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as tmp_file:
            output_file = Path(tmp_file.name)
        
        try:
            with patch('plugginger.cli.cmd_inspect.resolve_app_factory', return_value=mock_factory()):
                with patch('builtins.print') as mock_print:
                    cmd_inspect("test.module:factory", output_json=True, output_file=output_file)
                    
                    # Should print success message
                    mock_print.assert_called_once()
                    assert "written to" in mock_print.call_args[0][0]
                    
                    # Check file content
                    content = output_file.read_text(encoding='utf-8')
                    parsed = json.loads(content)
                    assert parsed["app"]["name"] == "test_app"
        finally:
            output_file.unlink(missing_ok=True)

    def test_cmd_inspect_with_error(self) -> None:
        """Test inspect command error handling."""
        with patch('plugginger.cli.cmd_inspect.resolve_app_factory', side_effect=Exception("Test error")):
            with pytest.raises(Exception, match="Test error"):
                cmd_inspect("test.module:factory", output_json=True, output_file=None)


class TestHumanReadableOutput:
    """Test human-readable output formatting."""

    def test_print_human_readable_analysis(self) -> None:
        """Test human-readable output formatting."""
        from plugginger.cli.cmd_inspect import _print_human_readable_analysis
        
        analysis = {
            "app": {
                "name": "test_app",
                "plugin_count": 2,
                "max_fractal_depth": 3
            },
            "plugins": [
                {
                    "registration_name": "plugin1",
                    "class_name": "Plugin1",
                    "services": [{"name": "service1"}],
                    "event_listeners": [{"method_name": "on_event", "event_pattern": "test.event"}],
                    "dependencies": [{"name": "dep1"}]
                },
                {
                    "registration_name": "plugin2",
                    "class_name": "Plugin2",
                    "services": [],
                    "event_listeners": [],
                    "dependencies": []
                }
            ],
            "dependency_graph": {
                "edges": [
                    {"from": "plugin1", "to": "dep1", "optional": False}
                ]
            }
        }
        
        with patch('builtins.print') as mock_print:
            _print_human_readable_analysis(analysis)
            
            # Should print multiple lines
            assert mock_print.call_count > 5
            
            # Check some expected content
            printed_text = ' '.join(call[0][0] for call in mock_print.call_args_list)
            assert "test_app" in printed_text
            assert "plugin1" in printed_text
            assert "plugin2" in printed_text
