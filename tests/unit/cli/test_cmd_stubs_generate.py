import pytest
from pathlib import Path
import tempfile
from unittest.mock import patch

from plugginger.cli.cmd_stubs_generate import cmd_stubs_generate


def test_cmd_stubs_generate() -> None:
    with tempfile.TemporaryDirectory() as tmpdir:
        output_directory = Path(tmpdir)
        with patch("plugginger.cli.cmd_stubs_generate._generate_stubs_for_app") as mock_generate_stubs_for_app:
            cmd_stubs_generate(factory_path="plugginger.api.app.PluggingerAppInstance", output_dir_option=output_directory, watch=False)
            mock_generate_stubs_for_app.assert_called_once_with("plugginger.api.app.PluggingerAppInstance", output_directory)


def test_cmd_stubs_generate_watch() -> None:
    with tempfile.TemporaryDirectory() as tmpdir:
        output_directory = Path(tmpdir)
        with patch("plugginger.cli.cmd_stubs_generate._watch_and_regenerate_stubs") as mock_watch_and_regenerate_stubs:
            cmd_stubs_generate(factory_path="plugginger.api.app.PluggingerAppInstance", output_dir_option=output_directory, watch=True)
            mock_watch_and_regenerate_stubs.assert_called_once_with("plugginger.api.app.PluggingerAppInstance", output_directory)


def test_cmd_stubs_generate_exception() -> None:
    with tempfile.TemporaryDirectory() as tmpdir:
        output_directory = Path(tmpdir)
        with patch("plugginger.cli.cmd_stubs_generate._generate_stubs_for_app", side_effect=Exception("Test exception")):
            with pytest.raises(Exception, match="Test exception"):
                cmd_stubs_generate(factory_path="plugginger.api.app.PluggingerAppInstance", output_dir_option=output_directory, watch=False)
