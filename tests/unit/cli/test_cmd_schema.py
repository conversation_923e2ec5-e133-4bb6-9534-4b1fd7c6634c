"""
Unit tests for the schema CLI command.

These tests verify the functionality of the `plugginger schema` command
for exporting JSON schemas.
"""

import json
import tempfile
from pathlib import Path
from typing import Any
from unittest.mock import patch

import pytest

from plugginger.cli.cmd_schema import cmd_schema


class TestCmdSchema:
    """Test the cmd_schema function."""

    def test_cmd_schema_app_graph_stdout(self) -> None:
        """Test schema command with app-graph output to stdout."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            # Should print JSON schema
            mock_print.assert_called_once()
            output = mock_print.call_args[0][0]

            # Verify it's valid JSON
            parsed = json.loads(output)
            assert parsed["$schema"] == "https://json-schema.org/draft/2020-12/schema"
            assert parsed["title"] == "Plugginger App Graph"

    def test_cmd_schema_app_graph_file_output(self) -> None:
        """Test schema command with app-graph output to file."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as tmp_file:
            output_file = Path(tmp_file.name)

        try:
            with patch('builtins.print') as mock_print:
                cmd_schema("app-graph", output_file=output_file)

                # Should print success message
                mock_print.assert_called_once()
                assert "written to" in mock_print.call_args[0][0]

                # Check file content
                content = output_file.read_text(encoding='utf-8')
                parsed = json.loads(content)
                assert parsed["$schema"] == "https://json-schema.org/draft/2020-12/schema"
                assert parsed["title"] == "Plugginger App Graph"
        finally:
            output_file.unlink(missing_ok=True)

    def test_cmd_schema_invalid_type(self) -> None:
        """Test schema command with invalid schema type."""
        with pytest.raises(ValueError, match="Unknown schema type"):
            cmd_schema("invalid-type", output_file=None)

    def test_cmd_schema_file_write_error(self) -> None:
        """Test schema command with file write error."""
        # Use a directory path instead of file path to cause write error
        invalid_path = Path("/nonexistent/directory/schema.json")

        with pytest.raises(Exception):
            cmd_schema("app-graph", output_file=invalid_path)


class TestSchemaCommandIntegration:
    """Integration tests for the schema command."""

    def test_schema_output_structure(self) -> None:
        """Test that schema output has correct structure."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            # Verify top-level structure
            assert "$schema" in schema
            assert "$id" in schema
            assert "title" in schema
            assert "description" in schema
            assert "type" in schema
            assert "required" in schema
            assert "properties" in schema
            assert "$defs" in schema

    def test_schema_required_properties(self) -> None:
        """Test that schema defines all required properties."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            # Check required top-level properties
            required = schema["required"]
            assert "app" in required
            assert "plugins" in required
            assert "dependency_graph" in required
            assert "metadata" in required

    def test_schema_definitions(self) -> None:
        """Test that schema contains all necessary definitions."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            # Check definitions
            defs = schema["$defs"]
            expected_defs = [
                "plugin",
                "service",
                "event_listener",
                "dependency",
                "method_signature",
                "parameter",
                "graph_node",
                "graph_edge"
            ]

            for def_name in expected_defs:
                assert def_name in defs, f"Missing definition: {def_name}"

    def test_schema_plugin_definition(self) -> None:
        """Test plugin definition in schema."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            plugin_def = schema["$defs"]["plugin"]
            assert plugin_def["type"] == "object"

            # Check required fields
            required = plugin_def["required"]
            assert "registration_name" in required
            assert "class_name" in required
            assert "module" in required
            assert "services" in required
            assert "event_listeners" in required
            assert "dependencies" in required
            assert "metadata" in required

    def test_schema_service_definition(self) -> None:
        """Test service definition in schema."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            service_def = schema["$defs"]["service"]
            assert service_def["type"] == "object"

            # Check required fields
            required = service_def["required"]
            assert "name" in required
            assert "method_name" in required
            assert "signature" in required
            assert "metadata" in required

    def test_schema_dependency_graph_definitions(self) -> None:
        """Test dependency graph definitions in schema."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            # Check graph node definition
            node_def = schema["$defs"]["graph_node"]
            assert node_def["type"] == "object"
            assert "id" in node_def["required"]
            assert "type" in node_def["required"]

            # Check graph edge definition
            edge_def = schema["$defs"]["graph_edge"]
            assert edge_def["type"] == "object"
            assert "from" in edge_def["required"]
            assert "to" in edge_def["required"]
            assert "type" in edge_def["required"]
            assert "optional" in edge_def["required"]

    def test_schema_method_signature_definition(self) -> None:
        """Test method signature definition in schema."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            signature_def = schema["$defs"]["method_signature"]
            assert signature_def["type"] == "object"

            # Check required fields
            required = signature_def["required"]
            assert "parameters" in required
            assert "return_type" in required

            # Check parameter definition
            parameter_def = schema["$defs"]["parameter"]
            assert parameter_def["type"] == "object"
            param_required = parameter_def["required"]
            assert "name" in param_required
            assert "kind" in param_required

    def test_schema_validates_against_meta_schema(self) -> None:
        """Test that our schema is valid according to JSON Schema meta-schema."""
        with patch('builtins.print') as mock_print:
            cmd_schema("app-graph", output_file=None)

            output = mock_print.call_args[0][0]
            schema = json.loads(output)

            # Basic validation - should not raise exceptions
            assert isinstance(schema, dict)
            assert schema["$schema"] == "https://json-schema.org/draft/2020-12/schema"

            # Check that all references are valid
            def check_refs(obj: dict[str, Any] | list[Any] | Any, path: str = "") -> None:
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if key == "$ref":
                            ref = value
                            if ref.startswith("#/$defs/"):
                                def_name = ref[8:]  # Remove "#/$defs/"
                                assert def_name in schema["$defs"], f"Invalid reference {ref} at {path}"
                        else:
                            check_refs(value, f"{path}.{key}")
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        check_refs(item, f"{path}[{i}]")

            check_refs(schema)
