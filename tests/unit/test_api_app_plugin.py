# tests/unit/test_api_app_plugin.py

"""
Unit tests for plugginger.api.app_plugin module.

Tests the AppPluginBase class and its fractal composition functionality,
event bridging, and lifecycle management.
"""

from __future__ import annotations

import asyncio
import logging
from typing import Any
from unittest.mock import AsyncMock, Mock, patch

import pytest
from pydantic import BaseModel

from plugginger.api.app_plugin import AppPluginBase
from plugginger.core.constants import PLUGIN_METADATA_KEY
from plugginger.core.exceptions import AppPluginError


# Mock classes for testing
class MockConfig(BaseModel):
    """Mock configuration for testing."""
    test_value: str = "default"
    internal_app_config: dict[str, Any] = {}


class TestAppPlugin(AppPluginBase):
    """Test implementation of AppPluginBase."""

    def _configure_internal_app(self) -> None:
        """Configure a mock internal app."""
        mock_app = Mock()
        mock_app.app_name = "test_internal_app"
        mock_app.start_all_plugins = AsyncMock()
        mock_app.stop_all_plugins = AsyncMock()
        mock_app.emit_event = AsyncMock()
        self._internal_app = mock_app


class FailingAppPlugin(AppPluginBase):
    """AppPlugin that fails to configure internal app."""

    def _configure_internal_app(self) -> None:
        """Intentionally fail to set _internal_app."""
        pass  # Don't set self._internal_app


@pytest.fixture
def mock_outer_app() -> Mock:
    """Create a mock outer app instance."""
    app = Mock()
    app.app_name = "outer_app"
    app._current_build_depth_for_sub_apps = 1
    app._max_build_depth_for_sub_apps = 5
    app.emit_event = AsyncMock()
    return app


@pytest.fixture
def test_app_plugin(mock_outer_app: Mock) -> TestAppPlugin:
    """Create a test app plugin instance."""
    # Add plugin metadata
    setattr(TestAppPlugin, PLUGIN_METADATA_KEY, True)
    setattr(TestAppPlugin, "_plugginger_plugin_name", "test_app_plugin")
    setattr(TestAppPlugin, "_plugginger_plugin_version", "1.0.0")
    setattr(TestAppPlugin, "_plugginger_config_schema", None)
    setattr(TestAppPlugin, "_plugginger_instance_id", "test_instance")

    return TestAppPlugin(mock_outer_app)


class TestAppPluginBaseInit:
    """Test AppPluginBase initialization."""

    def test_init_success(self, mock_outer_app: Mock) -> None:
        """Test successful initialization."""
        # Add plugin metadata
        setattr(TestAppPlugin, PLUGIN_METADATA_KEY, True)
        setattr(TestAppPlugin, "_plugginger_plugin_name", "test_plugin")
        setattr(TestAppPlugin, "_plugginger_plugin_version", "1.0.0")
        setattr(TestAppPlugin, "_plugginger_config_schema", None)
        setattr(TestAppPlugin, "_plugginger_instance_id", "test_id")

        plugin = TestAppPlugin(mock_outer_app)

        assert plugin.app is mock_outer_app
        assert plugin._internal_app is not None
        assert plugin._internal_app.app_name == "test_internal_app"
        assert isinstance(plugin._event_bridges_config, list)
        assert len(plugin._event_bridges_config) == 0
        assert isinstance(plugin._logger, logging.Logger)
        assert plugin._logger.name == "plugginger.app_plugin.TestAppPlugin"

    def test_init_with_dependencies(self, mock_outer_app: Mock) -> None:
        """Test initialization with injected dependencies."""
        # Add plugin metadata
        setattr(TestAppPlugin, PLUGIN_METADATA_KEY, True)
        setattr(TestAppPlugin, "_plugginger_plugin_name", "test_plugin")
        setattr(TestAppPlugin, "_plugginger_plugin_version", "1.0.0")
        setattr(TestAppPlugin, "_plugginger_config_schema", None)
        setattr(TestAppPlugin, "_plugginger_instance_id", "test_id")

        dependencies = {"dep1": "value1", "dep2": "value2"}
        plugin = TestAppPlugin(mock_outer_app, **dependencies)

        assert plugin.app is mock_outer_app
        assert plugin._internal_app is not None

    def test_init_fails_when_internal_app_not_configured(self, mock_outer_app: Mock) -> None:
        """Test that initialization fails when internal app is not configured."""
        # Add plugin metadata
        setattr(FailingAppPlugin, PLUGIN_METADATA_KEY, True)
        setattr(FailingAppPlugin, "_plugginger_plugin_name", "failing_plugin")
        setattr(FailingAppPlugin, "_plugginger_plugin_version", "1.0.0")
        setattr(FailingAppPlugin, "_plugginger_config_schema", None)
        setattr(FailingAppPlugin, "_plugginger_instance_id", "failing_id")

        with pytest.raises(AppPluginError) as exc_info:
            FailingAppPlugin(mock_outer_app)

        assert "failed to configure internal app" in str(exc_info.value)
        assert "_configure_internal_app() must set self._internal_app" in str(exc_info.value)


class TestAppPluginBaseLifecycle:
    """Test AppPluginBase lifecycle methods."""

    @pytest.mark.asyncio
    async def test_setup_success(self, test_app_plugin: TestAppPlugin) -> None:
        """Test successful setup."""
        config = MockConfig(test_value="setup_test")

        await test_app_plugin.setup(config)

        # Verify internal app was started
        test_app_plugin._internal_app.start_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_with_internal_app_config(self, test_app_plugin: TestAppPlugin) -> None:
        """Test setup with internal app configuration."""
        config = MockConfig(
            test_value="config_test",
            internal_app_config={"setting": "value"}
        )

        await test_app_plugin.setup(config)

        # Should still start internal app
        test_app_plugin._internal_app.start_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_handles_internal_app_start_failure(self, test_app_plugin: TestAppPlugin) -> None:
        """Test setup handles internal app startup failure."""
        config = MockConfig()
        test_app_plugin._internal_app.start_all_plugins.side_effect = RuntimeError("Start failed")

        with pytest.raises(AppPluginError) as exc_info:
            await test_app_plugin.setup(config)

        assert "Failed to set up AppPlugin" in str(exc_info.value)
        assert "Start failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_setup_with_none_internal_app(self, test_app_plugin: TestAppPlugin) -> None:
        """Test setup when internal app is None."""
        config = MockConfig()
        test_app_plugin._internal_app = None

        # Should not raise error, just skip starting
        await test_app_plugin.setup(config)

    @pytest.mark.asyncio
    async def test_teardown_success(self, test_app_plugin: TestAppPlugin) -> None:
        """Test successful teardown."""
        await test_app_plugin.teardown()

        # Verify internal app was stopped
        test_app_plugin._internal_app.stop_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_teardown_handles_internal_app_stop_failure(self, test_app_plugin: TestAppPlugin) -> None:
        """Test teardown handles internal app stop failure."""
        test_app_plugin._internal_app.stop_all_plugins.side_effect = RuntimeError("Stop failed")

        with pytest.raises(AppPluginError) as exc_info:
            await test_app_plugin.teardown()

        assert "Failed to tear down AppPlugin" in str(exc_info.value)
        assert "Stop failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_teardown_with_none_internal_app(self, test_app_plugin: TestAppPlugin) -> None:
        """Test teardown when internal app is None."""
        test_app_plugin._internal_app = None

        # Should not raise error, just skip stopping
        await test_app_plugin.teardown()


class TestAppPluginBaseEventBridging:
    """Test AppPluginBase event bridging functionality."""

    def test_bridge_internal_event_pattern_basic(self, test_app_plugin: TestAppPlugin) -> None:
        """Test basic internal event pattern bridging."""
        test_app_plugin.bridge_internal_event_pattern("user.*", "user_mgmt")

        assert len(test_app_plugin._event_bridges_config) == 1
        bridge = test_app_plugin._event_bridges_config[0]
        assert bridge["direction"] == "internal_to_external"
        assert bridge["internal_pattern"] == "user.*"
        assert bridge["external_prefix"] == "user_mgmt"
        assert bridge["transformer"] is None

    def test_bridge_internal_event_pattern_with_transformer(self, test_app_plugin: TestAppPlugin) -> None:
        """Test internal event pattern bridging with transformer."""
        def transform_data(data: dict[str, Any], event_type: str) -> dict[str, Any]:
            return {"transformed": True, "original": data}

        test_app_plugin.bridge_internal_event_pattern("user.*", "user_mgmt", transform_data)

        bridge = test_app_plugin._event_bridges_config[0]
        assert bridge["transformer"] is transform_data

    def test_bridge_internal_event_pattern_default_prefix(self, test_app_plugin: TestAppPlugin) -> None:
        """Test internal event pattern bridging with default prefix."""
        test_app_plugin.bridge_internal_event_pattern("user.*")

        bridge = test_app_plugin._event_bridges_config[0]
        assert bridge["external_prefix"] == "test_instance"  # Uses _plugginger_instance_id

    def test_bridge_internal_event_pattern_invalid_pattern(self, test_app_plugin: TestAppPlugin) -> None:
        """Test error handling for invalid internal event pattern."""
        with pytest.raises(ValueError) as exc_info:
            test_app_plugin.bridge_internal_event_pattern("", "prefix")

        assert "internal_event_pattern must be a non-empty string" in str(exc_info.value)

        with pytest.raises(ValueError) as exc_info:
            test_app_plugin.bridge_internal_event_pattern(None, "prefix")  # type: ignore[arg-type]

        assert "internal_event_pattern must be a non-empty string" in str(exc_info.value)

    def test_bridge_external_event_to_internal_basic(self, test_app_plugin: TestAppPlugin) -> None:
        """Test basic external to internal event bridging."""
        test_app_plugin.bridge_external_event_to_internal("system.shutdown", "internal.shutdown")

        assert len(test_app_plugin._event_bridges_config) == 1
        bridge = test_app_plugin._event_bridges_config[0]
        assert bridge["direction"] == "external_to_internal"
        assert bridge["external_pattern"] == "system.shutdown"
        assert bridge["to_internal_type"] == "internal.shutdown"
        assert bridge["transformer"] is None

    def test_bridge_external_event_to_internal_with_transformer(self, test_app_plugin: TestAppPlugin) -> None:
        """Test external to internal event bridging with transformer."""
        def transform_data(data: dict[str, Any], event_type: str) -> dict[str, Any]:
            return {"source": "external", "data": data}

        test_app_plugin.bridge_external_event_to_internal(
            "system.shutdown", "internal.shutdown", transform_data
        )

        bridge = test_app_plugin._event_bridges_config[0]
        assert bridge["transformer"] is transform_data

    def test_bridge_external_event_to_internal_invalid_parameters(self, test_app_plugin: TestAppPlugin) -> None:
        """Test error handling for invalid external event bridging parameters."""
        with pytest.raises(ValueError) as exc_info:
            test_app_plugin.bridge_external_event_to_internal("", "internal.event")

        assert "external_event_pattern must be a non-empty string" in str(exc_info.value)

        with pytest.raises(ValueError) as exc_info:
            test_app_plugin.bridge_external_event_to_internal("external.event", "")

        assert "to_internal_event_type must be a non-empty string" in str(exc_info.value)

    def test_multiple_event_bridges(self, test_app_plugin: TestAppPlugin) -> None:
        """Test configuring multiple event bridges."""
        test_app_plugin.bridge_internal_event_pattern("user.*", "user_mgmt")
        test_app_plugin.bridge_external_event_to_internal("system.shutdown", "internal.shutdown")
        test_app_plugin.bridge_internal_event_pattern("admin.*", "admin_mgmt")

        assert len(test_app_plugin._event_bridges_config) == 3

        # Check first bridge
        bridge1 = test_app_plugin._event_bridges_config[0]
        assert bridge1["direction"] == "internal_to_external"
        assert bridge1["internal_pattern"] == "user.*"

        # Check second bridge
        bridge2 = test_app_plugin._event_bridges_config[1]
        assert bridge2["direction"] == "external_to_internal"
        assert bridge2["external_pattern"] == "system.shutdown"

        # Check third bridge
        bridge3 = test_app_plugin._event_bridges_config[2]
        assert bridge3["direction"] == "internal_to_external"
        assert bridge3["internal_pattern"] == "admin.*"


class TestAppPluginBaseEventBridgeExecution:
    """Test AppPluginBase event bridge execution methods."""

    @patch('asyncio.create_task')
    def test_execute_internal_to_external_bridge_wildcard_pattern(self, mock_create_task: Mock, test_app_plugin: TestAppPlugin) -> None:
        """Test executing internal to external bridge with wildcard pattern."""
        # Configure bridge
        bridge_config = {
            "direction": "internal_to_external",
            "internal_pattern": "user.*",
            "external_prefix": "user_mgmt",
            "transformer": None
        }

        # Execute bridge
        test_app_plugin._execute_internal_to_external_bridge(
            "user.login.success",
            {"user_id": "123", "timestamp": "2023-01-01"},
            bridge_config
        )

        # Verify asyncio.create_task was called with emit_event coroutine
        mock_create_task.assert_called_once()

    @patch('asyncio.create_task')
    def test_execute_internal_to_external_bridge_transformer_error(self, mock_create_task: Mock, test_app_plugin: TestAppPlugin) -> None:
        """Test executing internal to external bridge with transformer error."""
        def failing_transformer(data: dict[str, Any], event_type: str) -> dict[str, Any]:
            raise ValueError("Transformer failed")

        bridge_config = {
            "direction": "internal_to_external",
            "internal_pattern": "user.*",
            "external_prefix": "user_mgmt",
            "transformer": failing_transformer
        }

        # Should not raise, but log error
        test_app_plugin._execute_internal_to_external_bridge(
            "user.test",
            {"data": "test"},
            bridge_config
        )

        # Should not have called create_task due to error
        mock_create_task.assert_not_called()

    @pytest.mark.asyncio
    async def test_execute_external_to_internal_bridge_no_internal_app(self, test_app_plugin: TestAppPlugin) -> None:
        """Test executing external to internal bridge when internal app is None."""
        test_app_plugin._internal_app = None

        bridge_config = {
            "direction": "external_to_internal",
            "external_pattern": "system.shutdown",
            "to_internal_type": "internal.shutdown",
            "transformer": None
        }

        # Should not raise error
        await test_app_plugin._execute_external_to_internal_bridge(
            "system.shutdown",
            {"reason": "test"},
            bridge_config
        )


class TestAppPluginBaseIntegration:
    """Test AppPluginBase integration scenarios."""

    def test_configure_event_bridges_called_during_init(self, mock_outer_app: Mock) -> None:
        """Test that _configure_event_bridges is called during initialization."""
        class EventBridgeTestPlugin(AppPluginBase):
            def __init__(self, app: Any, **kwargs: Any) -> None:
                self.bridges_configured = False
                super().__init__(app, **kwargs)

            def _configure_internal_app(self) -> None:
                mock_app = Mock()
                mock_app.app_name = "test_app"
                mock_app.start_all_plugins = AsyncMock()
                mock_app.stop_all_plugins = AsyncMock()
                self._internal_app = mock_app

            def _configure_event_bridges(self) -> None:
                self.bridges_configured = True
                self.bridge_internal_event_pattern("test.*", "test_prefix")

        # Add plugin metadata
        setattr(EventBridgeTestPlugin, PLUGIN_METADATA_KEY, True)
        setattr(EventBridgeTestPlugin, "_plugginger_plugin_name", "bridge_test")
        setattr(EventBridgeTestPlugin, "_plugginger_plugin_version", "1.0.0")
        setattr(EventBridgeTestPlugin, "_plugginger_config_schema", None)
        setattr(EventBridgeTestPlugin, "_plugginger_instance_id", "bridge_id")

        plugin = EventBridgeTestPlugin(mock_outer_app)

        assert plugin.bridges_configured is True
        assert len(plugin._event_bridges_config) == 1

    def test_event_bridge_integration_setup(self, mock_outer_app: Mock) -> None:
        """Test event bridge setup during initialization."""
        class FullLifecyclePlugin(AppPluginBase):
            def _configure_internal_app(self) -> None:
                mock_app = Mock()
                mock_app.app_name = "lifecycle_app"
                mock_app.start_all_plugins = AsyncMock()
                mock_app.stop_all_plugins = AsyncMock()
                mock_app.emit_event = AsyncMock()
                self._internal_app = mock_app

            def _configure_event_bridges(self) -> None:
                self.bridge_internal_event_pattern("internal.*", "external")
                self.bridge_external_event_to_internal("external.command", "internal.command")

        # Add plugin metadata
        setattr(FullLifecyclePlugin, PLUGIN_METADATA_KEY, True)
        setattr(FullLifecyclePlugin, "_plugginger_plugin_name", "lifecycle_test")
        setattr(FullLifecyclePlugin, "_plugginger_plugin_version", "1.0.0")
        setattr(FullLifecyclePlugin, "_plugginger_config_schema", None)
        setattr(FullLifecyclePlugin, "_plugginger_instance_id", "lifecycle_id")

        # Initialize
        plugin = FullLifecyclePlugin(mock_outer_app)
        assert plugin._internal_app is not None
        assert len(plugin._event_bridges_config) == 2

    def test_logger_configuration(self, test_app_plugin: TestAppPlugin) -> None:
        """Test that logger is properly configured."""
        assert isinstance(test_app_plugin._logger, logging.Logger)
        assert test_app_plugin._logger.name == "plugginger.app_plugin.TestAppPlugin"

    def test_abstract_method_enforcement(self, mock_outer_app: Mock) -> None:
        """Test that abstract method _configure_internal_app must be implemented."""
        # This test verifies that AppPluginBase is properly abstract
        # In practice, trying to instantiate AppPluginBase directly should fail
        # but since we're testing with concrete subclasses, we verify the abstract nature

        assert hasattr(AppPluginBase, '_configure_internal_app')
        assert getattr(AppPluginBase._configure_internal_app, '__isabstractmethod__', False)

    def test_event_bridge_config_structure(self, test_app_plugin: TestAppPlugin) -> None:
        """Test event bridge configuration structure."""
        # Configure different types of bridges
        test_app_plugin.bridge_internal_event_pattern("user.*", "user_mgmt")
        test_app_plugin.bridge_external_event_to_internal("system.shutdown", "internal.shutdown")

        # Verify structure
        assert len(test_app_plugin._event_bridges_config) == 2

        internal_bridge = test_app_plugin._event_bridges_config[0]
        assert "direction" in internal_bridge
        assert "internal_pattern" in internal_bridge
        assert "external_prefix" in internal_bridge
        assert "transformer" in internal_bridge

        external_bridge = test_app_plugin._event_bridges_config[1]
        assert "direction" in external_bridge
        assert "external_pattern" in external_bridge
        assert "to_internal_type" in external_bridge
        assert "transformer" in external_bridge
