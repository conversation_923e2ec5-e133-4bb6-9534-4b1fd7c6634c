"""
Tests for manifest validation integration in PluggingerAppBuilder.

These tests verify that the builder correctly integrates manifest validation
into the plugin loading process.
"""

import pytest


class TestBuilderManifestValidation:
    """Test manifest validation integration in PluggingerAppBuilder."""
    
    def test_builder_has_manifest_validation_methods(self) -> None:
        """Test that builder has manifest validation methods."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        
        assert hasattr(builder, "enable_manifest_validation")
        assert hasattr(builder, "disable_manifest_validation")
        assert callable(getattr(builder, "enable_manifest_validation"))
        assert callable(getattr(builder, "disable_manifest_validation"))
    
    def test_enable_manifest_validation(self) -> None:
        """Test enabling manifest validation."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        
        # Initially disabled
        assert not builder._manifest_validation_enabled
        
        # Enable validation
        result = builder.enable_manifest_validation()
        
        # Should return builder for chaining
        assert result is builder
        assert builder._manifest_validation_enabled
    
    def test_disable_manifest_validation(self) -> None:
        """Test disabling manifest validation."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        
        # Enable first
        builder.enable_manifest_validation()
        assert builder._manifest_validation_enabled
        
        # Disable
        result = builder.disable_manifest_validation()
        
        assert result is builder
        assert not builder._manifest_validation_enabled
    
    def test_manifest_validation_disabled_by_default(self) -> None:
        """Test that manifest validation is disabled by default."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        
        assert not builder._manifest_validation_enabled
        assert hasattr(builder, '_manifest_cache')
        assert isinstance(builder._manifest_cache, dict)
    
    def test_manifest_validator_initialization(self) -> None:
        """Test that manifest validator is properly initialized."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("test_app")

        assert hasattr(builder, '_manifest_validator')
        # Validator is None initially (lazy initialization)
        assert builder._manifest_validator is None

        # After getting validator, it should be initialized
        validator = builder._get_manifest_validator()
        assert validator is not None
        assert builder._manifest_validator is not None
    
    def test_internal_methods_exist(self) -> None:
        """Test that internal manifest validation methods exist."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        
        assert hasattr(builder, "_validate_plugin_manifests")
        assert callable(getattr(builder, "_validate_plugin_manifests"))
        assert hasattr(builder, "_load_or_generate_manifest")
        assert callable(getattr(builder, "_load_or_generate_manifest"))


class TestManifestValidationAPI:
    """Test the public API for manifest validation."""
    
    def test_manifest_validator_import(self) -> None:
        """Test that ManifestValidator can be imported."""
        from plugginger import ManifestValidator
        
        validator = ManifestValidator()
        assert validator is not None
    
    def test_validate_plugin_manifest_consistency_import(self) -> None:
        """Test that validate_plugin_manifest_consistency can be imported."""
        from plugginger import validate_plugin_manifest_consistency
        
        assert callable(validate_plugin_manifest_consistency)


class TestManifestValidationBasicFunctionality:
    """Test basic functionality without complex plugin interactions."""
    
    def test_builder_build_without_validation(self) -> None:
        """Test building app without manifest validation (default behavior)."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        
        # Should build successfully without validation and without plugins
        app = builder.build()
        assert app is not None
        assert app.app_name == "test_app"
    
    def test_builder_build_with_validation_enabled_no_plugins(self) -> None:
        """Test building app with manifest validation enabled but no plugins."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        builder.enable_manifest_validation()
        
        # Should build successfully even with validation enabled but no plugins
        app = builder.build()
        assert app is not None
        assert app.app_name == "test_app"
    
    def test_fluent_interface(self) -> None:
        """Test that manifest validation methods support fluent interface."""
        from plugginger.api.builder import PluggingerAppBuilder
        
        builder = PluggingerAppBuilder("test_app")
        
        # Should be able to chain method calls
        result = (builder
                 .enable_manifest_validation()
                 .disable_manifest_validation()
                 .enable_manifest_validation())
        
        assert result is builder
        assert builder._manifest_validation_enabled
