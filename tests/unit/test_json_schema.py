"""
Tests for JSON schema validation and structure.

These tests verify the app graph JSON schema and validation functionality.
"""

from typing import Any

import pytest

from plugginger.schemas.json.app_graph import APP_GRAPH_SCHEMA, validate_app_graph


class TestAppGraphSchema:
    """Test the app graph JSON schema."""

    def test_schema_structure(self) -> None:
        """Test basic schema structure."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        
        assert schema["$schema"] == "https://json-schema.org/draft/2020-12/schema"
        assert schema["type"] == "object"
        assert "app" in schema["required"]
        assert "plugins" in schema["required"]
        assert "dependency_graph" in schema["required"]
        assert "metadata" in schema["required"]

    def test_validate_app_graph_with_valid_data(self) -> None:
        """Test validation with valid app graph data."""
        valid_data = {
            "app": {
                "name": "test_app",
                "plugin_count": 1,
                "max_fractal_depth": None
            },
            "plugins": [
                {
                    "registration_name": "test_plugin",
                    "class_name": "TestPlugin",
                    "module": "test.module",
                    "services": [],
                    "event_listeners": [],
                    "dependencies": [],
                    "metadata": {}
                }
            ],
            "dependency_graph": {
                "nodes": [
                    {
                        "id": "test_plugin",
                        "type": "plugin"
                    }
                ],
                "edges": []
            },
            "metadata": {
                "generated_at": "2025-01-01T00:00:00Z",
                "generated_by": "test",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(valid_data)
        assert is_valid
        assert len(errors) == 0

    def test_validate_app_graph_with_invalid_data(self) -> None:
        """Test validation with invalid app graph data."""
        invalid_data = {
            "app": {
                "name": "test_app"
                # Missing required plugin_count
            },
            "plugins": [],
            "dependency_graph": {
                "nodes": [],
                "edges": []
            },
            "metadata": {
                "generated_at": "2025-01-01T00:00:00Z",
                "generated_by": "test",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(invalid_data)
        # Skip validation if jsonschema not available
        if errors != ["jsonschema not available - validation skipped"]:
            assert not is_valid
            assert len(errors) > 0

    def test_dependency_graph_schema(self) -> None:
        """Test dependency graph schema definition."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        dep_graph_def = schema["properties"]["dependency_graph"]

        assert dep_graph_def["type"] == "object"
        assert "nodes" in dep_graph_def["required"]
        assert "edges" in dep_graph_def["required"]
        assert "cycles" in dep_graph_def["properties"]
        assert "cycles" not in dep_graph_def["required"]  # Optional

    def test_graph_node_schema(self) -> None:
        """Test graph node schema definition."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        node_def = schema["$defs"]["graph_node"]

        assert node_def["type"] == "object"
        assert "id" in node_def["required"]
        assert "type" in node_def["required"]
        assert "metadata" in node_def["properties"]
        assert "metadata" not in node_def["required"]  # Optional

    def test_graph_edge_schema(self) -> None:
        """Test graph edge schema definition."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        edge_def = schema["$defs"]["graph_edge"]

        assert edge_def["type"] == "object"
        assert "from" in edge_def["required"]
        assert "to" in edge_def["required"]
        assert "type" in edge_def["required"]
        assert "optional" in edge_def["required"]
        assert "version_constraint" in edge_def["properties"]
        assert "version_constraint" not in edge_def["required"]  # Optional

    def test_dependency_cycle_schema(self) -> None:
        """Test dependency cycle schema definition."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        cycle_def = schema["$defs"]["dependency_cycle"]

        assert cycle_def["type"] == "object"
        assert "nodes" in cycle_def["required"]
        assert "type" in cycle_def["required"]
        
        # Check nodes array
        nodes_def = cycle_def["properties"]["nodes"]
        assert nodes_def["type"] == "array"
        assert nodes_def["items"]["type"] == "string"
        
        # Check type enum
        type_def = cycle_def["properties"]["type"]
        assert type_def["enum"] == ["circular_dependency"]


class TestDependencyGraphValidation:
    """Test dependency graph validation with enhanced features."""

    def test_validate_graph_with_cycles(self) -> None:
        """Test validation of dependency graph with cycles."""
        graph_with_cycles = {
            "app": {
                "name": "test_app",
                "plugin_count": 2
            },
            "plugins": [
                {
                    "registration_name": "plugin_a",
                    "class_name": "PluginA",
                    "module": "test.module",
                    "services": [],
                    "event_listeners": [],
                    "dependencies": [],
                    "metadata": {}
                },
                {
                    "registration_name": "plugin_b",
                    "class_name": "PluginB",
                    "module": "test.module",
                    "services": [],
                    "event_listeners": [],
                    "dependencies": [],
                    "metadata": {}
                }
            ],
            "dependency_graph": {
                "nodes": [
                    {
                        "id": "plugin_a",
                        "type": "plugin",
                        "metadata": {
                            "class_name": "PluginA",
                            "module": "test.module",
                            "plugin_name": "plugin_a",
                            "plugin_version": "1.0.0"
                        }
                    },
                    {
                        "id": "plugin_b",
                        "type": "plugin",
                        "metadata": {
                            "class_name": "PluginB",
                            "module": "test.module",
                            "plugin_name": "plugin_b",
                            "plugin_version": "1.0.0"
                        }
                    }
                ],
                "edges": [
                    {
                        "from": "plugin_a",
                        "to": "plugin_b",
                        "type": "depends_on",
                        "optional": False,
                        "version_constraint": ">=1.0.0"
                    },
                    {
                        "from": "plugin_b",
                        "to": "plugin_a",
                        "type": "depends_on",
                        "optional": False,
                        "version_constraint": None
                    }
                ],
                "cycles": [
                    {
                        "nodes": ["plugin_a", "plugin_b", "plugin_a"],
                        "type": "circular_dependency"
                    }
                ]
            },
            "metadata": {
                "generated_at": "2025-01-01T00:00:00Z",
                "generated_by": "test",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(graph_with_cycles)
        # Skip validation if jsonschema not available
        if errors != ["jsonschema not available - validation skipped"]:
            assert is_valid, f"Validation failed: {errors}"

    def test_validate_graph_with_version_constraints(self) -> None:
        """Test validation of dependency graph with version constraints."""
        graph_with_versions = {
            "app": {
                "name": "test_app",
                "plugin_count": 2
            },
            "plugins": [
                {
                    "registration_name": "plugin_a",
                    "class_name": "PluginA",
                    "module": "test.module",
                    "services": [],
                    "event_listeners": [],
                    "dependencies": [],
                    "metadata": {}
                },
                {
                    "registration_name": "plugin_b",
                    "class_name": "PluginB",
                    "module": "test.module",
                    "services": [],
                    "event_listeners": [],
                    "dependencies": [],
                    "metadata": {}
                }
            ],
            "dependency_graph": {
                "nodes": [
                    {
                        "id": "plugin_a",
                        "type": "plugin"
                    },
                    {
                        "id": "plugin_b",
                        "type": "plugin"
                    }
                ],
                "edges": [
                    {
                        "from": "plugin_a",
                        "to": "plugin_b",
                        "type": "depends_on",
                        "optional": False,
                        "version_constraint": ">=1.0.0,<2.0.0"
                    }
                ]
            },
            "metadata": {
                "generated_at": "2025-01-01T00:00:00Z",
                "generated_by": "test",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(graph_with_versions)
        # Skip validation if jsonschema not available
        if errors != ["jsonschema not available - validation skipped"]:
            assert is_valid, f"Validation failed: {errors}"

    def test_validate_empty_dependency_graph(self) -> None:
        """Test validation of empty dependency graph."""
        empty_graph = {
            "app": {
                "name": "empty_app",
                "plugin_count": 0
            },
            "plugins": [],
            "dependency_graph": {
                "nodes": [],
                "edges": []
            },
            "metadata": {
                "generated_at": "2025-01-01T00:00:00Z",
                "generated_by": "test",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(empty_graph)
        # Skip validation if jsonschema not available
        if errors != ["jsonschema not available - validation skipped"]:
            assert is_valid, f"Validation failed: {errors}"

    def test_validation_error_handling(self) -> None:
        """Test validation error handling."""
        # Test with completely invalid data
        invalid_data = "not a dictionary"
        
        is_valid, errors = validate_app_graph(invalid_data)  # type: ignore
        
        # Should handle the error gracefully
        assert isinstance(is_valid, bool)
        assert isinstance(errors, list)
        assert all(isinstance(error, str) for error in errors)
