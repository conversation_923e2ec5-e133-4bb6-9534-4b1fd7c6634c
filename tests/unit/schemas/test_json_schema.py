"""
Unit tests for JSON schema definitions.

These tests verify the JSON schema for app graph discovery output
and its validation functionality.
"""

import json
from typing import Any

import pytest

from plugginger.schemas.json import APP_GRAPH_SCHEMA, validate_app_graph


class TestAppGraphSchema:
    """Test the app graph JSON schema."""

    def test_schema_structure(self) -> None:
        """Test that the schema has the correct structure."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        assert schema["$schema"] == "https://json-schema.org/draft/2020-12/schema"
        assert schema["type"] == "object"
        assert "app" in schema["required"]
        assert "plugins" in schema["required"]
        assert "dependency_graph" in schema["required"]
        assert "metadata" in schema["required"]

    def test_schema_is_valid_json(self) -> None:
        """Test that the schema itself is valid JSON."""
        # Should not raise an exception
        json_str = json.dumps(APP_GRAPH_SCHEMA)
        parsed = json.loads(json_str)
        assert parsed == APP_GRAPH_SCHEMA

    def test_validate_minimal_valid_data(self) -> None:
        """Test validation with minimal valid data."""
        valid_data = {
            "app": {
                "name": "test_app",
                "plugin_count": 0
            },
            "plugins": [],
            "dependency_graph": {
                "nodes": [],
                "edges": []
            },
            "metadata": {
                "generated_at": "2025-06-01T12:00:00Z",
                "generated_by": "plugginger inspect",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(valid_data)
        assert is_valid
        assert not errors or errors == ["jsonschema not available - validation skipped"]

    def test_validate_complete_valid_data(self) -> None:
        """Test validation with complete valid data."""
        valid_data = {
            "app": {
                "name": "demo_app",
                "plugin_count": 2,
                "max_fractal_depth": 3
            },
            "plugins": [
                {
                    "registration_name": "logger",
                    "class_name": "LoggerPlugin",
                    "module": "test.plugins",
                    "services": [
                        {
                            "name": "log",
                            "method_name": "log_message",
                            "signature": {
                                "parameters": [
                                    {
                                        "name": "level",
                                        "type": "str",
                                        "default": None,
                                        "kind": "POSITIONAL_OR_KEYWORD"
                                    }
                                ],
                                "return_type": "None"
                            },
                            "metadata": {
                                "name": "log",
                                "description": "Log a message"
                            }
                        }
                    ],
                    "event_listeners": [
                        {
                            "method_name": "on_startup",
                            "event_pattern": "app.startup",
                            "patterns": ["app.startup"],
                            "signature": {
                                "parameters": [
                                    {
                                        "name": "event_data",
                                        "type": "dict[str, Any]",
                                        "default": None,
                                        "kind": "POSITIONAL_OR_KEYWORD"
                                    }
                                ],
                                "return_type": "None"
                            },
                            "metadata": {
                                "patterns": ["app.startup"],
                                "priority": 0
                            }
                        }
                    ],
                    "dependencies": [],
                    "metadata": {
                        "name": "logger",
                        "version": "1.0.0"
                    }
                },
                {
                    "registration_name": "cache",
                    "class_name": "CachePlugin",
                    "module": "test.plugins",
                    "services": [],
                    "event_listeners": [],
                    "dependencies": [
                        {
                            "name": "logger",
                            "optional": False,
                            "version": None
                        }
                    ],
                    "metadata": {
                        "name": "cache",
                        "version": "2.0.0"
                    }
                }
            ],
            "dependency_graph": {
                "nodes": [
                    {
                        "id": "logger",
                        "type": "plugin"
                    },
                    {
                        "id": "cache",
                        "type": "plugin"
                    }
                ],
                "edges": [
                    {
                        "from": "cache",
                        "to": "logger",
                        "type": "depends_on",
                        "optional": False
                    }
                ]
            },
            "metadata": {
                "generated_at": "2025-06-01T12:00:00Z",
                "generated_by": "plugginger inspect",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(valid_data)
        assert is_valid
        assert not errors or errors == ["jsonschema not available - validation skipped"]

    def test_validate_missing_required_fields(self) -> None:
        """Test validation with missing required fields."""
        invalid_data = {
            "app": {
                "name": "test_app"
                # Missing plugin_count
            },
            "plugins": [],
            "dependency_graph": {
                "nodes": [],
                "edges": []
            },
            "metadata": {
                "generated_at": "2025-06-01T12:00:00Z",
                "generated_by": "plugginger inspect",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(invalid_data)
        # If jsonschema is available, should be invalid
        # If not available, validation is skipped
        if errors != ["jsonschema not available - validation skipped"]:
            assert not is_valid
            assert len(errors) > 0

    def test_validate_invalid_types(self) -> None:
        """Test validation with invalid types."""
        invalid_data = {
            "app": {
                "name": "test_app",
                "plugin_count": "not_a_number"  # Should be integer
            },
            "plugins": [],
            "dependency_graph": {
                "nodes": [],
                "edges": []
            },
            "metadata": {
                "generated_at": "2025-06-01T12:00:00Z",
                "generated_by": "plugginger inspect",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(invalid_data)
        # If jsonschema is available, should be invalid
        # If not available, validation is skipped
        if errors != ["jsonschema not available - validation skipped"]:
            assert not is_valid
            assert len(errors) > 0

    def test_validate_additional_properties(self) -> None:
        """Test validation with additional properties (should be rejected)."""
        invalid_data = {
            "app": {
                "name": "test_app",
                "plugin_count": 0,
                "extra_field": "not_allowed"  # Additional property
            },
            "plugins": [],
            "dependency_graph": {
                "nodes": [],
                "edges": []
            },
            "metadata": {
                "generated_at": "2025-06-01T12:00:00Z",
                "generated_by": "plugginger inspect",
                "schema_version": "1.0.0"
            }
        }

        is_valid, errors = validate_app_graph(invalid_data)
        # If jsonschema is available, should be invalid
        # If not available, validation is skipped
        if errors != ["jsonschema not available - validation skipped"]:
            assert not is_valid
            assert len(errors) > 0

    def test_schema_definitions(self) -> None:
        """Test that all required schema definitions are present."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        defs = schema["$defs"]

        required_defs = [
            "plugin",
            "service",
            "event_listener",
            "dependency",
            "method_signature",
            "parameter",
            "graph_node",
            "graph_edge"
        ]

        for def_name in required_defs:
            assert def_name in defs, f"Missing schema definition: {def_name}"

    def test_plugin_schema_definition(self) -> None:
        """Test the plugin schema definition."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        plugin_def = schema["$defs"]["plugin"]

        assert plugin_def["type"] == "object"
        assert "registration_name" in plugin_def["required"]
        assert "class_name" in plugin_def["required"]
        assert "module" in plugin_def["required"]
        assert "services" in plugin_def["required"]
        assert "event_listeners" in plugin_def["required"]
        assert "dependencies" in plugin_def["required"]
        assert "metadata" in plugin_def["required"]

    def test_service_schema_definition(self) -> None:
        """Test the service schema definition."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        service_def = schema["$defs"]["service"]

        assert service_def["type"] == "object"
        assert "name" in service_def["required"]
        assert "method_name" in service_def["required"]
        assert "signature" in service_def["required"]
        assert "metadata" in service_def["required"]

    def test_dependency_graph_schema_definition(self) -> None:
        """Test the dependency graph schema definition."""
        schema: dict[str, Any] = APP_GRAPH_SCHEMA
        graph_node_def = schema["$defs"]["graph_node"]
        graph_edge_def = schema["$defs"]["graph_edge"]

        # Node definition
        assert graph_node_def["type"] == "object"
        assert "id" in graph_node_def["required"]
        assert "type" in graph_node_def["required"]

        # Edge definition
        assert graph_edge_def["type"] == "object"
        assert "from" in graph_edge_def["required"]
        assert "to" in graph_edge_def["required"]
        assert "type" in graph_edge_def["required"]
        assert "optional" in graph_edge_def["required"]


class TestSchemaValidationFunction:
    """Test the validate_app_graph function."""

    def test_validation_with_empty_dict(self) -> None:
        """Test validation with empty dictionary."""
        is_valid, errors = validate_app_graph({})
        
        # If jsonschema is available, should be invalid
        # If not available, validation is skipped
        if errors != ["jsonschema not available - validation skipped"]:
            assert not is_valid
            assert len(errors) > 0

    def test_validation_with_none(self) -> None:
        """Test validation with None input."""
        # Since we catch all exceptions, None will be handled gracefully
        is_valid, errors = validate_app_graph(None)  # type: ignore

        # Should be invalid (unless jsonschema is not available)
        if errors != ["jsonschema not available - validation skipped"]:
            assert not is_valid
            assert len(errors) > 0

    def test_validation_return_types(self) -> None:
        """Test that validation returns correct types."""
        valid_data = {
            "app": {"name": "test", "plugin_count": 0},
            "plugins": [],
            "dependency_graph": {"nodes": [], "edges": []},
            "metadata": {
                "generated_at": "2025-06-01T12:00:00Z",
                "generated_by": "test",
                "schema_version": "1.0.0"
            }
        }
        
        is_valid, errors = validate_app_graph(valid_data)
        
        assert isinstance(is_valid, bool)
        assert isinstance(errors, list)
        assert all(isinstance(error, str) for error in errors)
