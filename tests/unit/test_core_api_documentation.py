"""
Tests for Core API documentation completeness and accuracy.

These tests verify that the Core API documentation exists and contains
the expected content for stable candidate APIs.
"""

import os
import pytest
from pathlib import Path


class TestCoreAPIDocumentation:
    """Test the Core API documentation structure and content."""
    
    def test_core_api_directory_exists(self) -> None:
        """Test that the core API documentation directory exists."""
        docs_path = Path("docs/core-api")
        assert docs_path.exists(), "docs/core-api directory should exist"
        assert docs_path.is_dir(), "docs/core-api should be a directory"
    
    def test_readme_exists(self) -> None:
        """Test that the main README exists."""
        readme_path = Path("docs/core-api/README.md")
        assert readme_path.exists(), "docs/core-api/README.md should exist"
        
        content = readme_path.read_text()
        assert "Plugginger Core API Documentation" in content
        assert "Stable Candidates" in content
        assert "v0.9.0-alpha" in content
    
    def test_plugin_api_documentation_exists(self) -> None:
        """Test that plugin API documentation exists and is complete."""
        plugin_doc_path = Path("docs/core-api/plugin.md")
        assert plugin_doc_path.exists(), "docs/core-api/plugin.md should exist"
        
        content = plugin_doc_path.read_text()
        
        # Check for key sections
        assert "Plugin API - Stable Core API" in content
        assert "PluginBase" in content
        assert "@plugin" in content
        assert "get_plugin_metadata" in content
        assert "is_plugin_class" in content
        
        # Check for examples
        assert "```python" in content
        assert "class" in content and "PluginBase" in content
        
        # Check for best practices
        assert "Best Practices" in content
        assert "Error Handling" in content
        assert "API Stability" in content
    
    def test_service_api_documentation_exists(self) -> None:
        """Test that service API documentation exists and is complete."""
        service_doc_path = Path("docs/core-api/service.md")
        assert service_doc_path.exists(), "docs/core-api/service.md should exist"
        
        content = service_doc_path.read_text()
        
        # Check for key sections
        assert "Service API - Stable Core API" in content
        assert "@service" in content
        assert "get_service_metadata" in content
        assert "is_service_method" in content
        assert "extract_service_methods" in content
        
        # Check for async requirements
        assert "async def" in content
        assert "timeout_seconds" in content
        
        # Check for examples
        assert "```python" in content
        assert "async def" in content
        
        # Check for best practices
        assert "Best Practices" in content
        assert "Type Hints" in content
        assert "Error Handling" in content
    
    def test_events_api_documentation_exists(self) -> None:
        """Test that events API documentation exists and is complete."""
        events_doc_path = Path("docs/core-api/events.md")
        assert events_doc_path.exists(), "docs/core-api/events.md should exist"
        
        content = events_doc_path.read_text()
        
        # Check for key sections
        assert "Events API - Stable Core API" in content
        assert "@on_event" in content
        assert "get_event_listener_metadata" in content
        assert "is_event_listener_method" in content
        assert "extract_event_listeners" in content
        
        # Check for event patterns
        assert "Event Patterns" in content
        assert "Wildcard Patterns" in content
        assert "Multiple Patterns" in content
        
        # Check for examples
        assert "```python" in content
        assert "async def" in content
        assert "event_data" in content
        
        # Check for priority and execution
        assert "Priority" in content
        assert "priority=" in content
    
    def test_documentation_cross_references(self) -> None:
        """Test that documentation files properly cross-reference each other."""
        readme_path = Path("docs/core-api/README.md")
        readme_content = readme_path.read_text()
        
        # Check that README links to individual API docs
        assert "[Plugin API](plugin.md)" in readme_content
        assert "[Service API](service.md)" in readme_content
        assert "[Events API](events.md)" in readme_content
        
        # Check that README mentions all key decorators
        assert "@plugin" in readme_content
        assert "@service" in readme_content
        assert "@on_event" in readme_content
    
    def test_documentation_contains_examples(self) -> None:
        """Test that all documentation files contain practical examples."""
        doc_files = [
            "docs/core-api/README.md",
            "docs/core-api/plugin.md", 
            "docs/core-api/service.md",
            "docs/core-api/events.md"
        ]
        
        for doc_file in doc_files:
            doc_path = Path(doc_file)
            assert doc_path.exists(), f"{doc_file} should exist"
            
            content = doc_path.read_text()
            
            # Should contain code examples
            assert "```python" in content, f"{doc_file} should contain Python code examples"
            
            # Should contain practical examples
            assert "async def" in content, f"{doc_file} should contain async function examples"
    
    def test_documentation_mentions_stability(self) -> None:
        """Test that all API docs mention stability status."""
        api_docs = [
            "docs/core-api/plugin.md",
            "docs/core-api/service.md", 
            "docs/core-api/events.md"
        ]
        
        for doc_file in api_docs:
            doc_path = Path(doc_file)
            content = doc_path.read_text()
            
            # Should mention stable candidate status
            assert "Stable Candidate" in content, f"{doc_file} should mention stability status"
            assert "v0.9.0-alpha" in content, f"{doc_file} should mention version"
            assert "API Stability" in content, f"{doc_file} should have stability section"
    
    def test_documentation_has_best_practices(self) -> None:
        """Test that all API docs include best practices sections."""
        api_docs = [
            "docs/core-api/plugin.md",
            "docs/core-api/service.md",
            "docs/core-api/events.md"
        ]
        
        for doc_file in api_docs:
            doc_path = Path(doc_file)
            content = doc_path.read_text()
            
            # Should have best practices
            assert "Best Practices" in content, f"{doc_file} should have best practices section"
            
            # Should have error handling guidance
            assert "Error Handling" in content, f"{doc_file} should have error handling section"
    
    def test_documentation_formatting_consistency(self) -> None:
        """Test that documentation follows consistent formatting."""
        api_docs = [
            "docs/core-api/plugin.md",
            "docs/core-api/service.md",
            "docs/core-api/events.md"
        ]
        
        for doc_file in api_docs:
            doc_path = Path(doc_file)
            content = doc_path.read_text()
            
            # Should start with proper header
            lines = content.split('\n')
            assert lines[0].startswith('#'), f"{doc_file} should start with h1 header"
            assert "Stable Core API" in lines[0], f"{doc_file} should mention stable API in title"
            
            # Should have status and module info
            assert "**Status**:" in content, f"{doc_file} should have status info"
            assert "**Module**:" in content, f"{doc_file} should have module info"
            
            # Should have overview section
            assert "## Overview" in content, f"{doc_file} should have overview section"
    
    def test_quick_start_guide_completeness(self) -> None:
        """Test that the quick start guide in README is complete."""
        readme_path = Path("docs/core-api/README.md")
        content = readme_path.read_text()
        
        # Should have quick start section
        assert "Quick Start Guide" in content
        
        # Should show complete plugin example
        assert "Create a Basic Plugin" in content
        assert "Build and Run Application" in content
        
        # Should show all three decorators in examples
        assert "@plugin(" in content
        assert "@service(" in content  
        assert "@on_event(" in content
        
        # Should show app building and usage
        assert "PluggingerAppBuilder" in content
        assert "call_service" in content
        assert "emit_event" in content
    
    def test_migration_guidance_exists(self) -> None:
        """Test that migration guidance from experimental APIs exists."""
        readme_path = Path("docs/core-api/README.md")
        content = readme_path.read_text()
        
        # Should have migration section
        assert "Migration from Experimental APIs" in content
        
        # Should show import changes
        assert "Replace imports" in content
        assert "plugginger.experimental" in content
        assert "plugginger.api" in content
        
        # Should mention key changes
        assert "PluginBase" in content
        assert "Depends()" in content
