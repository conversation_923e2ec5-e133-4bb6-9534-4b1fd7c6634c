"""
Tests for manifest examples.

These tests verify that all example manifests are valid and
conform to the schema definitions.
"""

from pathlib import Path

import pytest

from plugginger.schemas import AppManifest, PluginManifest, manifest_from_yaml


class TestManifestExamples:
    """Test all manifest examples for validity."""

    @pytest.fixture
    def examples_dir(self) -> Path:
        """Get the examples directory path."""
        # Get the project root (3 levels up from this test file)
        project_root = Path(__file__).parent.parent.parent.parent
        return project_root / "examples" / "manifests"

    def test_examples_directory_exists(self, examples_dir: Path) -> None:
        """Test that examples directory exists."""
        assert examples_dir.exists(), f"Examples directory not found: {examples_dir}"
        assert examples_dir.is_dir(), f"Examples path is not a directory: {examples_dir}"

    def test_plugin_type_examples(self, examples_dir: Path) -> None:
        """Test all plugin type examples validate correctly."""
        plugin_types_dir = examples_dir / "plugin-types"
        assert plugin_types_dir.exists(), "plugin-types directory not found"

        yaml_files = list(plugin_types_dir.glob("*.yaml"))
        assert len(yaml_files) > 0, "No YAML files found in plugin-types directory"

        for yaml_file in yaml_files:
            with open(yaml_file, encoding='utf-8') as f:
                yaml_content = f.read()

            # Should validate as PluginManifest without errors
            try:
                manifest = manifest_from_yaml(yaml_content, PluginManifest)
                assert manifest is not None
                assert isinstance(manifest, PluginManifest)
                assert manifest.manifest_version == "1.0.0"
                assert manifest.metadata.name
                assert manifest.metadata.version
                assert manifest.runtime.plugginger_version
            except Exception as e:
                pytest.fail(f"Failed to validate {yaml_file.name}: {e}")

    def test_application_examples(self, examples_dir: Path) -> None:
        """Test all application examples validate correctly."""
        applications_dir = examples_dir / "applications"
        assert applications_dir.exists(), "applications directory not found"

        yaml_files = list(applications_dir.glob("*.yaml"))
        assert len(yaml_files) > 0, "No YAML files found in applications directory"

        for yaml_file in yaml_files:
            with open(yaml_file, encoding='utf-8') as f:
                yaml_content = f.read()

            # Should validate as AppManifest without errors
            try:
                manifest = manifest_from_yaml(yaml_content, AppManifest)
                assert manifest is not None
                assert isinstance(manifest, AppManifest)
                assert manifest.manifest_version == "1.0.0"
                assert manifest.app_name
                assert manifest.app_version
                assert len(manifest.plugins) > 0
            except Exception as e:
                pytest.fail(f"Failed to validate {yaml_file.name}: {e}")

    def test_advanced_examples(self, examples_dir: Path) -> None:
        """Test all advanced examples validate correctly."""
        advanced_dir = examples_dir / "advanced"
        assert advanced_dir.exists(), "advanced directory not found"

        yaml_files = list(advanced_dir.glob("*.yaml"))
        assert len(yaml_files) > 0, "No YAML files found in advanced directory"

        for yaml_file in yaml_files:
            with open(yaml_file, encoding='utf-8') as f:
                yaml_content = f.read()

            # Should validate as PluginManifest without errors
            try:
                manifest = manifest_from_yaml(yaml_content, PluginManifest)
                assert manifest is not None
                assert isinstance(manifest, PluginManifest)
                assert manifest.manifest_version == "1.0.0"
                assert manifest.metadata.name
                assert manifest.metadata.version
                assert manifest.runtime.plugginger_version
            except Exception as e:
                pytest.fail(f"Failed to validate {yaml_file.name}: {e}")

    def test_specific_examples_content(self, examples_dir: Path) -> None:
        """Test specific content of key examples."""

        # Test service-only example
        service_only_file = examples_dir / "plugin-types" / "service-only.yaml"
        with open(service_only_file, encoding='utf-8') as f:
            manifest = manifest_from_yaml(f.read(), PluginManifest)
        assert isinstance(manifest, PluginManifest)

        assert manifest.metadata.name == "calculator_service"
        assert len(manifest.services) > 0
        assert len(manifest.event_listeners) == 0
        assert len(manifest.dependencies) == 0
        assert manifest.config_schema is None

        # Test event-listener example
        event_listener_file = examples_dir / "plugin-types" / "event-listener.yaml"
        with open(event_listener_file, encoding='utf-8') as f:
            manifest = manifest_from_yaml(f.read(), PluginManifest)
        assert isinstance(manifest, PluginManifest)

        assert manifest.metadata.name == "audit_logger"
        assert len(manifest.services) == 0
        assert len(manifest.event_listeners) > 0
        assert len(manifest.dependencies) == 0

        # Test mixed plugin example
        mixed_file = examples_dir / "plugin-types" / "mixed-plugin.yaml"
        with open(mixed_file, encoding='utf-8') as f:
            manifest = manifest_from_yaml(f.read(), PluginManifest)
        assert isinstance(manifest, PluginManifest)

        assert manifest.metadata.name == "user_manager"
        assert len(manifest.services) > 0
        assert len(manifest.event_listeners) > 0

        # Test with-dependencies example
        deps_file = examples_dir / "plugin-types" / "with-dependencies.yaml"
        with open(deps_file, encoding='utf-8') as f:
            manifest = manifest_from_yaml(f.read(), PluginManifest)
        assert isinstance(manifest, PluginManifest)

        assert manifest.metadata.name == "notification_service"
        assert len(manifest.dependencies) > 0
        # Should have both required and optional dependencies
        required_deps = [dep for dep in manifest.dependencies if not dep.optional]
        optional_deps = [dep for dep in manifest.dependencies if dep.optional]
        assert len(required_deps) > 0
        assert len(optional_deps) > 0

        # Test with-config example
        config_file = examples_dir / "plugin-types" / "with-config.yaml"
        with open(config_file, encoding='utf-8') as f:
            manifest = manifest_from_yaml(f.read(), PluginManifest)
        assert isinstance(manifest, PluginManifest)

        assert manifest.metadata.name == "database_connector"
        assert manifest.config_schema is not None
        assert manifest.config_schema["type"] == "object"
        assert "properties" in manifest.config_schema

    def test_application_manifest_content(self, examples_dir: Path) -> None:
        """Test specific content of application manifests."""

        # Test simple app
        simple_app_file = examples_dir / "applications" / "simple-app.yaml"
        with open(simple_app_file, encoding='utf-8') as f:
            manifest = manifest_from_yaml(f.read(), AppManifest)
        assert isinstance(manifest, AppManifest)

        assert manifest.app_name == "simple_calculator_app"
        assert "calculator_service" in manifest.plugins
        assert "audit_logger" in manifest.plugins
        assert "calculator_service" in manifest.plugin_configs
        assert "global_config" in manifest.model_dump()

        # Test AI chat app
        ai_chat_file = examples_dir / "applications" / "ai-chat-app.yaml"
        with open(ai_chat_file, encoding='utf-8') as f:
            manifest = manifest_from_yaml(f.read(), AppManifest)
        assert isinstance(manifest, AppManifest)

        assert manifest.app_name == "ai_chat_reference"
        assert "chat_ai" in manifest.plugins
        assert "memory_store" in manifest.plugins
        assert "web_api" in manifest.plugins

    def test_execution_modes(self, examples_dir: Path) -> None:
        """Test that different execution modes are represented."""
        all_yaml_files: list[Path] = []

        # Collect all YAML files
        for subdir in ["plugin-types", "advanced"]:
            subdir_path = examples_dir / subdir
            if subdir_path.exists():
                all_yaml_files.extend(subdir_path.glob("*.yaml"))

        execution_modes = set()

        for yaml_file in all_yaml_files:
            with open(yaml_file, encoding='utf-8') as f:
                manifest = manifest_from_yaml(f.read(), PluginManifest)
            assert isinstance(manifest, PluginManifest)
            execution_modes.add(manifest.runtime.execution_mode)

        # Should have examples of different execution modes
        assert "thread" in execution_modes
        # Advanced examples should include other modes
        if len(all_yaml_files) >= 5:  # If we have enough examples
            assert len(execution_modes) > 1

    def test_readme_files_exist(self, examples_dir: Path) -> None:
        """Test that documentation files exist."""
        main_readme = examples_dir / "README.md"
        assert main_readme.exists(), "Main README.md not found"

        guide = examples_dir / "GUIDE.md"
        assert guide.exists(), "GUIDE.md not found"

        # Check that README files are not empty
        with open(main_readme, encoding='utf-8') as f:
            readme_content = f.read()
        assert len(readme_content) > 100, "README.md is too short"

        with open(guide, encoding='utf-8') as f:
            guide_content = f.read()
        assert len(guide_content) > 500, "GUIDE.md is too short"


class TestManifestExampleIntegration:
    """Integration tests for manifest examples."""

    def test_examples_can_be_imported(self) -> None:
        """Test that manifest examples can be imported and used."""
        from plugginger.schemas import AppManifest, PluginManifest, manifest_from_yaml

        # This should not raise any import errors
        assert PluginManifest is not None
        assert AppManifest is not None
        assert manifest_from_yaml is not None

    def test_yaml_parsing_robustness(self, tmp_path: Path) -> None:
        """Test YAML parsing with various edge cases."""
        from plugginger.schemas import PluginManifest, manifest_from_yaml

        # Test with minimal valid manifest
        minimal_manifest = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T15:30:00Z"
generated_by: "test"
"""

        manifest = manifest_from_yaml(minimal_manifest, PluginManifest)
        assert isinstance(manifest, PluginManifest)
        assert manifest.metadata.name == "test_plugin"
        assert manifest.runtime.execution_mode == "thread"  # Default value
