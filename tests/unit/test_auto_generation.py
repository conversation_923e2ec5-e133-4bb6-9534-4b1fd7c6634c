"""
Tests for automatic manifest generation functionality.

These tests verify that the PluggingerAppBuilder and PluggingerAppInstance
can automatically generate and export plugin manifests.
"""

import tempfile
from pathlib import Path
from typing import List

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


# Test plugin classes
@plugin(name="test_plugin_auto", version="1.0.0")
class TestPluginAuto(PluginBase):
    """Test plugin for auto-generation."""
    needs: List[Depends] = [Depends("logger")]
    
    @service(name="auto_service", description="Auto-generated service")
    async def auto_service_method(self, value: str) -> str:
        """Auto service method."""
        return f"auto: {value}"
    
    @on_event("auto.event", priority=5)
    async def on_auto_event(self, event_data: dict) -> None:
        """Handle auto event."""
        pass


@plugin(name="simple_plugin_auto", version="2.0.0")
class SimplePluginAuto(PluginBase):
    """Simple plugin for auto-generation."""
    
    @service()
    async def simple_method(self) -> bool:
        """Simple method."""
        return True


class TestBuilderAutoGeneration:
    """Test automatic manifest generation in PluggingerAppBuilder."""
    
    def test_builder_export_manifests_basic(self) -> None:
        """Test basic manifest export from builder."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPluginAuto)
        builder.include(SimplePluginAuto)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            exported_files = builder.export_manifests(
                output_dir=temp_dir,
                author="Test Author",
                license="MIT",
            )
            
            # Check that files were created
            assert len(exported_files) == 3  # 2 plugin manifests + 1 app manifest
            
            # Check individual plugin manifests
            test_plugin_manifest_path = Path(temp_dir) / "test_plugin_auto_manifest.yaml"
            simple_plugin_manifest_path = Path(temp_dir) / "simple_plugin_auto_manifest.yaml"
            app_manifest_path = Path(temp_dir) / "test_app_app_manifest.yaml"
            
            assert test_plugin_manifest_path.exists()
            assert simple_plugin_manifest_path.exists()
            assert app_manifest_path.exists()
            
            # Check file contents
            test_plugin_content = test_plugin_manifest_path.read_text()
            assert "name: test_plugin_auto" in test_plugin_content
            assert "version: 1.0.0" in test_plugin_content
            assert "author: Test Author" in test_plugin_content
            assert "license: MIT" in test_plugin_content
            assert "auto_service" in test_plugin_content
            
            simple_plugin_content = simple_plugin_manifest_path.read_text()
            assert "name: simple_plugin_auto" in simple_plugin_content
            assert "version: 2.0.0" in simple_plugin_content
            
            app_content = app_manifest_path.read_text()
            assert "app_name: test_app" in app_content
            assert "test_plugin_auto" in app_content
            assert "simple_plugin_auto" in app_content
    
    def test_builder_export_manifests_individual_only(self) -> None:
        """Test exporting only individual plugin manifests."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPluginAuto)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            exported_files = builder.export_manifests(
                output_dir=temp_dir,
                include_app_manifest=False,
            )
            
            # Check that only plugin manifest was created
            assert len(exported_files) == 1
            
            test_plugin_manifest_path = Path(temp_dir) / "test_plugin_auto_manifest.yaml"
            app_manifest_path = Path(temp_dir) / "test_app_app_manifest.yaml"
            
            assert test_plugin_manifest_path.exists()
            assert not app_manifest_path.exists()
    
    def test_builder_export_manifests_app_only(self) -> None:
        """Test exporting only application manifest."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPluginAuto)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            exported_files = builder.export_manifests(
                output_dir=temp_dir,
                include_individual_manifests=False,
            )
            
            # Check that only app manifest was created
            assert len(exported_files) == 1
            
            test_plugin_manifest_path = Path(temp_dir) / "test_plugin_auto_manifest.yaml"
            app_manifest_path = Path(temp_dir) / "test_app_app_manifest.yaml"
            
            assert not test_plugin_manifest_path.exists()
            assert app_manifest_path.exists()
    
    def test_builder_export_manifests_no_plugins(self) -> None:
        """Test that export fails when no plugins are registered."""
        builder = PluggingerAppBuilder("test_app")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            with pytest.raises(ValueError, match="No plugins registered"):
                builder.export_manifests(output_dir=temp_dir)
    
    def test_builder_export_manifests_invalid_output_dir(self) -> None:
        """Test that export fails with invalid output directory."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPluginAuto)
        
        with pytest.raises(ValueError, match="output_dir must be a non-empty string"):
            builder.export_manifests(output_dir="")
        
        with pytest.raises(ValueError, match="output_dir must be a non-empty string"):
            builder.export_manifests(output_dir=None)  # type: ignore
    
    def test_builder_export_manifests_custom_metadata(self) -> None:
        """Test export with custom metadata."""
        builder = PluggingerAppBuilder("custom_app")
        builder.include(TestPluginAuto)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            exported_files = builder.export_manifests(
                output_dir=temp_dir,
                app_version="2.1.0",
                app_description="Custom application",
                author="Custom Author",
                license="Apache-2.0",
                keywords=["test", "custom"],
            )
            
            # Check app manifest content
            app_manifest_path = Path(temp_dir) / "custom_app_app_manifest.yaml"
            app_content = app_manifest_path.read_text()
            
            assert "app_version: 2.1.0" in app_content
            assert "description: Custom application" in app_content
            
            # Check plugin manifest content
            plugin_manifest_path = Path(temp_dir) / "test_plugin_auto_manifest.yaml"
            plugin_content = plugin_manifest_path.read_text()
            
            assert "author: Custom Author" in plugin_content
            assert "license: Apache-2.0" in plugin_content
            assert "keywords:" in plugin_content
            assert "- test" in plugin_content
            assert "- custom" in plugin_content


class TestAppInstanceAutoGeneration:
    """Test automatic manifest generation in PluggingerAppInstance."""
    
    async def test_app_instance_export_manifests(self) -> None:
        """Test manifest export from app instance."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPluginAuto)
        app = builder.build()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            exported_files = app.export_manifests(
                output_dir=temp_dir,
                author="Instance Author",
            )
            
            # Check that files were created
            assert len(exported_files) == 2  # 1 plugin manifest + 1 app manifest
            
            # Check file existence
            plugin_manifest_path = Path(temp_dir) / "test_plugin_auto_manifest.yaml"
            app_manifest_path = Path(temp_dir) / "test_app_app_manifest.yaml"
            
            assert plugin_manifest_path.exists()
            assert app_manifest_path.exists()
            
            # Check content
            plugin_content = plugin_manifest_path.read_text()
            assert "author: Instance Author" in plugin_content
    
    async def test_app_instance_export_manifests_no_plugins(self) -> None:
        """Test that app instance export fails when no plugins are registered."""
        builder = PluggingerAppBuilder("empty_app")
        app = builder.build()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            with pytest.raises(ValueError, match="No plugins registered"):
                app.export_manifests(output_dir=temp_dir)
    
    async def test_app_instance_export_manifests_return_value(self) -> None:
        """Test that export returns correct file mapping."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPluginAuto)
        app = builder.build()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            exported_files = app.export_manifests(output_dir=temp_dir)
            
            # Check return value structure
            assert isinstance(exported_files, dict)
            assert len(exported_files) == 2
            
            # Check that all returned paths exist
            for file_path, yaml_content in exported_files.items():
                assert Path(file_path).exists()
                assert isinstance(yaml_content, str)
                assert len(yaml_content) > 0
                assert "manifest_version: 1.0.0" in yaml_content


class TestAutoGenerationIntegration:
    """Integration tests for auto-generation functionality."""
    
    async def test_full_workflow(self) -> None:
        """Test complete workflow from builder to app instance."""
        # Build app with plugins
        builder = PluggingerAppBuilder("integration_app")
        builder.include(TestPluginAuto)
        builder.include(SimplePluginAuto)
        
        # Export from builder
        with tempfile.TemporaryDirectory() as temp_dir:
            builder_exports = builder.export_manifests(
                output_dir=f"{temp_dir}/builder",
                author="Builder Author",
            )
            
            # Build app instance
            app = builder.build()
            
            # Export from app instance
            app_exports = app.export_manifests(
                output_dir=f"{temp_dir}/app",
                author="App Author",
            )
            
            # Both should have same number of files
            assert len(builder_exports) == len(app_exports)
            
            # Content should be similar (different authors)
            builder_plugin_path = Path(f"{temp_dir}/builder/test_plugin_auto_manifest.yaml")
            app_plugin_path = Path(f"{temp_dir}/app/test_plugin_auto_manifest.yaml")
            
            builder_content = builder_plugin_path.read_text()
            app_content = app_plugin_path.read_text()
            
            # Same plugin name and version
            assert "name: test_plugin_auto" in builder_content
            assert "name: test_plugin_auto" in app_content
            assert "version: 1.0.0" in builder_content
            assert "version: 1.0.0" in app_content
            
            # Different authors
            assert "author: Builder Author" in builder_content
            assert "author: App Author" in app_content
