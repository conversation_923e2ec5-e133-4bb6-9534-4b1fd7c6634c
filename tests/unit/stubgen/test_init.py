from pathlib import Path
import tempfile
from typing import Any

from plugginger.api.plugin import PluginBase
from plugginger.core.constants import PLUGIN_METADATA_KEY
from plugginger.stubgen import generate_stubs_for_plugins
import pytest


class MockPlugin(PluginBase):
    """Mock plugin class for testing."""
    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    @classmethod
    def get_service_method_names(cls) -> list[str]:
        return []


setattr(MockPlugin, PLUGIN_METADATA_KEY, True)
MockPlugin._plugginger_plugin_name = "mock_plugin"


def test_generate_stubs_for_plugins() -> None:
    print("test_generate_stubs_for_plugins called")
    print("test_generate_stubs_for_plugins called")
    with tempfile.TemporaryDirectory() as tmpdir:
        output_directory = Path(tmpdir)
        plugin_classes_map: dict[str, type[PluginBase]] = {"mock_plugin": MockPlugin}
        count_written = generate_stubs_for_plugins(plugin_classes_map, output_directory)
        assert count_written == 1
        stub_file_path = output_directory / "mock_plugin_proxy.pyi"
        assert stub_file_path.exists()
        stub_content = stub_file_path.read_text()
        assert "class MockPluginProxy:" in stub_content
        assert "\"\"\"Auto-generated proxy stub for plugin 'mock_plugin'.\"\"\"" in stub_content
        assert "pass  # No services found for this plugin." in stub_content


def test_generate_stubs_for_plugins_with_invalid_plugin() -> None:
    print("test_generate_stubs_for_plugins_with_invalid_plugin called")
    print("test_generate_stubs_for_plugins_with_invalid_plugin called")
    with tempfile.TemporaryDirectory() as tmpdir:
        output_directory = Path(tmpdir)
        class InvalidPlugin:
            pass
        plugin_classes_map: dict[str, Any] = {"invalid_plugin": InvalidPlugin}
        count_written = generate_stubs_for_plugins(plugin_classes_map, output_directory)
        assert count_written == 0
