# tests/unit/test_api_app.py

"""
Unit tests for plugginger.api.app module.

Tests the PluggingerAppInstance class and its lifecycle management,
service delegation, event handling, and background task management.
"""

from __future__ import annotations

import asyncio
import logging
from unittest.mock import AsyncMock, Mock, patch

import pytest

from plugginger.api.app import PluggingerAppInstance
from plugginger.config.models import GlobalAppConfig
from plugginger.core.config import DEFAULT_APP_NAME
from plugginger.core.exceptions import BackgroundTaskError


class MockRuntimeFacade:
    """Mock runtime facade for testing."""

    def __init__(self) -> None:
        self.setup_all_plugins = AsyncMock()
        self.teardown_all_plugins = AsyncMock()
        self.shutdown = AsyncMock()
        self.get_plugins_were_setup_flag = Mock(return_value=True)
        self.get_plugin_by_id = Mock(return_value=None)
        self.call_service = AsyncMock()
        self.has_service = Mock(return_value=False)
        self.list_services = Mock(return_value=[])
        self.emit_event = AsyncMock()
        self.list_event_patterns = Mock(return_value=[])
        self.get_executor = Mock()
        self.register_executor = Mock()


class TestPluggingerAppInstanceInit:
    """Test PluggingerAppInstance initialization."""

    def test_init_with_defaults(self) -> None:
        """Test initialization with default parameters."""
        app = PluggingerAppInstance()

        assert app.app_name == DEFAULT_APP_NAME
        assert app._runtime_facade is None
        assert isinstance(app.global_config, GlobalAppConfig)
        assert app.global_config.app_name == DEFAULT_APP_NAME
        assert app._parent_app_plugin_context is None
        assert app._current_app_depth == 0
        assert app._current_build_depth_for_sub_apps == 0
        assert app._max_build_depth_for_sub_apps == app.global_config.max_fractal_depth

    def test_init_with_custom_app_name(self) -> None:
        """Test initialization with custom app name."""
        app_name = "test_app"
        app = PluggingerAppInstance(app_name=app_name)

        assert app.app_name == app_name
        assert app.global_config.app_name == app_name

    def test_init_with_runtime_facade(self) -> None:
        """Test initialization with runtime facade."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        assert app._runtime_facade is runtime_facade

    def test_init_with_global_config(self) -> None:
        """Test initialization with custom global config."""
        config = GlobalAppConfig(app_name="custom_app", max_fractal_depth=5)
        app = PluggingerAppInstance(global_config=config)

        assert app.global_config is config
        assert app.app_name == DEFAULT_APP_NAME  # app_name parameter takes precedence
        assert app._max_build_depth_for_sub_apps == 5

    def test_init_with_fractal_depth(self) -> None:
        """Test initialization with fractal depth."""
        app = PluggingerAppInstance(_builder_fractal_depth=3)

        assert app._current_app_depth == 3
        assert app._current_build_depth_for_sub_apps == 3

    def test_logger_setup(self) -> None:
        """Test that logger is properly configured."""
        app_name = "test_logger_app"
        app = PluggingerAppInstance(app_name=app_name)

        assert isinstance(app.logger, logging.Logger)
        assert app.logger.name == f"plugginger.app.{app_name}"
        # Logger level is an integer, not a string
        expected_level = getattr(logging, app.global_config.log_level.value)
        assert app.logger.level == expected_level


class TestPluggingerAppInstanceProperties:
    """Test PluggingerAppInstance properties."""

    def test_app_name_property(self) -> None:
        """Test app_name property."""
        app_name = "property_test_app"
        app = PluggingerAppInstance(app_name=app_name)

        assert app.app_name == app_name

    def test_global_config_property(self) -> None:
        """Test global_config property."""
        config = GlobalAppConfig(app_name="config_test")
        app = PluggingerAppInstance(global_config=config)

        assert app.global_config is config

    def test_logger_property(self) -> None:
        """Test logger property."""
        app = PluggingerAppInstance()

        logger = app.logger
        assert isinstance(logger, logging.Logger)
        assert logger is app._logger


class TestPluggingerAppInstanceLifecycle:
    """Test PluggingerAppInstance lifecycle methods."""

    @pytest.mark.asyncio
    async def test_run_without_runtime_facade_error(self) -> None:
        """Test that run raises error without runtime facade."""
        app = PluggingerAppInstance()

        with pytest.raises(RuntimeError) as exc_info:
            await app.run()

        assert "runtime facade is missing" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_run_with_main_coroutine(self) -> None:
        """Test run with main coroutine."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        main_called = False

        async def main_coroutine() -> None:
            nonlocal main_called
            main_called = True

        await app.run(main_coroutine)

        assert main_called
        runtime_facade.setup_all_plugins.assert_called_once()
        runtime_facade.teardown_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_without_main_coroutine_keyboard_interrupt(self) -> None:
        """Test run without main coroutine handles KeyboardInterrupt."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        # Mock asyncio.Event to raise KeyboardInterrupt
        with patch("asyncio.Event") as mock_event_class:
            mock_event = AsyncMock()
            mock_event.wait.side_effect = KeyboardInterrupt()
            mock_event_class.return_value = mock_event

            await app.run()

        runtime_facade.setup_all_plugins.assert_called_once()
        runtime_facade.teardown_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_with_exception_in_main(self) -> None:
        """Test run handles exceptions in main coroutine."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        async def failing_main() -> None:
            raise ValueError("Test error")

        with pytest.raises(ValueError):
            await app.run(failing_main)

        runtime_facade.setup_all_plugins.assert_called_once()
        runtime_facade.teardown_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_all_plugins_without_runtime_facade_error(self) -> None:
        """Test that start_all_plugins raises error without runtime facade."""
        app = PluggingerAppInstance()

        with pytest.raises(RuntimeError) as exc_info:
            await app.start_all_plugins()

        assert "runtime facade is missing" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_start_all_plugins_success(self) -> None:
        """Test successful plugin startup."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        await app.start_all_plugins()

        runtime_facade.setup_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_all_plugins_without_runtime_facade(self) -> None:
        """Test stop_all_plugins without runtime facade."""
        app = PluggingerAppInstance()

        # Should not raise, just log warning
        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_stop_all_plugins_success(self) -> None:
        """Test successful plugin shutdown."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        await app.stop_all_plugins()

        runtime_facade.teardown_all_plugins.assert_called_once()
        runtime_facade.shutdown.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_all_plugins_timeout(self) -> None:
        """Test plugin shutdown with timeout."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.teardown_all_plugins.side_effect = TimeoutError()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        await app.stop_all_plugins(timeout=0.1)

        runtime_facade.teardown_all_plugins.assert_called_once()
        runtime_facade.shutdown.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_all_plugins_exception(self) -> None:
        """Test plugin shutdown with exception."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.teardown_all_plugins.side_effect = RuntimeError("Test error")
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        await app.stop_all_plugins()

        runtime_facade.teardown_all_plugins.assert_called_once()
        runtime_facade.shutdown.assert_called_once()


class TestPluggingerAppInstancePluginManagement:
    """Test PluggingerAppInstance plugin management methods."""

    def test_get_plugin_instance_without_runtime_facade(self) -> None:
        """Test get_plugin_instance without runtime facade."""
        app = PluggingerAppInstance()

        result = app.get_plugin_instance("test_plugin")

        assert result is None

    def test_get_plugin_instance_plugins_not_setup(self) -> None:
        """Test get_plugin_instance when plugins are not setup."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.get_plugins_were_setup_flag.return_value = False
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = app.get_plugin_instance("test_plugin")

        assert result is None

    def test_get_plugin_instance_success(self) -> None:
        """Test successful plugin instance retrieval."""
        runtime_facade = MockRuntimeFacade()
        mock_plugin = Mock()
        runtime_facade.get_plugin_by_id.return_value = mock_plugin
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = app.get_plugin_instance("test_plugin")

        assert result is mock_plugin
        runtime_facade.get_plugin_by_id.assert_called_once_with("test_plugin")


class TestPluggingerAppInstanceServiceDelegation:
    """Test PluggingerAppInstance service delegation methods."""

    @pytest.mark.asyncio
    async def test_call_service_without_runtime_facade_error(self) -> None:
        """Test that call_service raises error without runtime facade."""
        app = PluggingerAppInstance()

        with pytest.raises(RuntimeError) as exc_info:
            await app.call_service("test_service")

        assert "runtime facade is missing" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_call_service_success(self) -> None:
        """Test successful service call."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.call_service.return_value = "service_result"
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = await app.call_service("test_service", "arg1", kwarg1="value1")

        assert result == "service_result"
        runtime_facade.call_service.assert_called_once_with("test_service", "arg1", kwarg1="value1")

    def test_has_service_without_runtime_facade(self) -> None:
        """Test has_service without runtime facade."""
        app = PluggingerAppInstance()

        result = app.has_service("test_service")

        assert result is False

    def test_has_service_success(self) -> None:
        """Test successful service check."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.has_service.return_value = True
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = app.has_service("test_service")

        assert result is True
        runtime_facade.has_service.assert_called_once_with("test_service")

    def test_list_services_without_runtime_facade(self) -> None:
        """Test list_services without runtime facade."""
        app = PluggingerAppInstance()

        result = app.list_services()

        assert result == []

    def test_list_services_success(self) -> None:
        """Test successful service listing."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.list_services.return_value = ["service1", "service2"]
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = app.list_services()

        assert result == ["service1", "service2"]
        runtime_facade.list_services.assert_called_once()


class TestPluggingerAppInstanceEventDelegation:
    """Test PluggingerAppInstance event delegation methods."""

    @pytest.mark.asyncio
    async def test_emit_event_without_runtime_facade_error(self) -> None:
        """Test that emit_event raises error without runtime facade."""
        app = PluggingerAppInstance()

        with pytest.raises(RuntimeError) as exc_info:
            await app.emit_event("test.event", {"data": "value"})

        assert "runtime facade is missing" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_emit_event_success(self) -> None:
        """Test successful event emission."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)  # type: ignore[arg-type]

        event_data = {"key": "value", "number": 42}
        await app.emit_event("user.created", event_data)

        runtime_facade.emit_event.assert_called_once_with("user.created", event_data)

    def test_list_event_patterns_without_runtime_facade(self) -> None:
        """Test list_event_patterns without runtime facade."""
        app = PluggingerAppInstance()

        result = app.list_event_patterns()

        assert result == []

    def test_list_event_patterns_success(self) -> None:
        """Test successful event pattern listing."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.list_event_patterns.return_value = ["user.*", "admin.action"]
        app = PluggingerAppInstance(runtime_facade=runtime_facade)  # type: ignore[arg-type]

        result = app.list_event_patterns()

        assert result == ["user.*", "admin.action"]
        runtime_facade.list_event_patterns.assert_called_once()


class TestPluggingerAppInstanceBackgroundTasks:
    """Test PluggingerAppInstance background task management."""

    @pytest.mark.asyncio
    async def test_create_managed_task_success(self) -> None:
        """Test successful managed task creation."""
        app = PluggingerAppInstance()

        async def test_coro() -> str:
            return "task_result"

        task = app.create_managed_task(test_coro(), name="test_task")

        assert isinstance(task, asyncio.Task)
        assert task.get_name() == "test_task"

        # Clean up the task
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

    @pytest.mark.asyncio
    async def test_create_managed_task_without_name(self) -> None:
        """Test managed task creation without name."""
        app = PluggingerAppInstance()

        async def test_coro() -> str:
            return "task_result"

        task = app.create_managed_task(test_coro())

        assert isinstance(task, asyncio.Task)

        # Clean up the task
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

    def test_create_managed_task_error(self) -> None:
        """Test managed task creation with error."""
        app = PluggingerAppInstance()

        # Pass invalid coroutine (not a coroutine)
        with pytest.raises(BackgroundTaskError) as exc_info:
            app.create_managed_task("not_a_coroutine")

        assert "Failed to create managed task" in str(exc_info.value)


class TestPluggingerAppInstanceExecutorDelegation:
    """Test PluggingerAppInstance executor delegation methods."""

    def test_get_executor_without_runtime_facade_error(self) -> None:
        """Test that get_executor raises error without runtime facade."""
        app = PluggingerAppInstance()

        with pytest.raises(RuntimeError) as exc_info:
            app.get_executor("test_executor")

        assert "runtime facade is missing" in str(exc_info.value)

    def test_get_executor_success(self) -> None:
        """Test successful executor retrieval."""
        runtime_facade = MockRuntimeFacade()
        mock_executor = Mock()
        runtime_facade.get_executor.return_value = mock_executor
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = app.get_executor("test_executor")

        assert result is mock_executor
        runtime_facade.get_executor.assert_called_once_with("test_executor")

    def test_get_executor_default(self) -> None:
        """Test executor retrieval with default name."""
        runtime_facade = MockRuntimeFacade()
        mock_executor = Mock()
        runtime_facade.get_executor.return_value = mock_executor
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = app.get_executor()

        assert result is mock_executor
        runtime_facade.get_executor.assert_called_once_with(None)

    def test_register_executor_without_runtime_facade_error(self) -> None:
        """Test that register_executor raises error without runtime facade."""
        app = PluggingerAppInstance()

        with pytest.raises(RuntimeError) as exc_info:
            app.register_executor("test_executor", Mock())

        assert "runtime facade is missing" in str(exc_info.value)

    def test_register_executor_success(self) -> None:
        """Test successful executor registration."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)
        mock_config = Mock()

        app.register_executor("test_executor", mock_config)

        runtime_facade.register_executor.assert_called_once_with("test_executor", mock_config)

    def test_get_runtime_facade_without_facade_error(self) -> None:
        """Test that get_runtime_facade raises error without runtime facade."""
        app = PluggingerAppInstance()

        with pytest.raises(RuntimeError) as exc_info:
            app.get_runtime_facade()

        assert "runtime facade is missing" in str(exc_info.value)

    def test_get_runtime_facade_success(self) -> None:
        """Test successful runtime facade retrieval."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        result = app.get_runtime_facade()

        assert result is runtime_facade


class TestPluggingerAppInstanceIntegration:
    """Test PluggingerAppInstance integration scenarios."""

    @pytest.mark.asyncio
    async def test_full_lifecycle_integration(self) -> None:
        """Test complete application lifecycle."""
        runtime_facade = MockRuntimeFacade()
        config = GlobalAppConfig(app_name="integration_test", max_fractal_depth=3)
        app = PluggingerAppInstance(
            app_name="test_app",
            runtime_facade=runtime_facade,
            global_config=config
        )

        # Test properties
        assert app.app_name == "test_app"
        assert app.global_config is config
        assert isinstance(app.logger, logging.Logger)

        # Test service operations
        runtime_facade.has_service.return_value = True
        runtime_facade.list_services.return_value = ["service1", "service2"]
        runtime_facade.call_service.return_value = "result"

        assert app.has_service("test_service") is True
        assert app.list_services() == ["service1", "service2"]
        result = await app.call_service("test_service", "arg")
        assert result == "result"

        # Test event operations
        runtime_facade.list_event_patterns.return_value = ["pattern1", "pattern2"]

        assert app.list_event_patterns() == ["pattern1", "pattern2"]
        await app.emit_event("test.event", {"data": "value"})

        # Test plugin operations
        mock_plugin = Mock()
        runtime_facade.get_plugin_by_id.return_value = mock_plugin

        plugin = app.get_plugin_instance("plugin_id")
        assert plugin is mock_plugin

        # Test executor operations
        mock_executor = Mock()
        runtime_facade.get_executor.return_value = mock_executor

        executor = app.get_executor("executor_name")
        assert executor is mock_executor

        app.register_executor("new_executor", Mock())

        # Test lifecycle
        await app.start_all_plugins()
        await app.stop_all_plugins()

        # Verify all calls
        runtime_facade.setup_all_plugins.assert_called()
        runtime_facade.teardown_all_plugins.assert_called()
        runtime_facade.shutdown.assert_called()

    def test_fractal_composition_setup(self) -> None:
        """Test fractal composition configuration."""
        parent_plugin = Mock()
        config = GlobalAppConfig(max_fractal_depth=5)

        app = PluggingerAppInstance(
            app_name="child_app",
            global_config=config,
            parent_app_plugin_context=parent_plugin,
            _builder_fractal_depth=2
        )

        assert app._parent_app_plugin_context is parent_plugin
        assert app._current_app_depth == 2
        assert app._current_build_depth_for_sub_apps == 2
        assert app._max_build_depth_for_sub_apps == 5

    @pytest.mark.asyncio
    async def test_error_handling_in_run(self) -> None:
        """Test error handling in run method."""
        runtime_facade = MockRuntimeFacade()
        runtime_facade.setup_all_plugins.side_effect = RuntimeError("Setup failed")
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        with pytest.raises(RuntimeError, match="Setup failed"):
            await app.run()

        # Should still attempt teardown
        runtime_facade.teardown_all_plugins.assert_called_once()

    @pytest.mark.asyncio
    async def test_graceful_shutdown_on_cancellation(self) -> None:
        """Test graceful shutdown on task cancellation."""
        runtime_facade = MockRuntimeFacade()
        app = PluggingerAppInstance(runtime_facade=runtime_facade)

        async def cancelled_main() -> None:
            raise asyncio.CancelledError()

        await app.run(cancelled_main)

        runtime_facade.setup_all_plugins.assert_called_once()
        runtime_facade.teardown_all_plugins.assert_called_once()

    def test_logger_configuration_with_custom_config(self) -> None:
        """Test logger configuration with custom global config."""
        from plugginger.core.config import LogLevel

        config = GlobalAppConfig(
            app_name="logger_test",
            log_level=LogLevel.DEBUG
        )
        app = PluggingerAppInstance(app_name="test_app", global_config=config)

        # Logger level is an integer, not a string
        expected_level = getattr(logging, LogLevel.DEBUG.value)
        assert app.logger.level == expected_level
        assert app.logger.name == "plugginger.app.test_app"

    @pytest.mark.asyncio
    async def test_background_task_lifecycle(self) -> None:
        """Test background task creation and lifecycle."""
        app = PluggingerAppInstance()

        task_executed = False

        async def background_work() -> None:
            nonlocal task_executed
            await asyncio.sleep(0.01)  # Small delay
            task_executed = True

        task = app.create_managed_task(background_work(), name="bg_task")

        # Wait for task completion
        await task

        assert task_executed
        assert task.done()
        assert not task.cancelled()

    def test_app_instance_without_any_configuration(self) -> None:
        """Test app instance creation with minimal configuration."""
        app = PluggingerAppInstance()

        # Should have sensible defaults
        assert app.app_name == DEFAULT_APP_NAME
        assert app._runtime_facade is None
        assert isinstance(app.global_config, GlobalAppConfig)
        assert app._parent_app_plugin_context is None
        assert app._current_app_depth == 0

        # Should handle missing runtime facade gracefully
        assert app.has_service("any_service") is False
        assert app.list_services() == []
        assert app.list_event_patterns() == []
        assert app.get_plugin_instance("any_plugin") is None
