# tests/unit/internal/runtime/test_fault_policy.py

"""
Unit tests for plugginger._internal.runtime.fault_policy module.

Tests the FaultPolicyHandler class and its error handling strategies for
event listener fault management.
"""

from __future__ import annotations

import asyncio
from unittest.mock import Mock

import pytest

# Import the actual module to test for proper coverage
# We need to import it directly to avoid circular imports
import importlib.util
import sys
from pathlib import Path

# Load the fault_policy module directly from file
fault_policy_path = Path(__file__).parent.parent.parent.parent.parent / "src" / "plugginger" / "_internal" / "runtime" / "fault_policy.py"
spec = importlib.util.spec_from_file_location("fault_policy", fault_policy_path)
if spec is None or spec.loader is None:
    raise ImportError("Could not load fault_policy module")

fault_policy_module = importlib.util.module_from_spec(spec)
sys.modules["fault_policy"] = fault_policy_module
spec.loader.exec_module(fault_policy_module)

FaultPolicyHandler = fault_policy_module.FaultPolicyHandler

from plugginger.core.config import EventListenerFaultPolicy
from plugginger.core.exceptions import (
    EventListenerTimeoutError,
    EventListenerUnhandledError,
)
from plugginger.core.types import EventHandlerType


# Helper function to create proper async event handlers for testing
async def create_async_handler() -> None:
    """Helper to create async event handler for testing."""
    pass


class TestFaultPolicyHandlerInitialization:
    """Test FaultPolicyHandler initialization."""

    def test_initialization_log_and_continue(self) -> None:
        """Test initialization with LOG_AND_CONTINUE policy."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        assert handler._policy == EventListenerFaultPolicy.LOG_AND_CONTINUE
        assert handler._logger is logger
        assert handler._dead_listeners == set()

    def test_initialization_fail_fast(self) -> None:
        """Test initialization with FAIL_FAST policy."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        assert handler._policy == EventListenerFaultPolicy.FAIL_FAST
        assert handler._logger is logger
        assert handler._dead_listeners == set()

    def test_initialization_isolate_and_log(self) -> None:
        """Test initialization with ISOLATE_AND_LOG policy."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        assert handler._policy == EventListenerFaultPolicy.ISOLATE_AND_LOG
        assert handler._logger is logger
        assert handler._dead_listeners == set()

    def test_dead_listeners_set_is_mutable(self) -> None:
        """Test that dead listeners set is mutable and independent per instance."""
        logger = Mock()
        handler1 = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)
        handler2 = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        # Should be different sets
        assert handler1._dead_listeners is not handler2._dead_listeners

        # Should both be empty initially
        assert handler1._dead_listeners == set()
        assert handler2._dead_listeners == set()


class TestShouldInvoke:
    """Test should_invoke method."""

    def test_should_invoke_log_and_continue_always_true(self) -> None:
        """Test that LOG_AND_CONTINUE policy always allows invocation."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        async def test_listener() -> None:
            pass

        # Should always return True regardless of listener state
        assert handler.should_invoke(test_listener) is True

        # Even if we manually add to dead listeners, should still return True
        handler._dead_listeners.add(id(test_listener))
        assert handler.should_invoke(test_listener) is True

    def test_should_invoke_fail_fast_always_true(self) -> None:
        """Test that FAIL_FAST policy always allows invocation."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        async def test_listener() -> None:
            pass

        # Should always return True regardless of listener state
        assert handler.should_invoke(test_listener) is True

        # Even if we manually add to dead listeners, should still return True
        handler._dead_listeners.add(id(test_listener))
        assert handler.should_invoke(test_listener) is True

    def test_should_invoke_isolate_and_log_healthy_listener(self) -> None:
        """Test that ISOLATE_AND_LOG policy allows healthy listeners."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        async def test_listener() -> None:
            pass

        # Healthy listener should be allowed
        assert handler.should_invoke(test_listener) is True

    def test_should_invoke_isolate_and_log_dead_listener(self) -> None:
        """Test that ISOLATE_AND_LOG policy blocks dead listeners."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        async def test_listener() -> None:
            pass

        # Add listener to dead set
        listener_id = id(test_listener)
        handler._dead_listeners.add(listener_id)

        # Dead listener should be blocked
        assert handler.should_invoke(test_listener) is False

    def test_should_invoke_isolate_and_log_multiple_listeners(self) -> None:
        """Test ISOLATE_AND_LOG policy with multiple listeners."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        async def healthy_listener() -> None:
            pass

        async def dead_listener() -> None:
            pass

        # Mark one as dead
        handler._dead_listeners.add(id(dead_listener))

        # Healthy should be allowed, dead should be blocked
        assert handler.should_invoke(healthy_listener) is True
        assert handler.should_invoke(dead_listener) is False

    def test_should_invoke_with_different_listener_types(self) -> None:
        """Test should_invoke with different types of listeners."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        # Function listener
        async def func_listener() -> None:
            pass

        # Lambda-like listener (using def instead of lambda for ruff compliance)
        def lambda_listener() -> None:
            pass

        # Method listener
        class TestClass:
            async def method_listener(self) -> None:
                pass

        test_obj = TestClass()
        method_listener = test_obj.method_listener

        # All should be allowed initially
        assert handler.should_invoke(func_listener) is True
        assert handler.should_invoke(lambda_listener) is True
        assert handler.should_invoke(method_listener) is True

        # Mark function as dead
        handler._dead_listeners.add(id(func_listener))

        # Only function should be blocked
        assert handler.should_invoke(func_listener) is False
        assert handler.should_invoke(lambda_listener) is True
        assert handler.should_invoke(method_listener) is True


class TestHandleErrorLogAndContinue:
    """Test handle_error method with LOG_AND_CONTINUE policy."""

    def test_handle_error_logs_message(self) -> None:
        """Test that error is logged with proper format."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        exc = ValueError("test error")
        handler.handle_error("test.listener", 12345, exc)

        logger.assert_called_once_with(
            "[Plugginger] Error in event listener 'test.listener': ValueError('test error')"
        )

    def test_handle_error_does_not_raise(self) -> None:
        """Test that LOG_AND_CONTINUE does not raise exceptions."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        # Should not raise for any exception type
        handler.handle_error("test.listener", 12345, ValueError("test"))
        handler.handle_error("test.listener", 12345, RuntimeError("test"))
        handler.handle_error("test.listener", 12345, TimeoutError())
        handler.handle_error("test.listener", 12345, KeyError("test"))

    def test_handle_error_does_not_isolate_listeners(self) -> None:
        """Test that LOG_AND_CONTINUE does not add to dead listeners."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        listener_id = 12345
        handler.handle_error("test.listener", listener_id, ValueError("test"))

        # Dead listeners set should remain empty
        assert listener_id not in handler._dead_listeners
        assert len(handler._dead_listeners) == 0

    def test_handle_error_multiple_errors_same_listener(self) -> None:
        """Test multiple errors from same listener with LOG_AND_CONTINUE."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        listener_id = 12345

        # Handle multiple errors
        handler.handle_error("test.listener", listener_id, ValueError("error 1"))
        handler.handle_error("test.listener", listener_id, RuntimeError("error 2"))
        handler.handle_error("test.listener", listener_id, KeyError("error 3"))

        # Should log all errors
        assert logger.call_count == 3

        # Should not isolate listener
        assert listener_id not in handler._dead_listeners


class TestHandleErrorFailFast:
    """Test handle_error method with FAIL_FAST policy."""

    def test_handle_error_timeout_error_raises_timeout_exception(self) -> None:
        """Test that TimeoutError raises EventListenerTimeoutError."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        timeout_exc = TimeoutError()

        with pytest.raises(EventListenerTimeoutError) as exc_info:
            handler.handle_error("test.listener", 12345, timeout_exc)

        # Check exception message
        assert "test.listener" in str(exc_info.value)
        assert "timed out" in str(exc_info.value)
        assert "FAIL_FAST policy active" in str(exc_info.value)

        # Check exception chaining
        assert exc_info.value.__cause__ is timeout_exc

    def test_handle_error_other_exception_raises_unhandled_exception(self) -> None:
        """Test that other exceptions raise EventListenerUnhandledError."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        value_exc = ValueError("test error")

        with pytest.raises(EventListenerUnhandledError) as exc_info:
            handler.handle_error("test.listener", 12345, value_exc)

        # Check exception message
        assert "test.listener" in str(exc_info.value)
        assert "Unhandled exception" in str(exc_info.value)
        assert "FAIL_FAST policy active" in str(exc_info.value)

        # Check exception chaining
        assert exc_info.value.__cause__ is value_exc

    def test_handle_error_logs_before_raising(self) -> None:
        """Test that error is logged before exception is raised."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        exc = ValueError("test error")

        with pytest.raises(EventListenerUnhandledError):
            handler.handle_error("test.listener", 12345, exc)

        # Should have logged the error
        logger.assert_called_once_with(
            "[Plugginger] Error in event listener 'test.listener': ValueError('test error')"
        )

    def test_handle_error_does_not_isolate_listeners(self) -> None:
        """Test that FAIL_FAST does not add to dead listeners."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        listener_id = 12345

        with pytest.raises(EventListenerUnhandledError):
            handler.handle_error("test.listener", listener_id, ValueError("test"))

        # Dead listeners set should remain empty
        assert listener_id not in handler._dead_listeners
        assert len(handler._dead_listeners) == 0

    def test_handle_error_different_exception_types(self) -> None:
        """Test FAIL_FAST with different exception types."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        # TimeoutError should raise EventListenerTimeoutError
        with pytest.raises(EventListenerTimeoutError):
            handler.handle_error("test.listener", 1, TimeoutError())

        # Other exceptions should raise EventListenerUnhandledError
        exception_types = [ValueError, RuntimeError, KeyError, TypeError, AttributeError]

        for exc_type in exception_types:
            with pytest.raises(EventListenerUnhandledError):
                handler.handle_error("test.listener", 1, exc_type("test"))


class TestHandleErrorIsolateAndLog:
    """Test handle_error method with ISOLATE_AND_LOG policy."""

    def test_handle_error_logs_error_and_isolation(self) -> None:
        """Test that both error and isolation are logged."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        exc = ValueError("test error")
        handler.handle_error("test.listener", 12345, exc)

        # Should log both the error and the isolation
        assert logger.call_count == 2

        # Check log messages
        calls = logger.call_args_list
        assert "[Plugginger] Error in event listener 'test.listener': ValueError('test error')" in calls[0][0][0]
        assert "[Plugginger] Listener 'test.listener' isolated due to error" in calls[1][0][0]

    def test_handle_error_adds_to_dead_listeners(self) -> None:
        """Test that listener is added to dead listeners set."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        listener_id = 12345
        handler.handle_error("test.listener", listener_id, ValueError("test"))

        # Listener should be added to dead set
        assert listener_id in handler._dead_listeners
        assert len(handler._dead_listeners) == 1

    def test_handle_error_does_not_raise(self) -> None:
        """Test that ISOLATE_AND_LOG does not raise exceptions."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        # Should not raise for any exception type
        handler.handle_error("test.listener", 1, ValueError("test"))
        handler.handle_error("test.listener", 2, RuntimeError("test"))
        handler.handle_error("test.listener", 3, TimeoutError())
        handler.handle_error("test.listener", 4, KeyError("test"))

    def test_handle_error_multiple_listeners_isolated(self) -> None:
        """Test that multiple listeners can be isolated."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        # Isolate multiple listeners
        handler.handle_error("listener1", 111, ValueError("error 1"))
        handler.handle_error("listener2", 222, RuntimeError("error 2"))
        handler.handle_error("listener3", 333, KeyError("error 3"))

        # All should be in dead set
        assert 111 in handler._dead_listeners
        assert 222 in handler._dead_listeners
        assert 333 in handler._dead_listeners
        assert len(handler._dead_listeners) == 3

    def test_handle_error_same_listener_multiple_times(self) -> None:
        """Test handling multiple errors from same listener."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        listener_id = 12345

        # Handle multiple errors from same listener
        handler.handle_error("test.listener", listener_id, ValueError("error 1"))
        handler.handle_error("test.listener", listener_id, RuntimeError("error 2"))

        # Should only appear once in dead set (sets don't allow duplicates)
        assert listener_id in handler._dead_listeners
        assert len(handler._dead_listeners) == 1

        # Should log all errors and isolations
        assert logger.call_count == 4  # 2 errors + 2 isolations


class TestHandleErrorEdgeCases:
    """Test edge cases for handle_error method."""

    def test_handle_error_with_complex_exception_repr(self) -> None:
        """Test error handling with complex exception representations."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        # Exception with complex message
        complex_exc = ValueError("Error with 'quotes' and \"double quotes\" and special chars: !@#$%^&*()")
        handler.handle_error("complex.listener", 12345, complex_exc)

        # Should log the exception properly
        logger.assert_called_once()
        log_message = logger.call_args[0][0]
        assert "complex.listener" in log_message
        assert "ValueError" in log_message

    def test_handle_error_with_nested_exception(self) -> None:
        """Test error handling with nested exceptions."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)

        # Create nested exception
        try:
            try:
                raise ValueError("inner error")
            except ValueError as e:
                raise RuntimeError("outer error") from e
        except RuntimeError as nested_exc:
            with pytest.raises(EventListenerUnhandledError) as exc_info:
                handler.handle_error("nested.listener", 12345, nested_exc)

            # Should preserve the original exception as cause
            assert exc_info.value.__cause__ is nested_exc

    def test_handle_error_with_unicode_listener_name(self) -> None:
        """Test error handling with unicode characters in listener name."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        unicode_name = "测试.listener.ñame.🎯"
        exc = ValueError("test error")

        # Should handle unicode names without issues
        handler.handle_error(unicode_name, 12345, exc)

        logger.assert_called_once()
        log_message = logger.call_args[0][0]
        assert unicode_name in log_message

    def test_handle_error_with_very_long_listener_name(self) -> None:
        """Test error handling with very long listener names."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        long_name = "very.long.listener.name." * 50  # Very long name
        exc = ValueError("test error")

        handler.handle_error(long_name, 12345, exc)

        # Should handle long names without issues
        assert logger.call_count == 2
        assert 12345 in handler._dead_listeners

    def test_handle_error_with_zero_listener_id(self) -> None:
        """Test error handling with edge case listener ID."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        # Use edge case ID (0)
        listener_id = 0
        handler.handle_error("test.listener", listener_id, ValueError("test"))

        # Should work with any integer ID
        assert listener_id in handler._dead_listeners

    def test_handle_error_with_negative_listener_id(self) -> None:
        """Test error handling with negative listener ID."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        # Use negative ID
        listener_id = -12345
        handler.handle_error("test.listener", listener_id, ValueError("test"))

        # Should work with negative IDs too
        assert listener_id in handler._dead_listeners


class TestIntegrationScenarios:
    """Test integration scenarios combining should_invoke and handle_error."""

    def test_listener_lifecycle_with_isolation(self) -> None:
        """Test complete listener lifecycle with isolation policy."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        async def test_listener() -> None:
            pass

        listener_id = id(test_listener)

        # Initially, listener should be invokable
        assert handler.should_invoke(test_listener) is True

        # Handle an error
        handler.handle_error("test.listener", listener_id, ValueError("test error"))

        # Now listener should be isolated
        assert handler.should_invoke(test_listener) is False
        assert listener_id in handler._dead_listeners

    def test_multiple_listeners_with_mixed_states(self) -> None:
        """Test scenario with multiple listeners in different states."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        async def healthy_listener1() -> None:
            pass

        async def healthy_listener2() -> None:
            pass

        async def failing_listener() -> None:
            pass

        # Initially all should be invokable
        assert handler.should_invoke(healthy_listener1) is True
        assert handler.should_invoke(healthy_listener2) is True
        assert handler.should_invoke(failing_listener) is True

        # Fail one listener
        handler.handle_error("failing.listener", id(failing_listener), RuntimeError("failed"))

        # Check states after failure
        assert handler.should_invoke(healthy_listener1) is True
        assert handler.should_invoke(healthy_listener2) is True
        assert handler.should_invoke(failing_listener) is False

    def test_policy_behavior_consistency(self) -> None:
        """Test that each policy behaves consistently across multiple operations."""
        logger = Mock()

        # Test LOG_AND_CONTINUE consistency
        log_handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        async def listener1() -> None:
            pass

        # Multiple errors should not affect invocation
        for i in range(5):
            log_handler.handle_error(f"listener1.{i}", id(listener1), ValueError(f"error {i}"))
            assert log_handler.should_invoke(listener1) is True

        # Test ISOLATE_AND_LOG consistency
        isolate_handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        async def listener2() -> None:
            pass

        # First error should isolate
        isolate_handler.handle_error("listener2", id(listener2), ValueError("error"))
        assert isolate_handler.should_invoke(listener2) is False

        # Subsequent errors should keep it isolated
        for i in range(3):
            isolate_handler.handle_error(f"listener2.{i}", id(listener2), ValueError(f"error {i}"))
            assert isolate_handler.should_invoke(listener2) is False

    def test_handler_isolation_between_instances(self) -> None:
        """Test that different handler instances don't interfere with each other."""
        logger = Mock()

        handler1 = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)
        handler2 = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)

        async def test_listener() -> None:
            pass

        listener_id = id(test_listener)

        # Isolate listener in handler1
        handler1.handle_error("test.listener", listener_id, ValueError("error"))

        # handler1 should block the listener
        assert handler1.should_invoke(test_listener) is False

        # handler2 should still allow the listener
        assert handler2.should_invoke(test_listener) is True

        # Verify isolation sets are independent
        assert listener_id in handler1._dead_listeners
        assert listener_id not in handler2._dead_listeners


class TestLoggerInteraction:
    """Test interaction with logger function."""

    def test_logger_called_with_correct_format(self) -> None:
        """Test that logger is called with the expected message format."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        exc = ValueError("test message")
        handler.handle_error("my.test.listener", 12345, exc)

        logger.assert_called_once_with(
            "[Plugginger] Error in event listener 'my.test.listener': ValueError('test message')"
        )

    def test_logger_exception_repr_format(self) -> None:
        """Test that exception is formatted using repr."""
        logger = Mock()
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)

        # Test different exception types
        exceptions = [
            ValueError("value error"),
            RuntimeError("runtime error"),
            KeyError("missing_key"),
            TypeError("type error"),
        ]

        for exc in exceptions:
            logger.reset_mock()
            handler.handle_error("test.listener", 12345, exc)

            log_message = logger.call_args[0][0]
            # Should contain the exception repr
            assert repr(exc) in log_message

    def test_logger_not_called_multiple_times_unnecessarily(self) -> None:
        """Test that logger is called exactly the expected number of times."""
        logger = Mock()

        # LOG_AND_CONTINUE: 1 call per error
        log_handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, logger)
        log_handler.handle_error("test", 1, ValueError("test"))
        assert logger.call_count == 1

        # FAIL_FAST: 1 call per error (before raising)
        logger.reset_mock()
        fail_handler = FaultPolicyHandler(EventListenerFaultPolicy.FAIL_FAST, logger)
        with pytest.raises(EventListenerUnhandledError):
            fail_handler.handle_error("test", 1, ValueError("test"))
        assert logger.call_count == 1

        # ISOLATE_AND_LOG: 2 calls per error (error + isolation)
        logger.reset_mock()
        isolate_handler = FaultPolicyHandler(EventListenerFaultPolicy.ISOLATE_AND_LOG, logger)
        isolate_handler.handle_error("test", 1, ValueError("test"))
        assert logger.call_count == 2

    def test_logger_with_callable_that_raises(self) -> None:
        """Test behavior when logger itself raises an exception."""
        def failing_logger(message: str) -> None:
            raise RuntimeError("Logger failed")

        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, failing_logger)

        # The handler should not catch logger exceptions - they should propagate
        with pytest.raises(RuntimeError, match="Logger failed"):
            handler.handle_error("test.listener", 12345, ValueError("original error"))
