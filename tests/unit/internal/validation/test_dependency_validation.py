# tests/unit/internal/validation/test_dependency_validation.py

"""
Unit tests for plugginger._internal.validation.dependency_validation module.

Tests the DependencyValidator class for validating plugin dependencies,
versions, and injection signatures.
"""

from __future__ import annotations

import inspect
from unittest.mock import Mock

import pytest
from packaging.specifiers import InvalidSpecifier
from packaging.version import InvalidVersion

# Import the actual module to test for proper coverage
import importlib.util
import sys
from pathlib import Path

# Load the dependency_validation module directly from file
dep_validation_path = Path(__file__).parent.parent.parent.parent.parent / "src" / "plugginger" / "_internal" / "validation" / "dependency_validation.py"
spec = importlib.util.spec_from_file_location("dependency_validation", dep_validation_path)
if spec is None or spec.loader is None:
    raise ImportError("Could not load dependency_validation module")

dep_validation_module = importlib.util.module_from_spec(spec)
sys.modules["dependency_validation"] = dep_validation_module
spec.loader.exec_module(dep_validation_module)

DependencyValidator = dep_validation_module.DependencyValidator

# Import after dynamic loading to avoid import order issues
from plugginger._internal.graph import DependencyGraph  # noqa: E402
from plugginger.api.depends import Depends  # noqa: E402
from plugginger.api.plugin import PluginBase  # noqa: E402
from plugginger.core.exceptions import (  # noqa: E402
    ConfigurationError,
    DependencyError,
    DependencyVersionConflictError,
    MissingDependencyError,
    PluginRegistrationError,
)


# Test plugin classes for injection signature testing
class TestPluginWithDependencies(PluginBase):
    """Test plugin with dependencies."""

    def __init__(self, app: object, dependency1: object, dependency2: object) -> None:
        super().__init__(app)  # type: ignore[arg-type]
        self.dependency1 = dependency1
        self.dependency2 = dependency2


class TestPluginWithOptionalDependencies(PluginBase):
    """Test plugin with optional dependencies."""

    def __init__(self, app: object, required_dep: object, optional_dep: object = None) -> None:
        super().__init__(app)  # type: ignore[arg-type]
        self.required_dep = required_dep
        self.optional_dep = optional_dep


class TestPluginWithKwargs(PluginBase):
    """Test plugin with **kwargs parameter."""

    def __init__(self, app: object, **injected_dependencies: object) -> None:
        super().__init__(app)  # type: ignore[arg-type]
        self.injected_dependencies = injected_dependencies


class TestPluginNoDependencies(PluginBase):
    """Test plugin with no dependencies."""

    def __init__(self, app: object) -> None:
        super().__init__(app)  # type: ignore[arg-type]


class TestPluginWithMixedDeps(PluginBase):
    """Test plugin with mixed dependencies for constraint testing."""

    def __init__(self, app: object, dep1: object, dep2: object, dep3: object) -> None:
        super().__init__(app)  # type: ignore[arg-type]
        self.dep1 = dep1
        self.dep2 = dep2
        self.dep3 = dep3


class TestPluginWithKwargsAndParams(PluginBase):
    """Test plugin with both regular parameters and **kwargs."""

    def __init__(self, app: object, required_param: object, **injected_dependencies: object) -> None:
        super().__init__(app)  # type: ignore[arg-type]
        self.required_param = required_param
        self.injected_dependencies = injected_dependencies


class TestDependencyValidatorInitialization:
    """Test DependencyValidator initialization."""

    def test_initialization(self) -> None:
        """Test validator initialization with resolver functions."""
        version_resolver = Mock()
        class_resolver = Mock()
        
        validator = DependencyValidator(version_resolver, class_resolver)
        
        assert validator._version_resolver is version_resolver
        assert validator._class_resolver is class_resolver

    def test_initialization_with_callable_resolvers(self) -> None:
        """Test initialization with actual callable resolvers."""
        def version_resolver(plugin_name: str) -> str:
            return "1.0.0"
        
        def class_resolver(plugin_name: str) -> type[PluginBase]:
            return TestPluginNoDependencies
        
        validator = DependencyValidator(version_resolver, class_resolver)
        
        assert validator._version_resolver is version_resolver
        assert validator._class_resolver is class_resolver


class TestValidateGraphStructure:
    """Test validate_graph_structure method."""

    def test_validate_graph_structure_valid(self) -> None:
        """Test validation of valid graph structure."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        # Create graph with valid dependencies
        graph: DependencyGraph[str] = DependencyGraph()
        graph.add_node("plugin1")
        graph.add_node("plugin2")
        graph.add_node("plugin3")
        graph.add_dependency_edge("plugin2", "plugin1")  # plugin1 depends on plugin2
        graph.add_dependency_edge("plugin3", "plugin1")  # plugin1 depends on plugin3
        
        registered_plugins = {"plugin1", "plugin2", "plugin3"}
        
        # Should not raise any exception
        validator.validate_graph_structure(graph, registered_plugins)

    def test_validate_graph_structure_missing_dependency(self) -> None:
        """Test validation fails when dependency is missing."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        # Create graph with missing dependency
        graph: DependencyGraph[str] = DependencyGraph()
        graph.add_node("plugin1")
        graph.add_node("plugin2")
        graph.add_dependency_edge("plugin2", "plugin1")  # plugin1 depends on plugin2
        graph.add_dependency_edge("missing_plugin", "plugin1")  # plugin1 depends on missing plugin
        
        registered_plugins = {"plugin1", "plugin2"}  # missing_plugin not registered
        
        with pytest.raises(MissingDependencyError) as exc_info:
            validator.validate_graph_structure(graph, registered_plugins)
        
        error_msg = str(exc_info.value)
        assert "plugin1" in error_msg
        assert "missing_plugin" in error_msg
        assert "not registered" in error_msg

    def test_validate_graph_structure_multiple_missing_dependencies(self) -> None:
        """Test validation with multiple missing dependencies."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        # Create graph with multiple missing dependencies
        graph: DependencyGraph[str] = DependencyGraph()
        graph.add_node("plugin1")
        graph.add_node("plugin2")
        graph.add_dependency_edge("missing1", "plugin1")
        graph.add_dependency_edge("missing2", "plugin2")
        
        registered_plugins = {"plugin1", "plugin2"}
        
        # Should raise for the first missing dependency encountered
        with pytest.raises(MissingDependencyError):
            validator.validate_graph_structure(graph, registered_plugins)

    def test_validate_graph_structure_empty_graph(self) -> None:
        """Test validation of empty graph."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        graph: DependencyGraph[str] = DependencyGraph()
        registered_plugins: set[str] = set()
        
        # Should not raise any exception
        validator.validate_graph_structure(graph, registered_plugins)

    def test_validate_graph_structure_no_dependencies(self) -> None:
        """Test validation of graph with nodes but no dependencies."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        graph: DependencyGraph[str] = DependencyGraph()
        graph.add_node("plugin1")
        graph.add_node("plugin2")
        graph.add_node("plugin3")
        
        registered_plugins = {"plugin1", "plugin2", "plugin3"}
        
        # Should not raise any exception
        validator.validate_graph_structure(graph, registered_plugins)


class TestValidateDependencyVersionsAndSignatures:
    """Test validate_dependency_versions_and_signatures method."""

    def test_validate_versions_and_signatures_valid(self) -> None:
        """Test validation with valid versions and signatures."""
        version_resolver = Mock(return_value="1.5.0")
        class_resolver = Mock(return_value=TestPluginWithDependencies)
        validator = DependencyValidator(version_resolver, class_resolver)
        
        dependencies = {
            "plugin1": [
                Depends("dependency1", version_constraint=">=1.0,<2.0"),
                Depends("dependency2", version_constraint=">=1.0"),
            ]
        }
        
        # Should not raise any exception
        validator.validate_dependency_versions_and_signatures(dependencies)
        
        # Verify resolvers were called
        assert version_resolver.call_count == 2
        class_resolver.assert_called_once_with("plugin1")

    def test_validate_versions_and_signatures_no_version_constraint(self) -> None:
        """Test validation without version constraints."""
        version_resolver = Mock()
        class_resolver = Mock(return_value=TestPluginWithDependencies)
        validator = DependencyValidator(version_resolver, class_resolver)
        
        dependencies = {
            "plugin1": [
                Depends("dependency1"),  # No version constraint
                Depends("dependency2"),  # No version constraint
            ]
        }
        
        # Should not raise any exception
        validator.validate_dependency_versions_and_signatures(dependencies)
        
        # Version resolver should not be called when no constraints
        version_resolver.assert_not_called()
        class_resolver.assert_called_once_with("plugin1")

    def test_validate_class_resolver_error(self) -> None:
        """Test validation when class resolver fails."""
        version_resolver = Mock()
        class_resolver = Mock(side_effect=RuntimeError("Class not found"))
        validator = DependencyValidator(version_resolver, class_resolver)
        
        dependencies = {
            "plugin1": [Depends("dependency1")]
        }
        
        with pytest.raises(DependencyError) as exc_info:
            validator.validate_dependency_versions_and_signatures(dependencies)
        
        error_msg = str(exc_info.value)
        assert "Cannot resolve class for plugin 'plugin1'" in error_msg
        assert exc_info.value.__cause__ is not None

    def test_validate_empty_dependencies(self) -> None:
        """Test validation with empty dependencies."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        dependencies: dict[str, list[Depends]] = {}
        
        # Should not raise any exception
        validator.validate_dependency_versions_and_signatures(dependencies)
        
        # No resolvers should be called
        version_resolver.assert_not_called()
        class_resolver.assert_not_called()

    def test_validate_plugin_with_no_dependencies(self) -> None:
        """Test validation of plugin with no dependencies."""
        version_resolver = Mock()
        class_resolver = Mock(return_value=TestPluginNoDependencies)
        validator = DependencyValidator(version_resolver, class_resolver)
        
        dependencies: dict[str, list[Depends]] = {
            "plugin1": []  # No dependencies
        }
        
        # Should not raise any exception
        validator.validate_dependency_versions_and_signatures(dependencies)
        
        class_resolver.assert_called_once_with("plugin1")
        version_resolver.assert_not_called()


class TestValidateVersionConstraint:
    """Test _validate_version_constraint method."""

    def test_validate_version_constraint_valid(self) -> None:
        """Test validation with valid version constraint."""
        version_resolver = Mock(return_value="1.5.0")
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        # Should not raise any exception
        validator._validate_version_constraint("plugin1", "dependency1", ">=1.0,<2.0")
        
        version_resolver.assert_called_once_with("dependency1")

    def test_validate_version_constraint_version_conflict(self) -> None:
        """Test validation with version conflict."""
        version_resolver = Mock(return_value="2.5.0")  # Version too high
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        with pytest.raises(DependencyVersionConflictError) as exc_info:
            validator._validate_version_constraint("plugin1", "dependency1", ">=1.0,<2.0")
        
        error_msg = str(exc_info.value)
        assert "plugin1" in error_msg
        assert "dependency1" in error_msg
        assert ">=1.0,<2.0" in error_msg
        assert "2.5.0" in error_msg

    def test_validate_version_constraint_version_resolver_error(self) -> None:
        """Test validation when version resolver fails."""
        version_resolver = Mock(side_effect=RuntimeError("Version not found"))
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        with pytest.raises(DependencyError) as exc_info:
            validator._validate_version_constraint("plugin1", "dependency1", ">=1.0")
        
        error_msg = str(exc_info.value)
        assert "Cannot resolve version for plugin 'dependency1'" in error_msg
        assert exc_info.value.__cause__ is not None

    def test_validate_version_constraint_invalid_actual_version(self) -> None:
        """Test validation with invalid actual version."""
        version_resolver = Mock(return_value="invalid.version")
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        with pytest.raises(PluginRegistrationError) as exc_info:
            validator._validate_version_constraint("plugin1", "dependency1", ">=1.0")
        
        error_msg = str(exc_info.value)
        assert "dependency1" in error_msg
        assert "invalid version" in error_msg
        assert "invalid.version" in error_msg

    def test_validate_version_constraint_invalid_constraint(self) -> None:
        """Test validation with invalid version constraint."""
        version_resolver = Mock(return_value="1.0.0")
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        with pytest.raises(ConfigurationError) as exc_info:
            validator._validate_version_constraint("plugin1", "dependency1", "invalid constraint")
        
        error_msg = str(exc_info.value)
        assert "plugin1" in error_msg
        assert "invalid version constraint" in error_msg
        assert "invalid constraint" in error_msg
        assert "dependency1" in error_msg

    def test_validate_version_constraint_various_valid_constraints(self) -> None:
        """Test validation with various valid version constraints."""
        version_resolver = Mock(return_value="1.5.0")
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)
        
        valid_constraints = [
            ">=1.0",
            ">=1.0,<2.0",
            "==1.5.0",
            "!=1.4.0",
            "~=1.5",
            ">1.0,<=2.0",
        ]
        
        for constraint in valid_constraints:
            # Should not raise any exception
            validator._validate_version_constraint("plugin1", "dependency1", constraint)

    def test_validate_version_constraint_edge_cases(self) -> None:
        """Test validation with edge case versions."""
        class_resolver = Mock()
        
        # Test with pre-release version
        version_resolver = Mock(return_value="1.0.0a1")
        validator = DependencyValidator(version_resolver, class_resolver)
        validator._validate_version_constraint("plugin1", "dependency1", ">=1.0.0a1")
        
        # Test with development version
        version_resolver = Mock(return_value="1.0.0.dev1")
        validator = DependencyValidator(version_resolver, class_resolver)
        validator._validate_version_constraint("plugin1", "dependency1", ">=1.0.0.dev1")


class TestValidateInjectionSignature:
    """Test _validate_injection_signature method."""

    def test_validate_injection_signature_valid(self) -> None:
        """Test validation with valid injection signature."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        depends_list = [
            Depends("dependency1"),
            Depends("dependency2"),
        ]

        # Should not raise any exception
        validator._validate_injection_signature("plugin1", TestPluginWithDependencies, depends_list)

    def test_validate_injection_signature_with_optional_params(self) -> None:
        """Test validation with optional parameters."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        depends_list = [
            Depends("required_dep"),
            # optional_dep is not declared - should be fine since it has default
        ]

        # Should not raise any exception
        validator._validate_injection_signature("plugin1", TestPluginWithOptionalDependencies, depends_list)

    def test_validate_injection_signature_with_kwargs(self) -> None:
        """Test validation with **kwargs parameter."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        depends_list: list[Depends] = []

        # Should not raise any exception - **kwargs should be ignored
        validator._validate_injection_signature("plugin1", TestPluginWithKwargs, depends_list)

    def test_validate_injection_signature_with_kwargs_and_params(self) -> None:
        """Test validation with **kwargs and regular parameters."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        depends_list = [
            Depends("required_param"),
        ]

        # Should not raise any exception - **kwargs should be ignored, required_param should be satisfied
        validator._validate_injection_signature("plugin1", TestPluginWithKwargsAndParams, depends_list)

    def test_validate_injection_signature_no_dependencies(self) -> None:
        """Test validation with no dependencies."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        depends_list: list[Depends] = []

        # Should not raise any exception
        validator._validate_injection_signature("plugin1", TestPluginNoDependencies, depends_list)

    def test_validate_injection_signature_missing_parameter(self) -> None:
        """Test validation when declared dependency has no corresponding parameter."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        depends_list = [
            Depends("dependency1"),
            Depends("dependency2"),
            Depends("nonexistent_param"),  # This parameter doesn't exist in __init__
        ]

        with pytest.raises(DependencyError) as exc_info:
            validator._validate_injection_signature("plugin1", TestPluginWithDependencies, depends_list)

        error_msg = str(exc_info.value)
        assert "plugin1" in error_msg
        assert "nonexistent_param" in error_msg
        assert "no parameter" in error_msg

    def test_validate_injection_signature_missing_dependency(self) -> None:
        """Test validation when required parameter has no dependency."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        depends_list = [
            Depends("dependency1"),
            # dependency2 is missing but required
        ]

        with pytest.raises(DependencyError) as exc_info:
            validator._validate_injection_signature("plugin1", TestPluginWithDependencies, depends_list)

        error_msg = str(exc_info.value)
        assert "plugin1" in error_msg
        assert "dependency2" in error_msg
        assert "requires parameter" in error_msg
        assert "no dependency is declared" in error_msg

    def test_validate_injection_signature_inspect_error(self) -> None:
        """Test validation when signature inspection fails."""
        version_resolver = Mock()
        class_resolver = Mock()
        validator = DependencyValidator(version_resolver, class_resolver)

        # Create a mock class that raises an exception during signature inspection
        class BadPlugin:
            pass

        # Mock inspect.signature to raise an exception
        original_signature = inspect.signature

        def mock_signature(obj: object) -> object:
            if obj is BadPlugin.__init__:
                raise RuntimeError("Cannot inspect signature")
            return original_signature(obj)  # type: ignore[arg-type]

        import inspect as inspect_module
        inspect_module.signature = mock_signature  # type: ignore[assignment]

        try:
            with pytest.raises(DependencyError) as exc_info:
                validator._validate_injection_signature("plugin1", BadPlugin, [])

            error_msg = str(exc_info.value)
            assert "Cannot inspect __init__ signature for plugin 'plugin1'" in error_msg
            assert exc_info.value.__cause__ is not None
        finally:
            # Restore original function
            inspect_module.signature = original_signature


class TestDependencyValidatorIntegration:
    """Test integration scenarios for DependencyValidator."""

    def test_complete_validation_workflow(self) -> None:
        """Test complete validation workflow with graph and dependencies."""
        version_resolver = Mock(return_value="1.5.0")
        class_resolver = Mock(return_value=TestPluginWithDependencies)
        validator = DependencyValidator(version_resolver, class_resolver)

        # Create dependency graph
        graph: DependencyGraph[str] = DependencyGraph()
        graph.add_node("plugin1")
        graph.add_node("dependency1")
        graph.add_node("dependency2")
        graph.add_dependency_edge("dependency1", "plugin1")
        graph.add_dependency_edge("dependency2", "plugin1")

        registered_plugins = {"plugin1", "dependency1", "dependency2"}

        # Validate graph structure
        validator.validate_graph_structure(graph, registered_plugins)

        # Validate versions and signatures
        dependencies = {
            "plugin1": [
                Depends("dependency1", version_constraint=">=1.0,<2.0"),
                Depends("dependency2", version_constraint=">=1.0"),
            ]
        }

        validator.validate_dependency_versions_and_signatures(dependencies)

        # Verify all resolvers were called appropriately
        assert version_resolver.call_count == 2
        class_resolver.assert_called_once_with("plugin1")

    def test_validation_with_complex_dependency_tree(self) -> None:
        """Test validation with complex dependency relationships."""
        version_resolver = Mock(return_value="2.1.0")
        class_resolver = Mock(return_value=TestPluginWithOptionalDependencies)
        validator = DependencyValidator(version_resolver, class_resolver)

        # Create complex graph
        graph: DependencyGraph[str] = DependencyGraph()
        nodes = ["app", "auth", "database", "cache", "logging"]
        for node in nodes:
            graph.add_node(node)

        # Create dependency relationships
        graph.add_dependency_edge("auth", "app")
        graph.add_dependency_edge("database", "app")
        graph.add_dependency_edge("database", "auth")
        graph.add_dependency_edge("cache", "auth")
        graph.add_dependency_edge("logging", "database")

        registered_plugins = set(nodes)

        # Validate graph structure
        validator.validate_graph_structure(graph, registered_plugins)

        # Validate versions and signatures for multiple plugins
        dependencies = {
            "app": [Depends("required_dep")],
            "auth": [Depends("required_dep")],
            "database": [Depends("required_dep")],
        }

        validator.validate_dependency_versions_and_signatures(dependencies)

    def test_validation_error_propagation(self) -> None:
        """Test that validation errors are properly propagated."""
        version_resolver = Mock(return_value="0.5.0")  # Version too low
        class_resolver = Mock(return_value=TestPluginWithDependencies)
        validator = DependencyValidator(version_resolver, class_resolver)

        dependencies = {
            "plugin1": [
                Depends("dependency1", version_constraint=">=1.0,<2.0"),  # Will fail
                Depends("dependency2"),
            ]
        }

        with pytest.raises(DependencyVersionConflictError):
            validator.validate_dependency_versions_and_signatures(dependencies)

    def test_validation_with_mixed_constraint_types(self) -> None:
        """Test validation with mixed version constraint types."""
        def version_resolver(plugin_name: str) -> str:
            versions = {
                "dep1": "1.5.0",
                "dep2": "2.0.0",
                "dep3": "1.0.0a1",
            }
            return versions.get(plugin_name, "1.0.0")

        class_resolver = Mock(return_value=TestPluginWithMixedDeps)
        validator = DependencyValidator(version_resolver, class_resolver)

        dependencies = {
            "plugin1": [
                Depends("dep1", version_constraint=">=1.0,<2.0"),
                Depends("dep2", version_constraint="==2.0.0"),
                Depends("dep3", version_constraint=">=1.0.0a1"),
            ]
        }

        # Should not raise any exception
        validator.validate_dependency_versions_and_signatures(dependencies)

    def test_validation_edge_cases(self) -> None:
        """Test validation with various edge cases."""
        version_resolver = Mock(return_value="1.0.0")
        class_resolver = Mock(return_value=TestPluginNoDependencies)
        validator = DependencyValidator(version_resolver, class_resolver)

        # Empty graph with empty dependencies
        graph: DependencyGraph[str] = DependencyGraph()
        validator.validate_graph_structure(graph, set())

        dependencies: dict[str, list[Depends]] = {}
        validator.validate_dependency_versions_and_signatures(dependencies)

        # Single plugin with no dependencies
        graph.add_node("solo_plugin")
        validator.validate_graph_structure(graph, {"solo_plugin"})

        dependencies = {"solo_plugin": []}
        validator.validate_dependency_versions_and_signatures(dependencies)
