# tests/unit/internal/validation/test_plugin_validation.py

"""
Unit tests for plugginger._internal.validation.plugin_validation module.

Tests the ValidationConfig, ValidationProfiles, and validation functions
for plugin method signatures.
"""

from __future__ import annotations

import inspect
from typing import Any
from unittest.mock import Mock

import pytest

# Import the actual module to test for proper coverage
import importlib.util
import sys
from pathlib import Path

# Load the plugin_validation module directly from file
plugin_validation_path = Path(__file__).parent.parent.parent.parent.parent / "src" / "plugginger" / "_internal" / "validation" / "plugin_validation.py"
spec = importlib.util.spec_from_file_location("plugin_validation", plugin_validation_path)
if spec is None or spec.loader is None:
    raise ImportError("Could not load plugin_validation module")

plugin_validation_module = importlib.util.module_from_spec(spec)
sys.modules["plugin_validation"] = plugin_validation_module
spec.loader.exec_module(plugin_validation_module)

ValidationConfig = plugin_validation_module.ValidationConfig
ValidationProfiles = plugin_validation_module.ValidationProfiles
validate_method_signature = plugin_validation_module.validate_method_signature
validate_service_method = plugin_validation_module.validate_service_method
validate_event_handler = plugin_validation_module.validate_event_handler
validate_setup_method = plugin_validation_module.validate_setup_method
validate_teardown_method = plugin_validation_module.validate_teardown_method
validate_plugin_constructor = plugin_validation_module.validate_plugin_constructor
_validate_parameter_count = plugin_validation_module._validate_parameter_count
_validate_parameter_types = plugin_validation_module._validate_parameter_types
_validate_return_annotation = plugin_validation_module._validate_return_annotation

# Import after dynamic loading to avoid import order issues
from plugginger.core.exceptions import ValidationError  # noqa: E402


# Test method classes for validation testing
class TestMethods:
    """Test methods for validation testing."""

    async def valid_service_method(self, param: str) -> str:
        """Valid service method."""
        return param

    def sync_method(self, param: str) -> str:
        """Synchronous method."""
        return param

    async def no_self_method(param: str) -> str:  # type: ignore[misc]
        """Method without self parameter."""
        return param

    async def no_params_method(self) -> None:
        """Method with only self parameter."""
        pass

    async def too_many_params_method(self, p1: str, p2: str, p3: str, p4: str) -> None:
        """Method with too many parameters."""
        pass

    async def varargs_method(self, *args: Any) -> None:
        """Method with *args."""
        pass

    async def kwargs_method(self, **kwargs: Any) -> None:
        """Method with **kwargs."""
        pass

    async def no_annotations_method(self, param) -> None:  # type: ignore[no-untyped-def]
        """Method without parameter annotations."""
        pass

    async def no_return_annotation_method(self, param: str):  # type: ignore[no-untyped-def]
        """Method without return annotation."""
        return param

    async def wrong_return_type_method(self, param: str) -> int:
        """Method with wrong return type."""
        return 42

    async def valid_event_handler(self, event_data: dict[str, Any]) -> None:
        """Valid event handler with 2 parameters."""
        pass

    async def valid_event_handler_with_type(self, event_data: dict[str, Any], matched_event_type: str) -> None:
        """Valid event handler with 3 parameters."""
        pass

    async def valid_setup_method(self) -> None:
        """Valid setup method with only self."""
        pass

    async def valid_setup_method_with_config(self, config: dict[str, Any]) -> None:
        """Valid setup method with config parameter."""
        pass

    async def valid_teardown_method(self) -> None:
        """Valid teardown method."""
        pass

    def __init__(self, app: object, **injected_dependencies: object) -> None:
        """Valid plugin constructor."""
        self.app = app
        self.injected_dependencies = injected_dependencies


class TestValidationConfig:
    """Test ValidationConfig dataclass."""

    def test_validation_config_defaults(self) -> None:
        """Test ValidationConfig default values."""
        config = ValidationConfig()
        
        assert config.require_async is True
        assert config.require_self is True
        assert config.min_params == 1
        assert config.max_params is None
        assert config.allow_varargs is False
        assert config.allow_kwargs is False
        assert config.require_return_annotation is True
        assert config.allowed_return_types is None
        assert config.require_param_annotations is True
        assert config.context_name == "method"

    def test_validation_config_custom_values(self) -> None:
        """Test ValidationConfig with custom values."""
        config = ValidationConfig(
            require_async=False,
            require_self=False,
            min_params=2,
            max_params=5,
            allow_varargs=True,
            allow_kwargs=True,
            require_return_annotation=False,
            allowed_return_types={str, int},
            require_param_annotations=False,
            context_name="custom method"
        )
        
        assert config.require_async is False
        assert config.require_self is False
        assert config.min_params == 2
        assert config.max_params == 5
        assert config.allow_varargs is True
        assert config.allow_kwargs is True
        assert config.require_return_annotation is False
        assert config.allowed_return_types == {str, int}
        assert config.require_param_annotations is False
        assert config.context_name == "custom method"

    def test_validation_config_partial_override(self) -> None:
        """Test ValidationConfig with partial value override."""
        config = ValidationConfig(
            min_params=3,
            context_name="special method"
        )
        
        # Overridden values
        assert config.min_params == 3
        assert config.context_name == "special method"
        
        # Default values
        assert config.require_async is True
        assert config.require_self is True
        assert config.max_params is None


class TestValidationProfiles:
    """Test ValidationProfiles predefined configurations."""

    def test_service_method_profile(self) -> None:
        """Test SERVICE_METHOD profile configuration."""
        profile = ValidationProfiles.SERVICE_METHOD
        
        assert profile.require_async is True
        assert profile.require_self is True
        assert profile.min_params == 1
        assert profile.max_params is None
        assert profile.allow_varargs is False
        assert profile.allow_kwargs is False
        assert profile.require_return_annotation is True
        assert profile.allowed_return_types is None
        assert profile.require_param_annotations is True
        assert profile.context_name == "service method"

    def test_event_handler_profile(self) -> None:
        """Test EVENT_HANDLER profile configuration."""
        profile = ValidationProfiles.EVENT_HANDLER
        
        assert profile.require_async is True
        assert profile.require_self is True
        assert profile.min_params == 2
        assert profile.max_params == 3
        assert profile.allow_varargs is False
        assert profile.allow_kwargs is False
        assert profile.require_return_annotation is False
        assert profile.allowed_return_types is None
        assert profile.require_param_annotations is True
        assert profile.context_name == "event handler"

    def test_setup_method_profile(self) -> None:
        """Test SETUP_METHOD profile configuration."""
        profile = ValidationProfiles.SETUP_METHOD
        
        assert profile.require_async is True
        assert profile.require_self is True
        assert profile.min_params == 1
        assert profile.max_params == 2
        assert profile.allow_varargs is False
        assert profile.allow_kwargs is False
        assert profile.require_return_annotation is False
        assert profile.allowed_return_types is None
        assert profile.require_param_annotations is False
        assert profile.context_name == "setup method"

    def test_teardown_method_profile(self) -> None:
        """Test TEARDOWN_METHOD profile configuration."""
        profile = ValidationProfiles.TEARDOWN_METHOD
        
        assert profile.require_async is True
        assert profile.require_self is True
        assert profile.min_params == 1
        assert profile.max_params == 1
        assert profile.allow_varargs is False
        assert profile.allow_kwargs is False
        assert profile.require_return_annotation is False
        assert profile.allowed_return_types is None
        assert profile.require_param_annotations is False
        assert profile.context_name == "teardown method"

    def test_plugin_constructor_profile(self) -> None:
        """Test PLUGIN_CONSTRUCTOR profile configuration."""
        profile = ValidationProfiles.PLUGIN_CONSTRUCTOR
        
        assert profile.require_async is False
        assert profile.require_self is True
        assert profile.min_params == 1
        assert profile.max_params is None
        assert profile.allow_varargs is False
        assert profile.allow_kwargs is True
        assert profile.require_return_annotation is False
        assert profile.allowed_return_types is None
        assert profile.require_param_annotations is True
        assert profile.context_name == "plugin constructor"


class TestValidateMethodSignature:
    """Test validate_method_signature function."""

    def test_validate_method_signature_valid_async(self) -> None:
        """Test validation of valid async method."""
        config = ValidationConfig(
            require_async=True,
            require_self=True,
            min_params=1,
            require_return_annotation=True,
            require_param_annotations=True
        )
        
        # Should not raise any exception
        validate_method_signature(TestMethods.valid_service_method, config)

    def test_validate_method_signature_sync_when_async_required(self) -> None:
        """Test validation fails for sync method when async required."""
        config = ValidationConfig(require_async=True)
        
        with pytest.raises(ValidationError) as exc_info:
            validate_method_signature(TestMethods.sync_method, config)
        
        assert "must be async" in str(exc_info.value)
        assert "sync_method" in str(exc_info.value)

    def test_validate_method_signature_async_when_sync_allowed(self) -> None:
        """Test validation passes for async method when sync allowed."""
        config = ValidationConfig(require_async=False)
        
        # Should not raise any exception
        validate_method_signature(TestMethods.valid_service_method, config)

    def test_validate_method_signature_sync_when_sync_allowed(self) -> None:
        """Test validation passes for sync method when sync allowed."""
        config = ValidationConfig(require_async=False, require_return_annotation=False)
        
        # Should not raise any exception
        validate_method_signature(TestMethods.sync_method, config)

    def test_validate_method_signature_inspection_error(self) -> None:
        """Test validation handles signature inspection errors."""
        # Use a config that doesn't require async, so we get to signature inspection
        config = ValidationConfig(require_async=False)

        # Create an async callable that will pass the async check but fail signature inspection
        async def async_callable() -> None:
            pass

        # Mock inspect.signature to raise an exception for this specific callable
        original_signature = inspect.signature

        def mock_signature(obj: object) -> object:
            if obj is async_callable:
                raise ValueError("Cannot inspect signature")
            return original_signature(obj)  # type: ignore[arg-type]

        import inspect as inspect_module
        inspect_module.signature = mock_signature  # type: ignore[assignment]

        try:
            with pytest.raises(ValidationError) as exc_info:
                validate_method_signature(async_callable, config)

            assert "Cannot inspect signature" in str(exc_info.value)
            assert "async_callable" in str(exc_info.value)
            assert exc_info.value.__cause__ is not None
        finally:
            # Restore original function
            inspect_module.signature = original_signature


class TestValidateParameterCount:
    """Test _validate_parameter_count helper function."""

    def test_validate_parameter_count_valid_min(self) -> None:
        """Test validation passes when parameter count meets minimum."""
        config = ValidationConfig(min_params=2, max_params=None)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD),
        ]

        # Should not raise any exception
        _validate_parameter_count("test_method", params, config)

    def test_validate_parameter_count_valid_max(self) -> None:
        """Test validation passes when parameter count meets maximum."""
        config = ValidationConfig(min_params=1, max_params=2)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD),
        ]

        # Should not raise any exception
        _validate_parameter_count("test_method", params, config)

    def test_validate_parameter_count_too_few(self) -> None:
        """Test validation fails when too few parameters."""
        config = ValidationConfig(min_params=3)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD),
        ]

        with pytest.raises(ValidationError) as exc_info:
            _validate_parameter_count("test_method", params, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "at least 3 parameters" in error_msg
        assert "got 2" in error_msg

    def test_validate_parameter_count_too_many(self) -> None:
        """Test validation fails when too many parameters."""
        config = ValidationConfig(min_params=1, max_params=2)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param2", inspect.Parameter.POSITIONAL_OR_KEYWORD),
        ]

        with pytest.raises(ValidationError) as exc_info:
            _validate_parameter_count("test_method", params, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "at most 2 parameters" in error_msg
        assert "got 3" in error_msg

    def test_validate_parameter_count_no_max_limit(self) -> None:
        """Test validation passes when no maximum limit is set."""
        config = ValidationConfig(min_params=1, max_params=None)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param2", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param3", inspect.Parameter.POSITIONAL_OR_KEYWORD),
        ]

        # Should not raise any exception
        _validate_parameter_count("test_method", params, config)

    def test_validate_parameter_count_zero_params(self) -> None:
        """Test validation with zero parameters."""
        config = ValidationConfig(min_params=0, max_params=0)
        params: list[inspect.Parameter] = []

        # Should not raise any exception
        _validate_parameter_count("test_method", params, config)

    def test_validate_parameter_count_context_name_in_error(self) -> None:
        """Test that context name appears in error messages."""
        config = ValidationConfig(min_params=2, context_name="special method")
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
        ]

        with pytest.raises(ValidationError) as exc_info:
            _validate_parameter_count("test_method", params, config)

        error_msg = str(exc_info.value)
        assert "Special method" in error_msg  # Capitalized context name


class TestValidateParameterTypes:
    """Test _validate_parameter_types helper function."""

    def test_validate_parameter_types_valid(self) -> None:
        """Test validation passes for valid parameter types."""
        config = ValidationConfig(
            allow_varargs=False,
            allow_kwargs=False,
            require_param_annotations=True
        )
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD, annotation=str),
        ]

        # Should not raise any exception
        _validate_parameter_types("test_method", params, config)

    def test_validate_parameter_types_varargs_not_allowed(self) -> None:
        """Test validation fails when *args not allowed."""
        config = ValidationConfig(allow_varargs=False)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("args", inspect.Parameter.VAR_POSITIONAL),
        ]

        with pytest.raises(ValidationError) as exc_info:
            _validate_parameter_types("test_method", params, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "cannot use *args parameters" in error_msg

    def test_validate_parameter_types_varargs_allowed(self) -> None:
        """Test validation passes when *args allowed."""
        config = ValidationConfig(allow_varargs=True, require_param_annotations=False)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("args", inspect.Parameter.VAR_POSITIONAL),
        ]

        # Should not raise any exception
        _validate_parameter_types("test_method", params, config)

    def test_validate_parameter_types_kwargs_not_allowed(self) -> None:
        """Test validation fails when **kwargs not allowed."""
        config = ValidationConfig(allow_kwargs=False)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("kwargs", inspect.Parameter.VAR_KEYWORD),
        ]

        with pytest.raises(ValidationError) as exc_info:
            _validate_parameter_types("test_method", params, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "cannot use **kwargs parameters" in error_msg

    def test_validate_parameter_types_kwargs_allowed(self) -> None:
        """Test validation passes when **kwargs allowed."""
        config = ValidationConfig(allow_kwargs=True, require_param_annotations=False)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("kwargs", inspect.Parameter.VAR_KEYWORD),
        ]

        # Should not raise any exception
        _validate_parameter_types("test_method", params, config)

    def test_validate_parameter_types_missing_annotation(self) -> None:
        """Test validation fails when parameter annotation missing."""
        config = ValidationConfig(require_param_annotations=True)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD, annotation=inspect.Parameter.empty),
        ]

        with pytest.raises(ValidationError) as exc_info:
            _validate_parameter_types("test_method", params, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "param1" in error_msg
        assert "must have a type annotation" in error_msg

    def test_validate_parameter_types_self_annotation_ignored(self) -> None:
        """Test that self parameter annotation is not required."""
        config = ValidationConfig(require_param_annotations=True)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD, annotation=inspect.Parameter.empty),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD, annotation=str),
        ]

        # Should not raise any exception - self annotation is ignored
        _validate_parameter_types("test_method", params, config)

    def test_validate_parameter_types_annotations_not_required(self) -> None:
        """Test validation passes when annotations not required."""
        config = ValidationConfig(require_param_annotations=False)
        params = [
            inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD),
            inspect.Parameter("param1", inspect.Parameter.POSITIONAL_OR_KEYWORD, annotation=inspect.Parameter.empty),
        ]

        # Should not raise any exception
        _validate_parameter_types("test_method", params, config)


class TestValidateReturnAnnotation:
    """Test _validate_return_annotation helper function."""

    def test_validate_return_annotation_not_required(self) -> None:
        """Test validation passes when return annotation not required."""
        config = ValidationConfig(require_return_annotation=False)
        sig = inspect.Signature(return_annotation=inspect.Signature.empty)

        # Should not raise any exception
        _validate_return_annotation("test_method", sig, config)

    def test_validate_return_annotation_present_when_required(self) -> None:
        """Test validation passes when return annotation present and required."""
        config = ValidationConfig(require_return_annotation=True)
        sig = inspect.Signature(return_annotation=str)

        # Should not raise any exception
        _validate_return_annotation("test_method", sig, config)

    def test_validate_return_annotation_missing_when_required(self) -> None:
        """Test validation fails when return annotation missing and required."""
        config = ValidationConfig(require_return_annotation=True)
        sig = inspect.Signature(return_annotation=inspect.Signature.empty)

        with pytest.raises(ValidationError) as exc_info:
            _validate_return_annotation("test_method", sig, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "must have a return type annotation" in error_msg

    def test_validate_return_annotation_allowed_types_valid(self) -> None:
        """Test validation passes when return type is in allowed types."""
        config = ValidationConfig(
            require_return_annotation=True,
            allowed_return_types={str, int}
        )
        sig = inspect.Signature(return_annotation=str)

        # Should not raise any exception
        _validate_return_annotation("test_method", sig, config)

    def test_validate_return_annotation_allowed_types_invalid(self) -> None:
        """Test validation fails when return type not in allowed types."""
        config = ValidationConfig(
            require_return_annotation=True,
            allowed_return_types={str, int}
        )
        sig = inspect.Signature(return_annotation=float)

        with pytest.raises(ValidationError) as exc_info:
            _validate_return_annotation("test_method", sig, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "return type must be one of" in error_msg
        assert "str" in error_msg
        assert "int" in error_msg
        assert "float" in error_msg

    def test_validate_return_annotation_no_allowed_types_restriction(self) -> None:
        """Test validation passes when no allowed types restriction."""
        config = ValidationConfig(
            require_return_annotation=True,
            allowed_return_types=None
        )
        sig = inspect.Signature(return_annotation=float)

        # Should not raise any exception
        _validate_return_annotation("test_method", sig, config)

    def test_validate_return_annotation_complex_type_name(self) -> None:
        """Test validation with complex type that doesn't have __name__."""
        config = ValidationConfig(
            require_return_annotation=True,
            allowed_return_types={str}
        )

        # Create a mock type without __name__ attribute
        class MockType:
            def __str__(self) -> str:
                return "MockType"

        mock_type = MockType()
        sig = inspect.Signature(return_annotation=mock_type)

        with pytest.raises(ValidationError) as exc_info:
            _validate_return_annotation("test_method", sig, config)

        error_msg = str(exc_info.value)
        assert "test_method" in error_msg
        assert "MockType" in error_msg


class TestValidateMethodSignatureIntegration:
    """Test validate_method_signature integration scenarios."""

    def test_validate_method_signature_missing_self(self) -> None:
        """Test validation fails when self parameter missing."""
        config = ValidationConfig(require_self=True)

        with pytest.raises(ValidationError) as exc_info:
            validate_method_signature(TestMethods.no_self_method, config)

        error_msg = str(exc_info.value)
        assert "no_self_method" in error_msg
        assert "first parameter must be 'self'" in error_msg

    def test_validate_method_signature_self_not_required(self) -> None:
        """Test validation passes when self not required."""
        config = ValidationConfig(
            require_self=False,
            require_return_annotation=False,
            require_param_annotations=False
        )

        # Should not raise any exception
        validate_method_signature(TestMethods.no_self_method, config)

    def test_validate_method_signature_varargs_not_allowed(self) -> None:
        """Test validation fails when varargs not allowed."""
        config = ValidationConfig(allow_varargs=False)

        with pytest.raises(ValidationError) as exc_info:
            validate_method_signature(TestMethods.varargs_method, config)

        error_msg = str(exc_info.value)
        assert "varargs_method" in error_msg
        assert "cannot use *args parameters" in error_msg

    def test_validate_method_signature_kwargs_not_allowed(self) -> None:
        """Test validation fails when kwargs not allowed."""
        config = ValidationConfig(allow_kwargs=False)

        with pytest.raises(ValidationError) as exc_info:
            validate_method_signature(TestMethods.kwargs_method, config)

        error_msg = str(exc_info.value)
        assert "kwargs_method" in error_msg
        assert "cannot use **kwargs parameters" in error_msg

    def test_validate_method_signature_no_param_annotations(self) -> None:
        """Test validation fails when parameter annotations required but missing."""
        config = ValidationConfig(require_param_annotations=True)

        with pytest.raises(ValidationError) as exc_info:
            validate_method_signature(TestMethods.no_annotations_method, config)

        error_msg = str(exc_info.value)
        assert "no_annotations_method" in error_msg
        assert "must have a type annotation" in error_msg

    def test_validate_method_signature_no_return_annotation(self) -> None:
        """Test validation fails when return annotation required but missing."""
        config = ValidationConfig(require_return_annotation=True)

        with pytest.raises(ValidationError) as exc_info:
            validate_method_signature(TestMethods.no_return_annotation_method, config)

        error_msg = str(exc_info.value)
        assert "no_return_annotation_method" in error_msg
        assert "must have a return type annotation" in error_msg

    def test_validate_method_signature_wrong_return_type(self) -> None:
        """Test validation fails when return type not in allowed types."""
        config = ValidationConfig(
            require_return_annotation=True,
            allowed_return_types={str}
        )

        with pytest.raises(ValidationError) as exc_info:
            validate_method_signature(TestMethods.wrong_return_type_method, config)

        error_msg = str(exc_info.value)
        assert "wrong_return_type_method" in error_msg
        assert "return type must be one of" in error_msg


class TestConvenienceFunctions:
    """Test convenience validation functions."""

    def test_validate_service_method_valid(self) -> None:
        """Test validate_service_method with valid method."""
        # Should not raise any exception
        validate_service_method(TestMethods.valid_service_method)

    def test_validate_service_method_invalid_sync(self) -> None:
        """Test validate_service_method fails for sync method."""
        with pytest.raises(ValidationError) as exc_info:
            validate_service_method(TestMethods.sync_method)

        assert "must be async" in str(exc_info.value)

    def test_validate_event_handler_valid_two_params(self) -> None:
        """Test validate_event_handler with valid 2-parameter method."""
        # Should not raise any exception
        validate_event_handler(TestMethods.valid_event_handler)

    def test_validate_event_handler_valid_three_params(self) -> None:
        """Test validate_event_handler with valid 3-parameter method."""
        # Should not raise any exception
        validate_event_handler(TestMethods.valid_event_handler_with_type)

    def test_validate_event_handler_invalid_too_few_params(self) -> None:
        """Test validate_event_handler fails with too few parameters."""
        with pytest.raises(ValidationError) as exc_info:
            validate_event_handler(TestMethods.no_params_method)

        assert "at least 2 parameters" in str(exc_info.value)

    def test_validate_event_handler_invalid_too_many_params(self) -> None:
        """Test validate_event_handler fails with too many parameters."""
        with pytest.raises(ValidationError) as exc_info:
            validate_event_handler(TestMethods.too_many_params_method)

        assert "at most 3 parameters" in str(exc_info.value)

    def test_validate_setup_method_valid_no_config(self) -> None:
        """Test validate_setup_method with valid method without config."""
        # Should not raise any exception
        validate_setup_method(TestMethods.valid_setup_method)

    def test_validate_setup_method_valid_with_config(self) -> None:
        """Test validate_setup_method with valid method with config."""
        # Should not raise any exception
        validate_setup_method(TestMethods.valid_setup_method_with_config)

    def test_validate_setup_method_invalid_too_many_params(self) -> None:
        """Test validate_setup_method fails with too many parameters."""
        with pytest.raises(ValidationError) as exc_info:
            validate_setup_method(TestMethods.too_many_params_method)

        assert "at most 2 parameters" in str(exc_info.value)

    def test_validate_teardown_method_valid(self) -> None:
        """Test validate_teardown_method with valid method."""
        # Should not raise any exception
        validate_teardown_method(TestMethods.valid_teardown_method)

    def test_validate_teardown_method_invalid_too_many_params(self) -> None:
        """Test validate_teardown_method fails with too many parameters."""
        with pytest.raises(ValidationError) as exc_info:
            validate_teardown_method(TestMethods.valid_setup_method_with_config)

        assert "at most 1 parameters" in str(exc_info.value)

    def test_validate_plugin_constructor_valid(self) -> None:
        """Test validate_plugin_constructor with valid constructor."""
        # Should not raise any exception
        validate_plugin_constructor(TestMethods.__init__)

    def test_validate_plugin_constructor_invalid_async(self) -> None:
        """Test validate_plugin_constructor with async method (should pass since require_async=False)."""
        # Plugin constructor profile has require_async=False, so async methods should pass
        # This test should actually pass, not fail
        validate_plugin_constructor(TestMethods.valid_service_method)


class TestValidationProfilesIntegration:
    """Test integration of validation profiles with actual methods."""

    def test_service_method_profile_integration(self) -> None:
        """Test SERVICE_METHOD profile with actual method validation."""
        profile = ValidationProfiles.SERVICE_METHOD

        # Valid method should pass
        validate_method_signature(TestMethods.valid_service_method, profile)

        # Invalid methods should fail
        with pytest.raises(ValidationError):
            validate_method_signature(TestMethods.sync_method, profile)

    def test_event_handler_profile_integration(self) -> None:
        """Test EVENT_HANDLER profile with actual method validation."""
        profile = ValidationProfiles.EVENT_HANDLER

        # Valid methods should pass
        validate_method_signature(TestMethods.valid_event_handler, profile)
        validate_method_signature(TestMethods.valid_event_handler_with_type, profile)

        # Invalid method should fail
        with pytest.raises(ValidationError):
            validate_method_signature(TestMethods.no_params_method, profile)

    def test_setup_method_profile_integration(self) -> None:
        """Test SETUP_METHOD profile with actual method validation."""
        profile = ValidationProfiles.SETUP_METHOD

        # Valid methods should pass
        validate_method_signature(TestMethods.valid_setup_method, profile)
        validate_method_signature(TestMethods.valid_setup_method_with_config, profile)

        # Invalid method should fail
        with pytest.raises(ValidationError):
            validate_method_signature(TestMethods.too_many_params_method, profile)

    def test_teardown_method_profile_integration(self) -> None:
        """Test TEARDOWN_METHOD profile with actual method validation."""
        profile = ValidationProfiles.TEARDOWN_METHOD

        # Valid method should pass
        validate_method_signature(TestMethods.valid_teardown_method, profile)

        # Invalid method should fail
        with pytest.raises(ValidationError):
            validate_method_signature(TestMethods.valid_setup_method_with_config, profile)

    def test_plugin_constructor_profile_integration(self) -> None:
        """Test PLUGIN_CONSTRUCTOR profile with actual method validation."""
        profile = ValidationProfiles.PLUGIN_CONSTRUCTOR

        # Valid constructor should pass
        validate_method_signature(TestMethods.__init__, profile)

        # Plugin constructor profile allows async methods (require_async=False)
        # So this should actually pass, not fail
        validate_method_signature(TestMethods.valid_service_method, profile)


class TestEdgeCases:
    """Test edge cases and error conditions."""

    def test_method_without_name_attribute(self) -> None:
        """Test validation with method that has no __name__ attribute."""
        config = ValidationConfig(
            require_async=False,
            require_return_annotation=False,
            require_self=False,  # Don't require self parameter
            require_param_annotations=False
        )

        # Create a callable without __name__
        class CallableWithoutName:
            def __call__(self, param: str) -> str:
                return param

        callable_obj = CallableWithoutName()

        # Should not raise exception, should use str(method) for name
        validate_method_signature(callable_obj, config)

    def test_validation_with_all_features_enabled(self) -> None:
        """Test validation with all validation features enabled."""
        config = ValidationConfig(
            require_async=True,
            require_self=True,
            min_params=2,
            max_params=3,
            allow_varargs=False,
            allow_kwargs=False,
            require_return_annotation=True,
            allowed_return_types=None,  # Don't restrict return types for this test
            require_param_annotations=True,
            context_name="strict method"
        )

        # Valid method should pass
        validate_method_signature(TestMethods.valid_service_method, config)

        # Invalid methods should fail with appropriate errors
        with pytest.raises(ValidationError):
            validate_method_signature(TestMethods.sync_method, config)

    def test_validation_with_all_features_disabled(self) -> None:
        """Test validation with all validation features disabled."""
        config = ValidationConfig(
            require_async=False,
            require_self=False,
            min_params=0,
            max_params=None,
            allow_varargs=True,
            allow_kwargs=True,
            require_return_annotation=False,
            allowed_return_types=None,
            require_param_annotations=False,
            context_name="permissive method"
        )

        # All methods should pass
        validate_method_signature(TestMethods.sync_method, config)
        validate_method_signature(TestMethods.no_self_method, config)
        validate_method_signature(TestMethods.varargs_method, config)
        validate_method_signature(TestMethods.kwargs_method, config)
