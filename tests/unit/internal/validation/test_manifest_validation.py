"""
Tests for manifest validation functionality.

These tests verify that the ManifestValidator correctly validates
plugin manifests against their implementations.
"""

import pytest
from typing import List

from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import ValidationError
from plugginger.schemas import generate_plugin_manifest
from plugginger._internal.validation.manifest_validation import (
    ManifestValidator,
    validate_plugin_manifest_consistency,
)


# Test plugin classes
@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin for manifest validation."""
    needs: List[Depends] = [Depends("logger")]
    
    @service(name="test_service", description="Test service")
    async def test_service_method(self, value: str) -> str:
        """Test service method."""
        return f"test: {value}"
    
    @on_event("test.event", priority=5)
    async def on_test_event(self, event_data: dict) -> None:
        """Handle test event."""
        pass


@plugin(name="simple_plugin", version="2.0.0")
class SimplePlugin(PluginBase):
    """Simple plugin for manifest validation."""
    
    @service()
    async def simple_method(self) -> bool:
        """Simple method."""
        return True


@plugin(name="mismatch_plugin", version="1.0.0")
class MismatchPlugin(PluginBase):
    """Plugin with mismatched manifest for testing validation errors."""
    
    @service(name="actual_service")
    async def actual_service_method(self, value: str) -> str:
        """Actual service method."""
        return f"actual: {value}"


class TestManifestValidator:
    """Test the ManifestValidator class."""
    
    def test_validator_initialization(self) -> None:
        """Test that ManifestValidator can be initialized."""
        validator = ManifestValidator()
        assert validator is not None
    
    def test_validate_plugin_manifest_consistency_success(self) -> None:
        """Test successful manifest validation."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(TestPlugin)
        
        # Should not raise any exception
        validator.validate_plugin_manifest_consistency(TestPlugin, manifest)
    
    def test_validate_simple_plugin_manifest_consistency(self) -> None:
        """Test manifest validation for simple plugin."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(SimplePlugin)
        
        # Should not raise any exception
        validator.validate_plugin_manifest_consistency(SimplePlugin, manifest)
    
    def test_validate_plugin_name_mismatch(self) -> None:
        """Test validation fails when plugin name doesn't match."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(TestPlugin)
        
        # Modify manifest to have wrong name
        manifest.metadata.name = "wrong_name"
        
        with pytest.raises(ValidationError, match="Plugin name mismatch"):
            validator.validate_plugin_manifest_consistency(TestPlugin, manifest)
    
    def test_validate_plugin_version_mismatch(self) -> None:
        """Test validation fails when plugin version doesn't match."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(TestPlugin)
        
        # Modify manifest to have wrong version
        manifest.metadata.version = "2.0.0"
        
        with pytest.raises(ValidationError, match="Plugin version mismatch"):
            validator.validate_plugin_manifest_consistency(TestPlugin, manifest)
    
    def test_validate_missing_service_in_manifest(self) -> None:
        """Test validation fails when service is missing from manifest."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(TestPlugin)
        
        # Remove service from manifest
        manifest.services = []
        
        with pytest.raises(ValidationError, match="is implemented in plugin but not declared in manifest"):
            validator.validate_plugin_manifest_consistency(TestPlugin, manifest)
    
    def test_validate_extra_service_in_manifest(self) -> None:
        """Test validation fails when manifest declares non-existent service."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(SimplePlugin)
        
        # Add extra service to manifest
        from plugginger.schemas.manifest import ServiceInfo
        extra_service = ServiceInfo(
            name="non_existent_service",
            description="Service that doesn't exist",
            method_name="non_existent_method",
            signature="async def non_existent_method(self) -> str",
            parameters=[],
            return_annotation="str"
        )
        manifest.services.append(extra_service)
        
        with pytest.raises(ValidationError, match="is declared in manifest but not implemented in plugin"):
            validator.validate_plugin_manifest_consistency(SimplePlugin, manifest)
    
    def test_validate_missing_event_listener_in_manifest(self) -> None:
        """Test validation fails when event listener is missing from manifest."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(TestPlugin)
        
        # Remove event listeners from manifest
        manifest.event_listeners = []
        
        with pytest.raises(ValidationError, match="is implemented in plugin but not declared in manifest"):
            validator.validate_plugin_manifest_consistency(TestPlugin, manifest)
    
    def test_validate_extra_event_listener_in_manifest(self) -> None:
        """Test validation fails when manifest declares non-existent event listener."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(SimplePlugin)
        
        # Add extra event listener to manifest
        from plugginger.schemas.manifest import EventListenerInfo
        extra_listener = EventListenerInfo(
            patterns=["non.existent.event"],
            method_name="non_existent_handler",
            priority=1,
            description="Handler that doesn't exist",
            signature="async def non_existent_handler(self, event_data: dict) -> None"
        )
        manifest.event_listeners.append(extra_listener)
        
        with pytest.raises(ValidationError, match="is declared in manifest but not implemented in plugin"):
            validator.validate_plugin_manifest_consistency(SimplePlugin, manifest)
    
    def test_validate_missing_dependency_in_manifest(self) -> None:
        """Test validation fails when dependency is missing from manifest."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(TestPlugin)
        
        # Remove dependencies from manifest
        manifest.dependencies = []
        
        with pytest.raises(ValidationError, match="is declared in plugin code but not in manifest"):
            validator.validate_plugin_manifest_consistency(TestPlugin, manifest)
    
    def test_validate_extra_dependency_in_manifest(self) -> None:
        """Test validation fails when manifest declares non-existent dependency."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(SimplePlugin)
        
        # Add extra dependency to manifest
        from plugginger.schemas.manifest import DependencyInfo
        extra_dep = DependencyInfo(
            name="non_existent_plugin",
            version=">=1.0.0",
            optional=False
        )
        manifest.dependencies.append(extra_dep)
        
        with pytest.raises(ValidationError, match="is declared in manifest but not in plugin code"):
            validator.validate_plugin_manifest_consistency(SimplePlugin, manifest)


class TestManifestValidationConvenienceFunction:
    """Test the convenience function for manifest validation."""
    
    def test_validate_plugin_manifest_consistency_function(self) -> None:
        """Test the convenience function works correctly."""
        manifest = generate_plugin_manifest(TestPlugin)
        
        # Should not raise any exception
        validate_plugin_manifest_consistency(TestPlugin, manifest)
    
    def test_validate_plugin_manifest_consistency_function_error(self) -> None:
        """Test the convenience function raises errors correctly."""
        manifest = generate_plugin_manifest(TestPlugin)
        manifest.metadata.name = "wrong_name"
        
        with pytest.raises(ValidationError, match="Plugin name mismatch"):
            validate_plugin_manifest_consistency(TestPlugin, manifest)


class TestManifestValidationEdgeCases:
    """Test edge cases in manifest validation."""
    
    def test_validate_plugin_without_services(self) -> None:
        """Test validation of plugin without any services."""
        @plugin(name="no_services_plugin", version="1.0.0")
        class NoServicesPlugin(PluginBase):
            """Plugin without services."""
            pass
        
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(NoServicesPlugin)
        
        # Should not raise any exception
        validator.validate_plugin_manifest_consistency(NoServicesPlugin, manifest)
    
    def test_validate_plugin_without_event_listeners(self) -> None:
        """Test validation of plugin without any event listeners."""
        @plugin(name="no_listeners_plugin", version="1.0.0")
        class NoListenersPlugin(PluginBase):
            """Plugin without event listeners."""
            
            @service()
            async def simple_service(self) -> str:
                """Simple service."""
                return "test"
        
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(NoListenersPlugin)
        
        # Should not raise any exception
        validator.validate_plugin_manifest_consistency(NoListenersPlugin, manifest)
    
    def test_validate_plugin_without_dependencies(self) -> None:
        """Test validation of plugin without any dependencies."""
        validator = ManifestValidator()
        manifest = generate_plugin_manifest(SimplePlugin)
        
        # Should not raise any exception (SimplePlugin has no dependencies)
        validator.validate_plugin_manifest_consistency(SimplePlugin, manifest)
