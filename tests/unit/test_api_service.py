# tests/unit/test_api_service.py

"""
Unit tests for plugginger.api.service module.

Tests the @service decorator, service metadata handling, and utility functions
for service registration and discovery.
"""

from __future__ import annotations

import inspect
from typing import Any

import pytest

from plugginger.api.service import (
    extract_service_methods,
    get_service_call_signature,
    get_service_metadata,
    is_service_method,
    service,
    validate_service_method_signature,
)
from plugginger.core.constants import SERVICE_METADATA_KEY
from plugginger.core.exceptions import ServiceDefinitionError, ValidationError


# Test plugin classes with service methods
class TestPlugin:
    """Test plugin class with various service methods."""

    @service(name="get_user", timeout_seconds=5.0, description="Get user by ID")
    async def get_user_by_id(self: Any, user_id: int) -> dict[str, Any]:
        """Get user by ID service."""
        return {"id": user_id, "name": "John"}

    @service()
    async def default_service(self: Any, data: str) -> str:
        """Service with default settings."""
        return f"processed: {data}"

    @service(name="custom_name")
    async def method_with_custom_name(self: Any, value: float) -> float:
        """Service with custom name."""
        return value * 2

    async def non_service_method(self: Any, param: str) -> str:
        """Regular async method without @service decorator."""
        return param

    def sync_method(self: Any, param: str) -> str:
        """Synchronous method."""
        return param

    @property
    def some_property(self) -> str:
        """Property that should be ignored."""
        return "property_value"


class EmptyPlugin:
    """Plugin class without any service methods."""

    def regular_method(self) -> str:
        """Regular method."""
        return "regular"


class TestServiceDecorator:
    """Test @service decorator functionality."""

    def test_service_decorator_basic(self) -> None:
        """Test basic service decorator usage."""
        @service()
        async def test_method(self: Any, param: str) -> str:
            return param

        assert hasattr(test_method, SERVICE_METADATA_KEY)
        metadata = getattr(test_method, SERVICE_METADATA_KEY)

        assert metadata["name"] == "test_method"
        assert metadata["timeout_seconds"] is None
        assert metadata["description"] is None
        assert metadata["method_name"] == "test_method"

    def test_service_decorator_with_all_parameters(self) -> None:
        """Test service decorator with all parameters."""
        @service(name="custom_service", timeout_seconds=10.0, description="Test service")
        async def test_method(self: Any, param: str) -> str:
            return param

        metadata = getattr(test_method, SERVICE_METADATA_KEY)

        assert metadata["name"] == "custom_service"
        assert metadata["timeout_seconds"] == 10.0
        assert metadata["description"] == "Test service"
        assert metadata["method_name"] == "test_method"

    def test_service_decorator_with_custom_name(self) -> None:
        """Test service decorator with custom name only."""
        @service(name="my_service")
        async def test_method(self: Any, param: str) -> str:
            return param

        metadata = getattr(test_method, SERVICE_METADATA_KEY)

        assert metadata["name"] == "my_service"
        assert metadata["timeout_seconds"] is None
        assert metadata["description"] is None

    def test_service_decorator_with_timeout(self) -> None:
        """Test service decorator with timeout only."""
        @service(timeout_seconds=15.5)
        async def test_method(self: Any, param: str) -> str:
            return param

        metadata = getattr(test_method, SERVICE_METADATA_KEY)

        assert metadata["name"] == "test_method"
        assert metadata["timeout_seconds"] == 15.5
        assert metadata["description"] is None

    def test_service_decorator_with_description(self) -> None:
        """Test service decorator with description only."""
        @service(description="A test service")
        async def test_method(self: Any, param: str) -> str:
            return param

        metadata = getattr(test_method, SERVICE_METADATA_KEY)

        assert metadata["name"] == "test_method"
        assert metadata["timeout_seconds"] is None
        assert metadata["description"] == "A test service"

    def test_service_decorator_signature_metadata(self) -> None:
        """Test that service decorator captures signature metadata."""
        @service()
        async def test_method(self: Any, param1: str, param2: int = 42) -> str:
            return f"{param1}:{param2}"

        metadata = getattr(test_method, SERVICE_METADATA_KEY)

        assert "signature" in metadata
        assert "parameters" in metadata
        assert len(metadata["parameters"]) == 2

        # Check first parameter
        param1 = metadata["parameters"][0]
        assert param1["name"] == "param1"
        assert param1["annotation"] == str or param1["annotation"] == "str"
        assert param1["default"] is None
        assert param1["kind"] == "POSITIONAL_OR_KEYWORD"

        # Check second parameter
        param2 = metadata["parameters"][1]
        assert param2["name"] == "param2"
        assert param2["annotation"] == int or param2["annotation"] == "int"
        assert param2["default"] == 42
        assert param2["kind"] == "POSITIONAL_OR_KEYWORD"

    def test_service_decorator_non_async_error(self) -> None:
        """Test that decorator raises error for non-async methods."""
        with pytest.raises(ServiceDefinitionError) as exc_info:
            # Apply decorator to a sync function instead of async
            service_decorator = service()

            def sync_method(self: Any, param: str) -> str:
                return param

            service_decorator(sync_method)  # type: ignore[type-var]

        assert "must be async" in str(exc_info.value)
        assert "sync_method" in str(exc_info.value)

    def test_service_decorator_no_self_parameter_error(self) -> None:
        """Test that decorator raises error for methods without self parameter."""
        with pytest.raises(ServiceDefinitionError) as exc_info:
            @service()
            async def no_self_method(param: str) -> str:
                return param

        assert "must be an instance method" in str(exc_info.value)
        assert "first parameter must be 'self'" in str(exc_info.value)

    def test_service_decorator_empty_name_uses_function_name(self) -> None:
        """Test that decorator uses function name when empty name provided."""
        @service(name="")
        async def test_method(self: Any, param: str) -> str:
            return param

        metadata = getattr(test_method, SERVICE_METADATA_KEY)
        # Empty name should fall back to function name
        assert metadata["name"] == "test_method"

    def test_service_decorator_none_name_uses_function_name(self) -> None:
        """Test that decorator uses function name when None name provided."""
        @service(name=None)
        async def test_method(self: Any, param: str) -> str:
            return param

        metadata = getattr(test_method, SERVICE_METADATA_KEY)
        # None name should fall back to function name
        assert metadata["name"] == "test_method"

    def test_service_decorator_invalid_timeout_error(self) -> None:
        """Test that decorator raises error for invalid timeout values."""
        invalid_timeouts = [0, -1, -5.5]

        for invalid_timeout in invalid_timeouts:
            with pytest.raises(ServiceDefinitionError) as exc_info:
                @service(timeout_seconds=invalid_timeout)
                async def test_method(self: Any, param: str) -> str:
                    return param

            assert "must be a positive number" in str(exc_info.value)

    def test_service_decorator_valid_timeout_values(self) -> None:
        """Test that decorator accepts valid timeout values."""
        valid_timeouts = [1, 5.5, 10.0, 100]

        for valid_timeout in valid_timeouts:
            @service(timeout_seconds=valid_timeout)
            async def test_method(self: Any, param: str) -> str:
                return param

            metadata = getattr(test_method, SERVICE_METADATA_KEY)
            assert metadata["timeout_seconds"] == valid_timeout

    def test_service_decorator_preserves_function(self) -> None:
        """Test that decorator preserves the original function."""
        original_name = "test_function"
        original_doc = "Test function docstring"

        @service()
        async def test_function(self: Any, param: str) -> str:
            """Test function docstring"""
            return param

        assert test_function.__name__ == original_name
        assert test_function.__doc__ == original_doc
        assert inspect.iscoroutinefunction(test_function)

    def test_service_decorator_parameters_without_defaults(self) -> None:
        """Test service decorator with parameters that have no defaults."""
        @service()
        async def test_method(self: Any, param1: str, param2: int) -> str:
            return f"{param1}:{param2}"

        metadata = getattr(test_method, SERVICE_METADATA_KEY)

        for param in metadata["parameters"]:
            assert param["default"] is None

    def test_service_decorator_complex_signature(self) -> None:
        """Test service decorator with complex method signature."""
        @service()
        async def complex_method(
            self: Any,
            required_param: str,
            optional_param: int = 42,
            *args: Any,
            keyword_only: bool = True,
            **kwargs: Any
        ) -> dict[str, Any]:
            return {"result": "complex"}

        metadata = getattr(complex_method, SERVICE_METADATA_KEY)
        assert "parameters" in metadata
        assert len(metadata["parameters"]) > 0


class TestGetServiceMetadata:
    """Test get_service_metadata function."""

    def test_get_service_metadata_valid_service(self) -> None:
        """Test getting metadata from valid service method."""
        plugin = TestPlugin()
        metadata = get_service_metadata(plugin.get_user_by_id)

        assert metadata["name"] == "get_user"
        assert metadata["timeout_seconds"] == 5.0
        assert metadata["description"] == "Get user by ID"
        assert metadata["method_name"] == "get_user_by_id"

    def test_get_service_metadata_default_service(self) -> None:
        """Test getting metadata from service with default settings."""
        plugin = TestPlugin()
        metadata = get_service_metadata(plugin.default_service)

        assert metadata["name"] == "default_service"
        assert metadata["timeout_seconds"] is None
        assert metadata["description"] is None

    def test_get_service_metadata_non_service_error(self) -> None:
        """Test that function raises error for non-service method."""
        plugin = TestPlugin()

        with pytest.raises(ServiceDefinitionError) as exc_info:
            get_service_metadata(plugin.non_service_method)

        assert "is not a valid service" in str(exc_info.value)
        assert "missing @service decorator" in str(exc_info.value)

    def test_get_service_metadata_regular_method_error(self) -> None:
        """Test that function raises error for regular method."""
        plugin = TestPlugin()

        with pytest.raises(ServiceDefinitionError) as exc_info:
            get_service_metadata(plugin.sync_method)

        assert "is not a valid service" in str(exc_info.value)


class TestIsServiceMethod:
    """Test is_service_method function."""

    def test_is_service_method_valid_service(self) -> None:
        """Test that function returns True for valid service methods."""
        plugin = TestPlugin()

        assert is_service_method(plugin.get_user_by_id) is True
        assert is_service_method(plugin.default_service) is True
        assert is_service_method(plugin.method_with_custom_name) is True

    def test_is_service_method_non_service_async(self) -> None:
        """Test that function returns False for async non-service methods."""
        plugin = TestPlugin()

        assert is_service_method(plugin.non_service_method) is False

    def test_is_service_method_sync_method(self) -> None:
        """Test that function returns False for sync methods."""
        plugin = TestPlugin()

        assert is_service_method(plugin.sync_method) is False

    def test_is_service_method_property(self) -> None:
        """Test that function returns False for properties."""
        plugin = TestPlugin()

        # Properties are not callable in the same way
        assert is_service_method(plugin.some_property) is False  # type: ignore[arg-type]

    def test_is_service_method_non_callable(self) -> None:
        """Test that function returns False for non-callable objects."""
        assert is_service_method("not a method") is False  # type: ignore[arg-type]
        assert is_service_method(123) is False  # type: ignore[arg-type]
        assert is_service_method(None) is False  # type: ignore[arg-type]

    def test_is_service_method_class_method(self) -> None:
        """Test with unbound class methods."""
        assert is_service_method(TestPlugin.get_user_by_id) is True
        assert is_service_method(TestPlugin.non_service_method) is False
        assert is_service_method(TestPlugin.sync_method) is False


class TestExtractServiceMethods:
    """Test extract_service_methods function."""

    def test_extract_service_methods_with_services(self) -> None:
        """Test extracting service methods from plugin with services."""
        plugin = TestPlugin()
        services = extract_service_methods(plugin)

        # Should find 3 service methods
        assert len(services) == 3

        # Check service names
        assert "get_user" in services
        assert "default_service" in services
        assert "custom_name" in services

        # Check that methods are bound to the instance
        assert hasattr(services["get_user"], "__self__")
        assert services["get_user"].__self__ is plugin
        assert hasattr(services["default_service"], "__self__")
        assert services["default_service"].__self__ is plugin
        assert hasattr(services["custom_name"], "__self__")
        assert services["custom_name"].__self__ is plugin

    def test_extract_service_methods_empty_plugin(self) -> None:
        """Test extracting service methods from plugin without services."""
        plugin = EmptyPlugin()
        services = extract_service_methods(plugin)

        assert len(services) == 0
        assert services == {}

    def test_extract_service_methods_ignores_private_methods(self) -> None:
        """Test that private methods are ignored."""
        class PluginWithPrivateService:
            @service()
            async def _private_service(self: Any, param: str) -> str:
                return param

            @service()
            async def __magic_service__(self: Any, param: str) -> str:
                return param

            @service()
            async def public_service(self: Any, param: str) -> str:
                return param

        plugin = PluginWithPrivateService()
        services = extract_service_methods(plugin)

        # Should only find the public service
        assert len(services) == 1
        assert "public_service" in services

    def test_extract_service_methods_ignores_non_methods(self) -> None:
        """Test that non-method attributes are ignored."""
        class PluginWithMixedAttributes:
            some_attribute = "value"

            @service()
            async def valid_service(self: Any, param: str) -> str:
                return param

            def regular_method(self: Any, param: str) -> str:
                return param

        plugin = PluginWithMixedAttributes()
        services = extract_service_methods(plugin)

        # Should only find the service method
        assert len(services) == 1
        assert "valid_service" in services

    def test_extract_service_methods_service_name_mapping(self) -> None:
        """Test that services are mapped by their service names, not method names."""
        plugin = TestPlugin()
        services = extract_service_methods(plugin)

        # Method 'method_with_custom_name' should be mapped as 'custom_name'
        assert "custom_name" in services
        assert "method_with_custom_name" not in services

        # Verify it's the correct method
        assert services["custom_name"].__name__ == "method_with_custom_name"

    def test_extract_service_methods_preserves_metadata(self) -> None:
        """Test that extracted methods preserve their metadata."""
        plugin = TestPlugin()
        services = extract_service_methods(plugin)

        # Check that metadata is accessible from extracted methods
        user_service = services["get_user"]
        metadata = get_service_metadata(user_service)

        assert metadata["name"] == "get_user"
        assert metadata["timeout_seconds"] == 5.0
        assert metadata["description"] == "Get user by ID"


class TestValidateServiceMethodSignature:
    """Test validate_service_method_signature function."""

    def test_validate_service_method_signature_valid(self) -> None:
        """Test validation of valid service method."""
        # Test with unbound methods from the class, not bound methods from instance
        # Should not raise any exception
        validate_service_method_signature(TestPlugin.get_user_by_id)
        validate_service_method_signature(TestPlugin.default_service)

    def test_validate_service_method_signature_invalid_sync(self) -> None:
        """Test validation fails for sync method."""
        plugin = TestPlugin()

        with pytest.raises(ServiceDefinitionError) as exc_info:
            validate_service_method_signature(plugin.sync_method)

        # Should convert ValidationError to ServiceDefinitionError
        assert isinstance(exc_info.value, ServiceDefinitionError)
        assert exc_info.value.__cause__ is not None
        assert isinstance(exc_info.value.__cause__, ValidationError)

    def test_validate_service_method_signature_static_method(self) -> None:
        """Test validation with static method."""
        class TestClass:
            @staticmethod
            async def static_service_method(param: str) -> str:
                return param

        with pytest.raises(ServiceDefinitionError):
            validate_service_method_signature(TestClass.static_service_method)

    def test_validate_service_method_signature_class_method(self) -> None:
        """Test validation with class method."""
        class TestClass:
            @classmethod
            async def class_service_method(cls: type[TestClass], param: str) -> str:
                return param

        with pytest.raises(ServiceDefinitionError):
            validate_service_method_signature(TestClass.class_service_method)


class TestGetServiceCallSignature:
    """Test get_service_call_signature function."""

    def test_get_service_call_signature_basic(self) -> None:
        """Test getting call signature for basic service method."""
        # Use unbound method from class to get proper signature
        sig = get_service_call_signature(TestPlugin.get_user_by_id)

        # Should exclude 'self' parameter
        params = list(sig.parameters.values())
        assert len(params) == 1
        assert params[0].name == "user_id"
        assert params[0].annotation == int or str(params[0].annotation) == "int"

    def test_get_service_call_signature_multiple_params(self) -> None:
        """Test getting call signature for method with multiple parameters."""
        @service()
        async def multi_param_service(self: Any, param1: str, param2: int = 42, param3: bool = True) -> str:
            return f"{param1}:{param2}:{param3}"

        sig = get_service_call_signature(multi_param_service)

        params = list(sig.parameters.values())
        assert len(params) == 3

        assert params[0].name == "param1"
        assert params[0].annotation == str or str(params[0].annotation) == "str"
        assert params[0].default == inspect.Parameter.empty

        assert params[1].name == "param2"
        assert params[1].annotation == int or str(params[1].annotation) == "int"
        assert params[1].default == 42

        assert params[2].name == "param3"
        assert params[2].annotation == bool or str(params[2].annotation) == "bool"
        assert params[2].default is True

    def test_get_service_call_signature_no_params(self) -> None:
        """Test getting call signature for method with no parameters except self."""
        @service()
        async def no_param_service(self: Any) -> str:
            return "result"

        sig = get_service_call_signature(no_param_service)

        params = list(sig.parameters.values())
        assert len(params) == 0

    def test_get_service_call_signature_return_annotation(self) -> None:
        """Test that return annotation is preserved."""
        # Use unbound method from class to get proper signature
        sig = get_service_call_signature(TestPlugin.get_user_by_id)

        # Return annotation might be a string representation
        assert sig.return_annotation == dict[str, Any] or str(sig.return_annotation) == "dict[str, Any]"

    def test_get_service_call_signature_complex_params(self) -> None:
        """Test getting call signature for method with complex parameters."""
        @service()
        async def complex_service(
            self: Any,
            required: str,
            optional: int = 10,
            *args: Any,
            keyword_only: bool = False,
            **kwargs: Any
        ) -> dict[str, Any]:
            return {"result": "complex"}

        sig = get_service_call_signature(complex_service)

        params = list(sig.parameters.values())
        assert len(params) == 5  # required, optional, *args, keyword_only, **kwargs

        # Check parameter kinds
        assert params[0].kind == inspect.Parameter.POSITIONAL_OR_KEYWORD
        assert params[1].kind == inspect.Parameter.POSITIONAL_OR_KEYWORD
        assert params[2].kind == inspect.Parameter.VAR_POSITIONAL
        assert params[3].kind == inspect.Parameter.KEYWORD_ONLY
        assert params[4].kind == inspect.Parameter.VAR_KEYWORD


class TestServiceIntegration:
    """Test integration scenarios for service functionality."""

    def test_service_lifecycle_integration(self) -> None:
        """Test complete service lifecycle."""
        class IntegrationPlugin:
            @service(name="process_data", timeout_seconds=30.0, description="Process input data")
            async def process_data_method(self: Any, data: str, multiplier: int = 2) -> str:
                return data * multiplier

        plugin = IntegrationPlugin()

        # Test service detection
        assert is_service_method(plugin.process_data_method)

        # Test metadata extraction
        metadata = get_service_metadata(plugin.process_data_method)
        assert metadata["name"] == "process_data"
        assert metadata["timeout_seconds"] == 30.0
        assert metadata["description"] == "Process input data"

        # Test service extraction
        services = extract_service_methods(plugin)
        assert "process_data" in services
        assert services["process_data"] == plugin.process_data_method

        # Test signature validation (use unbound method from class)
        validate_service_method_signature(IntegrationPlugin.process_data_method)

        # Test call signature (use unbound method from class)
        call_sig = get_service_call_signature(IntegrationPlugin.process_data_method)
        params = list(call_sig.parameters.values())
        assert len(params) == 2
        assert params[0].name == "data"
        assert params[1].name == "multiplier"

    def test_multiple_services_same_plugin(self) -> None:
        """Test plugin with multiple service methods."""
        class MultiServicePlugin:
            @service(name="service_a")
            async def method_a(self: Any, param: str) -> str:
                return f"a:{param}"

            @service(name="service_b", timeout_seconds=5.0)
            async def method_b(self: Any, param: int) -> int:
                return param * 2

            async def non_service_method(self: Any, param: str) -> str:
                return param

        plugin = MultiServicePlugin()
        services = extract_service_methods(plugin)

        assert len(services) == 2
        assert "service_a" in services
        assert "service_b" in services
        assert "non_service_method" not in services

        # Test individual service metadata
        metadata_a = get_service_metadata(services["service_a"])
        metadata_b = get_service_metadata(services["service_b"])

        assert metadata_a["name"] == "service_a"
        assert metadata_a["timeout_seconds"] is None

        assert metadata_b["name"] == "service_b"
        assert metadata_b["timeout_seconds"] == 5.0
