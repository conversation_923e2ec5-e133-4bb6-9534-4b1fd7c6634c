from plugginger.core.config import EventListenerFaultPolicy, LogLevel


def test_event_listener_fault_policy_enum() -> None:
    assert EventListenerFaultPolicy.LOG_AND_CONTINUE == EventListenerFaultPolicy.LOG_AND_CONTINUE
    assert EventListenerFaultPolicy.FAIL_FAST == EventListenerFaultPolicy.FAIL_FAST
    assert EventListenerFaultPolicy.ISOLATE_AND_LOG == EventListenerFaultPolicy.ISOLATE_AND_LOG

    assert EventListenerFaultPolicy.LOG_AND_CONTINUE.value == "log"
    assert EventListenerFaultPolicy.FAIL_FAST.value == "fail"
    assert EventListenerFaultPolicy.ISOLATE_AND_LOG.value == "isolate"

def test_log_level_enum() -> None:
    assert LogLevel.DEBUG == LogLevel.DEBUG
    assert LogLevel.INFO == LogLevel.INFO
    assert LogLevel.WARNING == LogLevel.WARNING
    assert LogLevel.ERROR == LogLevel.ERROR
    assert LogLevel.CRITICAL == LogLevel.CRITICAL

    assert LogLevel.DEBUG.name == "DEBUG"
    assert LogLevel.INFO.name == "INFO"
    assert LogLevel.WARNING.name == "WARNING"
    assert LogLevel.ERROR.name == "ERROR"
    assert LogLevel.CRITICAL.name == "CRITICAL"

    assert LogLevel.DEBUG.value == "DEBUG"
    assert LogLevel.INFO.value == "INFO"
    assert LogLevel.WARNING.value == "WARNING"
    assert LogLevel.ERROR.value == "ERROR"
    assert LogLevel.CRITICAL.value == "CRITICAL"
