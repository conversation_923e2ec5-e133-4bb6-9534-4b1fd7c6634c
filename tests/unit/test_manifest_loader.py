"""
Unit tests for the ManifestLoader class.

These tests verify the functionality of loading and validating YAML manifests
from files during plugin registration.
"""

from pathlib import Path

import pytest

from plugginger._internal.validation.manifest_loader import ManifestLoader, load_plugin_manifest
from plugginger.api.plugin import PluginBase, plugin
from plugginger.core.exceptions import PluginRegistrationError
from plugginger.schemas import PluginManifest


@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin for manifest loader tests."""
    pass


class TestManifestLoader:
    """Test the ManifestLoader class."""

    def test_init_with_default_logger(self) -> None:
        """Test ManifestLoader initialization with default logger."""
        loader = ManifestLoader()
        assert loader._logger is not None

    def test_init_with_custom_logger(self) -> None:
        """Test ManifestLoader initialization with custom logger."""
        import logging
        custom_logger = logging.getLogger("test")
        loader = Manifest<PERSON>oader(custom_logger)
        assert loader._logger is custom_logger

    def test_load_plugin_manifest_no_file_optional(self) -> None:
        """Test loading manifest when no file exists and not required."""
        loader = ManifestLoader()

        result = loader.load_plugin_manifest(
            TestPlugin,
            manifest_path=None,
            require_manifest=False
        )

        assert result is None

    def test_load_plugin_manifest_no_file_required(self) -> None:
        """Test loading manifest when no file exists and required."""
        loader = ManifestLoader()

        with pytest.raises(PluginRegistrationError, match="Required manifest file not found"):
            loader.load_plugin_manifest(
                TestPlugin,
                manifest_path=None,
                require_manifest=True
            )

    def test_load_plugin_manifest_explicit_path_not_found(self) -> None:
        """Test loading manifest with explicit path that doesn't exist."""
        loader = ManifestLoader()

        with pytest.raises(PluginRegistrationError, match="Required manifest file not found"):
            loader.load_plugin_manifest(
                TestPlugin,
                manifest_path="/nonexistent/manifest.yaml",
                require_manifest=True
            )

    def test_load_plugin_manifest_valid_file(self, tmp_path: Path) -> None:
        """Test loading a valid manifest file."""
        # Create a valid manifest file
        manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
  description: "Test plugin"
runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T16:00:00Z"
generated_by: "test"
"""

        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        loader = ManifestLoader()
        result = loader.load_plugin_manifest(
            TestPlugin,
            manifest_path=str(manifest_file),
            require_manifest=True
        )

        assert result is not None
        assert isinstance(result, PluginManifest)
        assert result.metadata.name == "test_plugin"
        assert result.metadata.version == "1.0.0"

    def test_load_plugin_manifest_invalid_yaml(self, tmp_path: Path) -> None:
        """Test loading manifest with invalid YAML."""
        # Create an invalid YAML file
        manifest_content = """
invalid_yaml: [
  missing_closing_bracket
"""

        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        loader = ManifestLoader()

        with pytest.raises(PluginRegistrationError, match="Invalid manifest file"):
            loader.load_plugin_manifest(
                TestPlugin,
                manifest_path=str(manifest_file),
                require_manifest=True
            )

    def test_load_plugin_manifest_invalid_schema(self, tmp_path: Path) -> None:
        """Test loading manifest with invalid schema."""
        # Create a manifest with invalid schema
        manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "invalid_name!"  # Invalid Python identifier
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T16:00:00Z"
generated_by: "test"
"""

        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        loader = ManifestLoader()

        with pytest.raises(PluginRegistrationError, match="Invalid manifest file"):
            loader.load_plugin_manifest(
                TestPlugin,
                manifest_path=str(manifest_file),
                require_manifest=True
            )

    def test_discover_manifest_file_no_module(self) -> None:
        """Test manifest discovery when plugin has no module."""
        # Create a plugin class without proper module
        class NoModulePlugin:
            __module__: str | None = None  # type: ignore[assignment]

        loader = ManifestLoader()
        result = loader._discover_manifest_file(NoModulePlugin)
        assert result is None

    def test_discover_manifest_file_main_module(self) -> None:
        """Test manifest discovery when plugin is in __main__ module."""
        # Create a plugin class in __main__
        class MainModulePlugin:
            __module__ = "__main__"

        loader = ManifestLoader()
        result = loader._discover_manifest_file(MainModulePlugin)
        assert result is None

    def test_discover_manifest_file_found(self, tmp_path: Path) -> None:
        """Test successful manifest discovery."""
        # Create a manifest file
        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text("test content")

        # Mock a plugin module
        import sys
        import types

        fake_module = types.ModuleType("fake_discovery_module")
        fake_module.__file__ = str(tmp_path / "plugin.py")
        sys.modules["fake_discovery_module"] = fake_module

        class DiscoveryPlugin:
            __module__ = "fake_discovery_module"
            __name__ = "DiscoveryPlugin"

        try:
            loader = ManifestLoader()
            result = loader._discover_manifest_file(DiscoveryPlugin)

            assert result is not None
            assert result == manifest_file

        finally:
            # Clean up
            if "fake_discovery_module" in sys.modules:
                del sys.modules["fake_discovery_module"]

    def test_validate_manifest_schema_valid(self) -> None:
        """Test schema validation with valid YAML."""
        yaml_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T16:00:00Z"
generated_by: "test"
"""

        loader = ManifestLoader()
        result = loader.validate_manifest_schema(yaml_content, "test.yaml")

        assert isinstance(result, PluginManifest)
        assert result.metadata.name == "test_plugin"

    def test_validate_manifest_schema_invalid(self) -> None:
        """Test schema validation with invalid YAML."""
        yaml_content = """
invalid_yaml: [
  missing_bracket
"""

        loader = ManifestLoader()

        with pytest.raises(PluginRegistrationError, match="Manifest validation failed"):
            loader.validate_manifest_schema(yaml_content, "test.yaml")


class TestLoadPluginManifestFunction:
    """Test the convenience function load_plugin_manifest."""

    def test_load_plugin_manifest_function(self, tmp_path: Path) -> None:
        """Test the convenience function."""
        # Create a valid manifest file
        manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T16:00:00Z"
generated_by: "test"
"""

        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        result = load_plugin_manifest(
            TestPlugin,
            manifest_path=str(manifest_file),
            require_manifest=True
        )

        assert result is not None
        assert isinstance(result, PluginManifest)
        assert result.metadata.name == "test_plugin"

    def test_load_plugin_manifest_function_with_logger(self, tmp_path: Path) -> None:
        """Test the convenience function with custom logger."""
        import logging

        # Create a valid manifest file
        manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T16:00:00Z"
generated_by: "test"
"""

        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        custom_logger = logging.getLogger("test")
        result = load_plugin_manifest(
            TestPlugin,
            manifest_path=str(manifest_file),
            require_manifest=True,
            logger=custom_logger
        )

        assert result is not None
        assert isinstance(result, PluginManifest)
