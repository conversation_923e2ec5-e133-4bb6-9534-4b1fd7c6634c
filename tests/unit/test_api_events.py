# tests/unit/test_api_events.py

"""
Unit tests for plugginger.api.events module.

Tests the @on_event decorator, event listener utilities, and event pattern
handling functionality.
"""

from __future__ import annotations

import inspect
from typing import Any

import pytest

from plugginger.api.events import (
    _normalize_event_patterns,
    _validate_async_requirement,
    _validate_basic_parameters,
    _validate_parameter_types,
    _validate_return_annotation,
    extract_event_listeners,
    get_event_listener_metadata,
    get_listener_patterns,
    is_event_listener_method,
    on_event,
    validate_event_listener_signature,
)
from plugginger.core.constants import EVENT_METADATA_KEY
from plugginger.core.exceptions import EventDefinitionError


# Test plugin class for event listener testing
class TestPlugin:
    """Test plugin with various event listeners."""

    @on_event("user.created")
    async def on_user_created(self, event_data: dict[str, Any]) -> None:
        """Handle user creation event."""
        pass

    @on_event(["user.updated", "user.deleted"], timeout_seconds=5.0, priority=10)
    async def on_user_changed(self, event_data: dict[str, Any], event_type: str) -> None:
        """Handle user update/delete events."""
        pass

    @on_event("admin.*", description="Admin event handler")
    async def on_admin_event(self, event_data: dict[str, Any]) -> None:
        """Handle admin events."""
        pass

    def regular_method(self) -> str:
        """Regular non-event method."""
        return "regular"

    async def async_method_no_decorator(self, data: dict[str, Any]) -> None:
        """Async method without event decorator."""
        pass


class TestOnEventDecorator:
    """Test @on_event decorator functionality."""

    def test_on_event_single_pattern(self) -> None:
        """Test decorator with single event pattern."""
        @on_event("test.event")
        async def test_handler(self: Any, event_data: dict[str, Any]) -> None:
            pass

        # Check that function is properly decorated
        assert hasattr(test_handler, EVENT_METADATA_KEY)
        metadata = getattr(test_handler, EVENT_METADATA_KEY)

        assert metadata["patterns"] == ["test.event"]
        assert metadata["timeout_seconds"] is None
        assert metadata["description"] is None
        assert metadata["priority"] == 0
        assert metadata["method_name"] == "test_handler"

    def test_on_event_multiple_patterns(self) -> None:
        """Test decorator with multiple event patterns."""
        @on_event(["user.created", "user.updated", "user.deleted"])
        async def test_handler(self, event_data: dict[str, Any]) -> None:
            pass

        metadata = getattr(test_handler, EVENT_METADATA_KEY)
        assert metadata["patterns"] == ["user.created", "user.updated", "user.deleted"]

    def test_on_event_with_all_options(self) -> None:
        """Test decorator with all optional parameters."""
        @on_event(
            patterns="admin.action",
            timeout_seconds=10.5,
            description="Admin action handler",
            priority=5
        )
        async def test_handler(self, event_data: dict[str, Any]) -> None:
            pass

        metadata = getattr(test_handler, EVENT_METADATA_KEY)
        assert metadata["patterns"] == ["admin.action"]
        assert metadata["timeout_seconds"] == 10.5
        assert metadata["description"] == "Admin action handler"
        assert metadata["priority"] == 5

    def test_on_event_preserves_function_metadata(self) -> None:
        """Test that decorator preserves function metadata."""
        @on_event("test.event")
        async def test_handler(self, event_data: dict[str, Any]) -> None:
            """Test handler docstring."""
            pass

        assert test_handler.__name__ == "test_handler"
        assert test_handler.__doc__ == "Test handler docstring."

    def test_on_event_signature_metadata(self) -> None:
        """Test that decorator captures signature metadata."""
        @on_event("test.event")
        async def test_handler(self, event_data: dict[str, Any], event_type: str = "default") -> None:
            pass

        metadata = getattr(test_handler, EVENT_METADATA_KEY)
        assert "signature" in metadata
        assert "parameters" in metadata
        assert len(metadata["parameters"]) == 2

        # Check first parameter (event_data)
        param1 = metadata["parameters"][0]
        assert param1["name"] == "event_data"
        assert param1["annotation"] == dict[str, Any] or str(param1["annotation"]) == "dict[str, Any]"
        assert param1["default"] is None
        assert param1["kind"] == "POSITIONAL_OR_KEYWORD"

        # Check second parameter (event_type)
        param2 = metadata["parameters"][1]
        assert param2["name"] == "event_type"
        assert param2["annotation"] is str or str(param2["annotation"]) == "str"
        assert param2["default"] == "default"
        assert param2["kind"] == "POSITIONAL_OR_KEYWORD"

    def test_on_event_sync_function_error(self) -> None:
        """Test that decorator raises error for sync functions."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event("test.event")
            def sync_handler(self, event_data: dict[str, Any]) -> None:
                pass

        assert "must be async" in str(exc_info.value)
        assert "sync_handler" in str(exc_info.value)

    def test_on_event_no_self_parameter_error(self) -> None:
        """Test that decorator raises error for functions without self parameter."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event("test.event")
            async def no_self_handler(event_data: dict[str, Any]) -> None:
                pass

        assert "must be an instance method" in str(exc_info.value)
        assert "first parameter must be 'self'" in str(exc_info.value)

    def test_on_event_invalid_timeout_error(self) -> None:
        """Test that decorator raises error for invalid timeout values."""
        invalid_timeouts = [0, -1, -5.5, "invalid"]

        for invalid_timeout in invalid_timeouts:
            with pytest.raises(EventDefinitionError) as exc_info:
                @on_event("test.event", timeout_seconds=invalid_timeout)
                async def test_handler(self, event_data: dict[str, Any]) -> None:
                    pass

            assert "timeout must be a positive number" in str(exc_info.value)

    def test_on_event_invalid_priority_error(self) -> None:
        """Test that decorator raises error for invalid priority values."""
        invalid_priorities = [1.5, "high", None]

        for invalid_priority in invalid_priorities:
            with pytest.raises(EventDefinitionError) as exc_info:
                @on_event("test.event", priority=invalid_priority)
                async def test_handler(self, event_data: dict[str, Any]) -> None:
                    pass

            assert "priority must be an integer" in str(exc_info.value)

    def test_on_event_empty_pattern_error(self) -> None:
        """Test that decorator raises error for empty patterns."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event("")
            async def test_handler(self, event_data: dict[str, Any]) -> None:
                pass

        assert "must be a non-empty string" in str(exc_info.value)

    def test_on_event_invalid_pattern_in_list_error(self) -> None:
        """Test that decorator raises error for invalid patterns in list."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event(["valid.pattern", "", "another.valid"])
            async def test_handler(self, event_data: dict[str, Any]) -> None:
                pass

        assert "must be a non-empty string" in str(exc_info.value)

    def test_on_event_invalid_return_annotation_error(self) -> None:
        """Test that decorator raises error for invalid return annotations."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event("test.event")
            async def test_handler(self, event_data: dict[str, Any]) -> str:
                return "invalid"

        assert "must return None" in str(exc_info.value)

    def test_on_event_valid_return_annotations(self) -> None:
        """Test that decorator accepts valid return annotations."""
        # These should all work
        @on_event("test.event1")
        async def handler1(self, event_data: dict[str, Any]) -> None:
            pass

        @on_event("test.event2")
        async def handler2(self, event_data: dict[str, Any]):
            pass

        # Check that both are properly decorated
        assert hasattr(handler1, EVENT_METADATA_KEY)
        assert hasattr(handler2, EVENT_METADATA_KEY)

    def test_on_event_valid_timeout_values(self) -> None:
        """Test that decorator accepts valid timeout values."""
        valid_timeouts = [1, 5.5, 10.0, 100]

        for valid_timeout in valid_timeouts:
            @on_event("test.event", timeout_seconds=valid_timeout)
            async def test_handler(self, event_data: dict[str, Any]) -> None:
                pass

            metadata = getattr(test_handler, EVENT_METADATA_KEY)
            assert metadata["timeout_seconds"] == valid_timeout

    def test_on_event_valid_priority_values(self) -> None:
        """Test that decorator accepts valid priority values."""
        valid_priorities = [-10, 0, 5, 100]

        for valid_priority in valid_priorities:
            @on_event("test.event", priority=valid_priority)
            async def test_handler(self, event_data: dict[str, Any]) -> None:
                pass

            metadata = getattr(test_handler, EVENT_METADATA_KEY)
            assert metadata["priority"] == valid_priority


class TestNormalizeEventPatterns:
    """Test _normalize_event_patterns function."""

    def test_normalize_single_string_pattern(self) -> None:
        """Test normalizing single string pattern."""
        result = _normalize_event_patterns("user.created")
        assert result == ["user.created"]

    def test_normalize_list_patterns(self) -> None:
        """Test normalizing list of patterns."""
        patterns = ["user.created", "user.updated", "user.deleted"]
        result = _normalize_event_patterns(patterns)
        assert result == patterns

    def test_normalize_tuple_patterns(self) -> None:
        """Test normalizing tuple of patterns."""
        patterns = ("user.created", "user.updated")
        result = _normalize_event_patterns(patterns)
        assert result == ["user.created", "user.updated"]

    def test_normalize_invalid_pattern_type_error(self) -> None:
        """Test error for invalid pattern type."""
        with pytest.raises(EventDefinitionError) as exc_info:
            _normalize_event_patterns(123)  # type: ignore[arg-type]

        assert "must be a string or list of strings" in str(exc_info.value)

    def test_normalize_invalid_pattern_in_list_error(self) -> None:
        """Test error for invalid pattern in list."""
        with pytest.raises(EventDefinitionError) as exc_info:
            _normalize_event_patterns(["valid.pattern", 123, "another.valid"])  # type: ignore[list-item]

        assert "All event patterns must be strings" in str(exc_info.value)

    def test_normalize_empty_list(self) -> None:
        """Test normalizing empty list."""
        result = _normalize_event_patterns([])
        assert result == []


class TestGetEventListenerMetadata:
    """Test get_event_listener_metadata function."""

    def test_get_event_listener_metadata_decorated_method(self) -> None:
        """Test getting metadata from decorated method."""
        plugin = TestPlugin()
        metadata = get_event_listener_metadata(plugin.on_user_created)

        assert metadata["patterns"] == ["user.created"]
        assert metadata["timeout_seconds"] is None
        assert metadata["priority"] == 0

    def test_get_event_listener_metadata_with_options(self) -> None:
        """Test getting metadata from method with options."""
        plugin = TestPlugin()
        metadata = get_event_listener_metadata(plugin.on_user_changed)

        assert metadata["patterns"] == ["user.updated", "user.deleted"]
        assert metadata["timeout_seconds"] == 5.0
        assert metadata["priority"] == 10

    def test_get_event_listener_metadata_non_event_method_error(self) -> None:
        """Test error for non-event method."""
        plugin = TestPlugin()

        with pytest.raises(EventDefinitionError) as exc_info:
            get_event_listener_metadata(plugin.regular_method)

        assert "is not a valid event listener" in str(exc_info.value)
        assert "missing @on_event decorator" in str(exc_info.value)

    def test_get_event_listener_metadata_unbound_method(self) -> None:
        """Test getting metadata from unbound class method."""
        metadata = get_event_listener_metadata(TestPlugin.on_user_created)

        assert metadata["patterns"] == ["user.created"]
        assert metadata["method_name"] == "on_user_created"


class TestIsEventListenerMethod:
    """Test is_event_listener_method function."""

    def test_is_event_listener_method_decorated_method(self) -> None:
        """Test that function returns True for decorated methods."""
        plugin = TestPlugin()
        assert is_event_listener_method(plugin.on_user_created) is True
        assert is_event_listener_method(plugin.on_user_changed) is True

    def test_is_event_listener_method_unbound_method(self) -> None:
        """Test that function returns True for unbound decorated methods."""
        assert is_event_listener_method(TestPlugin.on_user_created) is True
        assert is_event_listener_method(TestPlugin.on_user_changed) is True

    def test_is_event_listener_method_regular_method(self) -> None:
        """Test that function returns False for regular methods."""
        plugin = TestPlugin()
        assert is_event_listener_method(plugin.regular_method) is False

    def test_is_event_listener_method_async_method_no_decorator(self) -> None:
        """Test that function returns False for async methods without decorator."""
        plugin = TestPlugin()
        assert is_event_listener_method(plugin.async_method_no_decorator) is False

    def test_is_event_listener_method_non_function(self) -> None:
        """Test that function returns False for non-function objects."""
        assert is_event_listener_method("not a function") is False  # type: ignore[arg-type]
        assert is_event_listener_method(123) is False  # type: ignore[arg-type]
        assert is_event_listener_method(None) is False  # type: ignore[arg-type]


class TestExtractEventListeners:
    """Test extract_event_listeners function."""

    def test_extract_event_listeners_basic(self) -> None:
        """Test extracting event listeners from plugin instance."""
        plugin = TestPlugin()
        listeners = extract_event_listeners(plugin)

        # Should find all decorated methods
        assert "on_user_created" in listeners
        assert "on_user_changed" in listeners
        assert "on_admin_event" in listeners

        # Should not include regular methods
        assert "regular_method" not in listeners
        assert "async_method_no_decorator" not in listeners

        # Check that methods are bound to the instance
        assert hasattr(listeners["on_user_created"], "__self__")
        assert listeners["on_user_created"].__self__ is plugin

    def test_extract_event_listeners_empty_plugin(self) -> None:
        """Test extracting from plugin with no event listeners."""
        class EmptyPlugin:
            def regular_method(self) -> str:
                return "test"

        plugin = EmptyPlugin()
        listeners = extract_event_listeners(plugin)
        assert listeners == {}

    def test_extract_event_listeners_mixed_methods(self) -> None:
        """Test extracting from plugin with mixed method types."""
        class MixedPlugin:
            @on_event("test.event")
            async def event_handler(self, event_data: dict[str, Any]) -> None:
                pass

            def sync_method(self) -> str:
                return "sync"

            async def async_method(self) -> None:
                pass

            @property
            def some_property(self) -> str:
                return "property"

        plugin = MixedPlugin()
        listeners = extract_event_listeners(plugin)

        assert len(listeners) == 1
        assert "event_handler" in listeners
        assert "sync_method" not in listeners
        assert "async_method" not in listeners
        assert "some_property" not in listeners


class TestGetListenerPatterns:
    """Test get_listener_patterns function."""

    def test_get_listener_patterns_basic(self) -> None:
        """Test getting patterns from plugin instance."""
        plugin = TestPlugin()
        patterns = get_listener_patterns(plugin)

        # Should have patterns from all event listeners
        pattern_strings = [p[0] for p in patterns]
        assert "user.created" in pattern_strings
        assert "user.updated" in pattern_strings
        assert "user.deleted" in pattern_strings
        assert "admin.*" in pattern_strings

        # Check that handlers are included
        for _pattern, handler, metadata in patterns:
            assert callable(handler)
            assert isinstance(metadata, dict)
            assert "patterns" in metadata
            assert "priority" in metadata

    def test_get_listener_patterns_priority_sorting(self) -> None:
        """Test that patterns are sorted by priority."""
        plugin = TestPlugin()
        patterns = get_listener_patterns(plugin)

        # Extract priorities
        priorities = [metadata["priority"] for _, _, metadata in patterns]

        # Should be sorted in descending order (higher priority first)
        assert priorities == sorted(priorities, reverse=True)

        # Check specific priorities
        high_priority_patterns = [p for p in patterns if p[2]["priority"] == 10]
        low_priority_patterns = [p for p in patterns if p[2]["priority"] == 0]

        # High priority patterns should come first
        assert len(high_priority_patterns) == 2  # user.updated and user.deleted
        assert len(low_priority_patterns) == 2   # user.created and admin.*

    def test_get_listener_patterns_metadata_content(self) -> None:
        """Test that metadata contains expected content."""
        plugin = TestPlugin()
        patterns = get_listener_patterns(plugin)

        for pattern, _handler, metadata in patterns:
            assert pattern in metadata["patterns"]
            assert "timeout_seconds" in metadata
            assert "description" in metadata
            assert "priority" in metadata
            assert "method_name" in metadata

    def test_get_listener_patterns_empty_plugin(self) -> None:
        """Test getting patterns from plugin with no listeners."""
        class EmptyPlugin:
            pass

        plugin = EmptyPlugin()
        patterns = get_listener_patterns(plugin)
        assert patterns == []


class TestValidateEventListenerSignature:
    """Test validate_event_listener_signature function."""

    def test_validate_event_listener_signature_valid(self) -> None:
        """Test validation of valid event listener."""
        # Should not raise any exception
        validate_event_listener_signature(TestPlugin.on_user_created)
        validate_event_listener_signature(TestPlugin.on_user_changed)

    def test_validate_event_listener_signature_invalid_sync(self) -> None:
        """Test validation fails for sync method."""
        def sync_method(self: Any, event_data: dict[str, Any]) -> None:
            pass

        with pytest.raises(EventDefinitionError):
            validate_event_listener_signature(sync_method)

    def test_validate_event_listener_signature_invalid_no_self(self) -> None:
        """Test validation fails for method without self."""
        async def no_self_method(event_data: dict[str, Any]) -> None:
            pass

        with pytest.raises(EventDefinitionError):
            validate_event_listener_signature(no_self_method)


class TestValidationHelpers:
    """Test validation helper functions."""

    def test_validate_async_requirement_valid(self) -> None:
        """Test async requirement validation for valid method."""
        async def async_method() -> None:
            pass

        # Should not raise
        _validate_async_requirement(async_method)

    def test_validate_async_requirement_invalid(self) -> None:
        """Test async requirement validation for sync method."""
        def sync_method() -> None:
            pass

        with pytest.raises(EventDefinitionError) as exc_info:
            _validate_async_requirement(sync_method)

        assert "must be async" in str(exc_info.value)

    def test_validate_basic_parameters_valid(self) -> None:
        """Test basic parameter validation for valid method."""
        async def valid_method(self: Any, event_data: dict[str, Any]) -> None:
            pass

        sig = inspect.signature(valid_method)
        params = list(sig.parameters.values())

        # Should not raise
        _validate_basic_parameters(valid_method, params)

    def test_validate_basic_parameters_no_params(self) -> None:
        """Test basic parameter validation for method with no parameters."""
        async def no_params_method() -> None:
            pass

        sig = inspect.signature(no_params_method)
        params = list(sig.parameters.values())

        with pytest.raises(EventDefinitionError) as exc_info:
            _validate_basic_parameters(no_params_method, params)

        assert "must have at least one parameter" in str(exc_info.value)

    def test_validate_basic_parameters_no_self(self) -> None:
        """Test basic parameter validation for method without self."""
        async def no_self_method(event_data: dict[str, Any]) -> None:
            pass

        sig = inspect.signature(no_self_method)
        params = list(sig.parameters.values())

        with pytest.raises(EventDefinitionError) as exc_info:
            _validate_basic_parameters(no_self_method, params)

        assert "first parameter must be 'self'" in str(exc_info.value)

    def test_validate_basic_parameters_too_many_params(self) -> None:
        """Test basic parameter validation for method with too many parameters."""
        async def too_many_params_method(self: Any, event_data: dict[str, Any], event_type: str, extra: str) -> None:
            pass

        sig = inspect.signature(too_many_params_method)
        params = list(sig.parameters.values())

        with pytest.raises(EventDefinitionError) as exc_info:
            _validate_basic_parameters(too_many_params_method, params)

        assert "must have 1 or 2 parameters after 'self'" in str(exc_info.value)

    def test_validate_parameter_types_valid(self) -> None:
        """Test parameter type validation for valid parameters."""
        async def valid_method(self: Any, event_data: dict[str, Any], event_type: str = "default") -> None:
            pass

        sig = inspect.signature(valid_method)
        params = list(sig.parameters.values())[1:]  # Skip self

        # Should not raise
        _validate_parameter_types(valid_method, params)

    def test_validate_parameter_types_var_positional(self) -> None:
        """Test parameter type validation rejects *args."""
        async def var_args_method(self: Any, *args: Any) -> None:
            pass

        sig = inspect.signature(var_args_method)
        params = list(sig.parameters.values())[1:]  # Skip self

        with pytest.raises(EventDefinitionError) as exc_info:
            _validate_parameter_types(var_args_method, params)

        assert "cannot use *args parameters" in str(exc_info.value)

    def test_validate_parameter_types_var_keyword(self) -> None:
        """Test parameter type validation rejects **kwargs."""
        async def var_kwargs_method(self: Any, **kwargs: Any) -> None:
            pass

        sig = inspect.signature(var_kwargs_method)
        params = list(sig.parameters.values())[1:]  # Skip self

        with pytest.raises(EventDefinitionError) as exc_info:
            _validate_parameter_types(var_kwargs_method, params)

        assert "cannot use **kwargs parameters" in str(exc_info.value)

    def test_validate_return_annotation_valid(self) -> None:
        """Test return annotation validation for valid annotations."""
        async def none_return_method(self: Any, event_data: dict[str, Any]) -> None:
            pass

        async def no_annotation_method(self: Any, event_data: dict[str, Any]) -> None:
            pass

        sig1 = inspect.signature(none_return_method)
        sig2 = inspect.signature(no_annotation_method)

        # Should not raise
        _validate_return_annotation(none_return_method, sig1)
        _validate_return_annotation(no_annotation_method, sig2)

    def test_validate_return_annotation_invalid(self) -> None:
        """Test return annotation validation for invalid annotations."""
        async def str_return_method(self: Any, event_data: dict[str, Any]) -> str:
            return "invalid"

        sig = inspect.signature(str_return_method)

        with pytest.raises(EventDefinitionError) as exc_info:
            _validate_return_annotation(str_return_method, sig)

        assert "must return None" in str(exc_info.value)


class TestEventIntegration:
    """Test integration scenarios for event listeners."""

    def test_event_listener_lifecycle_integration(self) -> None:
        """Test complete event listener lifecycle."""
        class IntegrationPlugin:
            @on_event("integration.test", timeout_seconds=2.0, priority=5)
            async def handle_integration_event(self, event_data: dict[str, Any]) -> None:
                """Handle integration test event."""
                pass

        plugin = IntegrationPlugin()

        # Test listener extraction
        listeners = extract_event_listeners(plugin)
        assert "handle_integration_event" in listeners
        assert listeners["handle_integration_event"] == plugin.handle_integration_event

        # Test metadata extraction
        metadata = get_event_listener_metadata(plugin.handle_integration_event)
        assert metadata["patterns"] == ["integration.test"]
        assert metadata["timeout_seconds"] == 2.0
        assert metadata["priority"] == 5

        # Test pattern extraction
        patterns = get_listener_patterns(plugin)
        assert len(patterns) == 1
        pattern, handler, meta = patterns[0]
        assert pattern == "integration.test"
        assert handler == plugin.handle_integration_event
        assert meta["priority"] == 5

    def test_multiple_event_listeners_same_plugin(self) -> None:
        """Test plugin with multiple event listeners."""
        class MultiEventPlugin:
            @on_event("event.high", priority=10)
            async def high_priority_handler(self, event_data: dict[str, Any]) -> None:
                pass

            @on_event("event.medium", priority=5)
            async def medium_priority_handler(self, event_data: dict[str, Any]) -> None:
                pass

            @on_event("event.low", priority=1)
            async def low_priority_handler(self, event_data: dict[str, Any]) -> None:
                pass

        plugin = MultiEventPlugin()
        patterns = get_listener_patterns(plugin)

        # Should be sorted by priority (descending)
        priorities = [meta["priority"] for _, _, meta in patterns]
        assert priorities == [10, 5, 1]

        # Check pattern order
        pattern_names = [pattern for pattern, _, _ in patterns]
        assert pattern_names == ["event.high", "event.medium", "event.low"]

    def test_event_listener_with_complex_patterns(self) -> None:
        """Test event listener with complex pattern matching."""
        class PatternPlugin:
            @on_event(["user.*", "admin.user.*", "system.user.created"])
            async def user_event_handler(self, event_data: dict[str, Any], event_type: str) -> None:
                pass

        plugin = PatternPlugin()
        patterns = get_listener_patterns(plugin)

        # Should have one handler for each pattern
        assert len(patterns) == 3
        pattern_strings = [p[0] for p in patterns]
        assert "user.*" in pattern_strings
        assert "admin.user.*" in pattern_strings
        assert "system.user.created" in pattern_strings

        # All should point to the same handler
        handlers = [h for _, h, _ in patterns]
        assert all(h == plugin.user_event_handler for h in handlers)

    def test_event_listener_signature_validation_integration(self) -> None:
        """Test signature validation integration."""
        # Valid signatures should pass
        validate_event_listener_signature(TestPlugin.on_user_created)
        validate_event_listener_signature(TestPlugin.on_user_changed)

        # Invalid signatures should fail
        class InvalidPlugin:
            def sync_handler(self: Any, event_data: dict[str, Any]) -> None:
                pass

            async def no_self_handler(event_data: dict[str, Any]) -> None:  # type: ignore[misc]
                pass

        with pytest.raises(EventDefinitionError):
            validate_event_listener_signature(InvalidPlugin.sync_handler)

        with pytest.raises(EventDefinitionError):
            validate_event_listener_signature(InvalidPlugin.no_self_handler)

    def test_event_listener_metadata_preservation(self) -> None:
        """Test that event listener metadata is properly preserved."""
        @on_event(
            patterns=["test.event1", "test.event2"],
            timeout_seconds=15.5,
            description="Test event handler",
            priority=-5
        )
        async def test_handler(self: Any, event_data: dict[str, Any]) -> None:
            """Test handler with full metadata."""
            pass

        # Check function metadata preservation
        assert test_handler.__name__ == "test_handler"
        assert test_handler.__doc__ == "Test handler with full metadata."

        # Check event metadata
        metadata = getattr(test_handler, EVENT_METADATA_KEY)
        assert metadata["patterns"] == ["test.event1", "test.event2"]
        assert metadata["timeout_seconds"] == 15.5
        assert metadata["description"] == "Test event handler"
        assert metadata["priority"] == -5
        assert metadata["method_name"] == "test_handler"

        # Check parameter metadata
        assert len(metadata["parameters"]) == 1
        param = metadata["parameters"][0]
        assert param["name"] == "event_data"
        assert param["annotation"] == dict[str, Any] or str(param["annotation"]) == "dict[str, Any]"


class TestEventEdgeCases:
    """Test edge cases and error conditions."""

    def test_on_event_with_tuple_patterns(self) -> None:
        """Test @on_event with tuple patterns."""
        @on_event(("event.one", "event.two"))
        async def tuple_handler(self: Any, event_data: dict[str, Any]) -> None:
            pass

        metadata = getattr(tuple_handler, EVENT_METADATA_KEY)
        assert metadata["patterns"] == ["event.one", "event.two"]

    def test_on_event_with_mixed_pattern_types_error(self) -> None:
        """Test @on_event with mixed invalid pattern types."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event(["valid.pattern", 123, "another.valid"])  # type: ignore[list-item]
            async def mixed_handler(self: Any, event_data: dict[str, Any]) -> None:
                pass

        assert "All event patterns must be strings" in str(exc_info.value)

    def test_on_event_with_none_pattern_error(self) -> None:
        """Test @on_event with None pattern."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event(None)  # type: ignore[arg-type]
            async def none_handler(self: Any, event_data: dict[str, Any]) -> None:
                pass

        assert "must be a string or list of strings" in str(exc_info.value)

    def test_on_event_zero_timeout(self) -> None:
        """Test @on_event with zero timeout (should fail)."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event("test.event", timeout_seconds=0)
            async def zero_timeout_handler(self: Any, event_data: dict[str, Any]) -> None:
                pass

        assert "timeout must be a positive number" in str(exc_info.value)

    def test_on_event_negative_timeout(self) -> None:
        """Test @on_event with negative timeout (should fail)."""
        with pytest.raises(EventDefinitionError) as exc_info:
            @on_event("test.event", timeout_seconds=-1.0)
            async def negative_timeout_handler(self: Any, event_data: dict[str, Any]) -> None:
                pass

        assert "timeout must be a positive number" in str(exc_info.value)

    def test_on_event_with_none_timeout(self) -> None:
        """Test @on_event with None timeout (should work)."""
        @on_event("test.event", timeout_seconds=None)
        async def none_timeout_handler(self: Any, event_data: dict[str, Any]) -> None:
            pass

        metadata = getattr(none_timeout_handler, EVENT_METADATA_KEY)
        assert metadata["timeout_seconds"] is None

    def test_on_event_with_none_description(self) -> None:
        """Test @on_event with None description (should work)."""
        @on_event("test.event", description=None)
        async def none_desc_handler(self: Any, event_data: dict[str, Any]) -> None:
            pass

        metadata = getattr(none_desc_handler, EVENT_METADATA_KEY)
        assert metadata["description"] is None

    def test_on_event_with_empty_description(self) -> None:
        """Test @on_event with empty description (should work)."""
        @on_event("test.event", description="")
        async def empty_desc_handler(self: Any, event_data: dict[str, Any]) -> None:
            pass

        metadata = getattr(empty_desc_handler, EVENT_METADATA_KEY)
        assert metadata["description"] == ""

    def test_normalize_patterns_with_empty_string_in_list(self) -> None:
        """Test pattern normalization with empty string in list."""
        # This should work - empty strings are allowed in the list
        result = _normalize_event_patterns(["valid.pattern", "", "another.valid"])
        assert result == ["valid.pattern", "", "another.valid"]

    def test_is_event_listener_method_with_class_methods(self) -> None:
        """Test is_event_listener_method with class and static methods."""
        class MethodTestPlugin:
            @on_event("test.event")
            async def instance_method(self, event_data: dict[str, Any]) -> None:
                pass

            @classmethod
            async def class_method(cls, event_data: dict[str, Any]) -> None:
                pass

            @staticmethod
            async def static_method(event_data: dict[str, Any]) -> None:
                pass

        # Only instance method should be recognized as event listener
        assert is_event_listener_method(MethodTestPlugin.instance_method) is True
        assert is_event_listener_method(MethodTestPlugin.class_method) is False
        assert is_event_listener_method(MethodTestPlugin.static_method) is False

    def test_extract_event_listeners_ignores_private_methods(self) -> None:
        """Test that extract_event_listeners ignores private methods."""
        class PrivateMethodPlugin:
            @on_event("public.event")
            async def public_handler(self, event_data: dict[str, Any]) -> None:
                pass

            @on_event("private.event")
            async def _private_handler(self, event_data: dict[str, Any]) -> None:
                pass

            @on_event("dunder.event")
            async def __dunder_handler__(self, event_data: dict[str, Any]) -> None:
                pass

        plugin = PrivateMethodPlugin()
        listeners = extract_event_listeners(plugin)

        # Should only find public method
        assert "public_handler" in listeners
        assert "_private_handler" not in listeners
        assert "__dunder_handler__" not in listeners

    def test_get_listener_patterns_with_duplicate_priorities(self) -> None:
        """Test get_listener_patterns with duplicate priorities."""
        class DuplicatePriorityPlugin:
            @on_event("event.a", priority=5)
            async def handler_a(self, event_data: dict[str, Any]) -> None:
                pass

            @on_event("event.b", priority=5)
            async def handler_b(self, event_data: dict[str, Any]) -> None:
                pass

            @on_event("event.c", priority=10)
            async def handler_c(self, event_data: dict[str, Any]) -> None:
                pass

        plugin = DuplicatePriorityPlugin()
        patterns = get_listener_patterns(plugin)

        # Should be sorted by priority, with stable sort for equal priorities
        priorities = [meta["priority"] for _, _, meta in patterns]
        assert priorities == [10, 5, 5]

        # High priority should come first
        assert patterns[0][0] == "event.c"
        # The order of equal priorities may vary but both should be present
        equal_priority_patterns = {patterns[1][0], patterns[2][0]}
        assert equal_priority_patterns == {"event.a", "event.b"}
