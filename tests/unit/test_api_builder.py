# tests/unit/test_api_builder.py

"""
Unit tests for plugginger.api.builder module.

Tests the PluggingerAppBuilder class and its plugin registration,
dependency management, and application building functionality.
"""

from __future__ import annotations

from typing import Any
from unittest.mock import Mock, patch

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase
from plugginger.config.models import GlobalAppConfig
from plugginger.core.constants import PLUGIN_METADATA_KEY
from plugginger.core.exceptions import (
    AppPluginError,
    PluginRegistrationError,
)


# Mock plugin classes for testing
@pytest.fixture
def mock_plugin_class() -> type[PluginBase]:
    """Create a mock plugin class with proper metadata."""
    class MockPlugin(PluginBase):
        needs: list[Depends] = []

    # Add plugin metadata
    setattr(MockPlugin, PLUGIN_METADATA_KEY, True)
    MockPlugin._plugginger_plugin_name = "mock_plugin"
    MockPlugin._plugginger_plugin_version = "1.0.0"
    MockPlugin._plugginger_config_schema = None
    MockPlugin._plugginger_instance_id = ""

    return MockPlugin


@pytest.fixture
def mock_app_plugin_class() -> type[Any]:
    """Create a mock app plugin class with proper metadata."""
    # Import here to avoid circular imports in fixtures
    from plugginger.api.app_plugin import AppPluginBase

    class MockAppPlugin(AppPluginBase):
        needs: list[Depends] = []

        def _configure_internal_app(self) -> None:
            pass

    # Add plugin metadata
    setattr(MockAppPlugin, PLUGIN_METADATA_KEY, True)
    MockAppPlugin._plugginger_plugin_name = "mock_app_plugin"
    MockAppPlugin._plugginger_plugin_version = "1.0.0"
    MockAppPlugin._plugginger_config_schema = None
    MockAppPlugin._plugginger_instance_id = ""

    return MockAppPlugin


class TestPluggingerAppBuilderInit:
    """Test PluggingerAppBuilder initialization."""

    def test_init_with_valid_app_name(self) -> None:
        """Test initialization with valid app name."""
        builder = PluggingerAppBuilder("test_app")

        assert builder._app_name == "test_app"
        assert builder._parent_app_plugin_context is None
        assert builder._current_fractal_depth == 0
        assert builder._instance_id_prefix is None
        assert isinstance(builder._registered_item_classes, dict)
        assert isinstance(builder._plugin_dependency_declarations, dict)
        assert len(builder._registered_item_classes) == 0

    def test_init_with_empty_app_name_error(self) -> None:
        """Test that initialization raises error for empty app name."""
        with pytest.raises(ValueError) as exc_info:
            PluggingerAppBuilder("")

        assert "must be a non-empty string" in str(exc_info.value)

    def test_init_with_none_app_name_error(self) -> None:
        """Test that initialization raises error for None app name."""
        with pytest.raises(ValueError) as exc_info:
            PluggingerAppBuilder(None)  # type: ignore[arg-type]

        assert "must be a non-empty string" in str(exc_info.value)

    def test_init_with_parent_context(self) -> None:
        """Test initialization with parent app plugin context."""
        parent_mock = Mock()
        parent_mock._plugginger_instance_id = "parent_id"

        builder = PluggingerAppBuilder("child_app", parent_app_plugin_context=parent_mock)

        assert builder._parent_app_plugin_context is parent_mock
        assert builder._instance_id_prefix == "parent_id"

    def test_init_with_fractal_depth(self) -> None:
        """Test initialization with custom fractal depth."""
        builder = PluggingerAppBuilder("test_app", _current_depth=3, _max_depth_from_config=5)

        assert builder._current_fractal_depth == 3
        assert builder._max_fractal_depth == 5

    def test_init_with_default_max_depth(self) -> None:
        """Test initialization uses default max depth from GlobalAppConfig."""
        builder = PluggingerAppBuilder("test_app")

        # Should use default from GlobalAppConfig
        default_max_depth = GlobalAppConfig.model_fields["max_fractal_depth"].default
        if default_max_depth is not None:
            assert builder._max_fractal_depth == default_max_depth
        else:
            assert builder._max_fractal_depth == 10  # fallback


class TestPluggingerAppBuilderRegistration:
    """Test plugin registration methods."""

    def test_get_registered_plugin_classes_empty(self) -> None:
        """Test getting registered classes when none are registered."""
        builder = PluggingerAppBuilder("test_app")

        classes = builder.get_registered_plugin_classes()

        assert classes == {}
        assert classes is not builder._registered_item_classes  # Should be a copy

    def test_get_registered_plugin_classes_with_plugins(self, mock_plugin_class: type[PluginBase]) -> None:
        """Test getting registered classes with plugins."""
        builder = PluggingerAppBuilder("test_app")
        builder._registered_item_classes["test_plugin"] = mock_plugin_class

        classes = builder.get_registered_plugin_classes()

        assert "test_plugin" in classes
        assert classes["test_plugin"] is mock_plugin_class

    def test_include_valid_plugin(self, mock_plugin_class: type[PluginBase]) -> None:
        """Test including a valid plugin class."""
        builder = PluggingerAppBuilder("test_app")

        result = builder.include(mock_plugin_class)

        assert result is builder  # Should return self for chaining
        assert "mock_plugin" in builder._registered_item_classes
        assert builder._registered_item_classes["mock_plugin"] is mock_plugin_class

    def test_include_non_class_error(self) -> None:
        """Test that include raises error for non-class argument."""
        builder = PluggingerAppBuilder("test_app")

        with pytest.raises(TypeError) as exc_info:
            builder.include("not_a_class")  # type: ignore[arg-type]

        assert "must be a class" in str(exc_info.value)

    def test_include_class_without_plugin_decorator_error(self) -> None:
        """Test that include raises error for class without @plugin decorator."""
        builder = PluggingerAppBuilder("test_app")

        class UnDecoratedPlugin(PluginBase):
            pass

        with pytest.raises(PluginRegistrationError) as exc_info:
            builder.include(UnDecoratedPlugin)

        assert "not a valid Plugginger plugin" in str(exc_info.value)
        assert "missing @plugin decorator" in str(exc_info.value)

    def test_include_app_valid_app_plugin(self, mock_app_plugin_class: type[Any]) -> None:
        """Test including a valid app plugin class."""
        builder = PluggingerAppBuilder("test_app")

        result = builder.include_app(mock_app_plugin_class, "my_app_plugin")

        assert result is builder  # Should return self for chaining
        assert "my_app_plugin" in builder._registered_item_classes
        assert builder._registered_item_classes["my_app_plugin"] is mock_app_plugin_class

    def test_include_app_non_class_error(self) -> None:
        """Test that include_app raises error for non-class argument."""
        builder = PluggingerAppBuilder("test_app")

        with pytest.raises(TypeError) as exc_info:
            builder.include_app("not_a_class", "test_name")  # type: ignore[arg-type]

        assert "must be a class" in str(exc_info.value)

    def test_include_app_exceeds_fractal_depth_error(self, mock_app_plugin_class: type[Any]) -> None:
        """Test that include_app raises error when exceeding fractal depth."""
        builder = PluggingerAppBuilder("test_app", _current_depth=5, _max_depth_from_config=5)

        with pytest.raises(AppPluginError) as exc_info:
            builder.include_app(mock_app_plugin_class, "deep_app")

        assert "Exceeds maximum configured fractal depth" in str(exc_info.value)

    def test_register_item_class_duplicate_name_error(self, mock_plugin_class: type[PluginBase]) -> None:
        """Test that registering duplicate names raises error."""
        builder = PluggingerAppBuilder("test_app")
        builder._register_item_class("duplicate", mock_plugin_class, False)

        with pytest.raises(PluginRegistrationError) as exc_info:
            builder._register_item_class("duplicate", mock_plugin_class, False)

        assert "already included in the builder" in str(exc_info.value)

    def test_register_item_class_invalid_name_error(self, mock_plugin_class: type[PluginBase]) -> None:
        """Test that registering with invalid name raises error."""
        builder = PluggingerAppBuilder("test_app")

        invalid_names = ["", "123invalid", "invalid-name", "invalid name"]
        for invalid_name in invalid_names:
            with pytest.raises(PluginRegistrationError) as exc_info:
                builder._register_item_class(invalid_name, mock_plugin_class, False)

            assert "Invalid registration name" in str(exc_info.value)


class TestPluggingerAppBuilderMetadata:
    """Test metadata handling methods."""

    def test_get_plugin_metadata_attr_valid(self, mock_plugin_class: type[PluginBase]) -> None:
        """Test getting valid metadata attribute."""
        builder = PluggingerAppBuilder("test_app")

        name = builder._get_plugin_metadata_attr(mock_plugin_class, "_plugginger_plugin_name")

        assert name == "mock_plugin"

    def test_get_plugin_metadata_attr_missing_decorator_error(self) -> None:
        """Test error when class lacks @plugin decorator."""
        builder = PluggingerAppBuilder("test_app")

        class UnDecoratedClass:
            pass

        with pytest.raises(PluginRegistrationError) as exc_info:
            builder._get_plugin_metadata_attr(UnDecoratedClass, "_plugginger_plugin_name")

        assert "not a valid Plugginger plugin" in str(exc_info.value)

    def test_get_plugin_metadata_attr_missing_attribute_error(self, mock_plugin_class: type[PluginBase]) -> None:
        """Test error when metadata attribute is missing."""
        builder = PluggingerAppBuilder("test_app")

        with pytest.raises(PluginRegistrationError) as exc_info:
            builder._get_plugin_metadata_attr(mock_plugin_class, "_nonexistent_attribute")

        assert "missing essential Plugginger metadata attribute" in str(exc_info.value)

    def test_get_plugin_metadata_attr_non_class_error(self) -> None:
        """Test error when argument is not a class."""
        builder = PluggingerAppBuilder("test_app")

        # The actual implementation tries to access __name__ before checking if it's a class
        with pytest.raises(AttributeError):
            builder._get_plugin_metadata_attr("not_a_class", "_plugginger_plugin_name")  # type: ignore[arg-type]


class TestPluggingerAppBuilderInstanceId:
    """Test instance ID generation."""

    def test_generate_plugin_instance_id_top_level(self) -> None:
        """Test instance ID generation for top-level app."""
        builder = PluggingerAppBuilder("main_app")

        instance_id = builder._generate_plugin_instance_id("my_plugin")

        assert instance_id == "main_app:my_plugin"

    def test_generate_plugin_instance_id_with_prefix(self) -> None:
        """Test instance ID generation with prefix."""
        parent_mock = Mock()
        parent_mock._plugginger_instance_id = "parent_app:parent_plugin"

        builder = PluggingerAppBuilder("child_app", parent_app_plugin_context=parent_mock)

        instance_id = builder._generate_plugin_instance_id("child_plugin")

        assert instance_id == "parent_app:parent_plugin:child_plugin"

    def test_generate_plugin_instance_id_no_prefix_attribute(self) -> None:
        """Test instance ID generation when parent has no _plugginger_instance_id."""
        parent_mock = Mock()
        # Mock getattr to return the app_name when _plugginger_instance_id is not found
        del parent_mock._plugginger_instance_id  # Ensure attribute doesn't exist

        builder = PluggingerAppBuilder("child_app", parent_app_plugin_context=parent_mock)

        instance_id = builder._generate_plugin_instance_id("child_plugin")

        # When _plugginger_instance_id is not available, it should use app_name
        assert instance_id == "child_app:child_plugin"


class TestPluggingerAppBuilderValidation:
    """Test validation methods."""

    def test_register_item_class_invalid_needs_error(self) -> None:
        """Test error when plugin has invalid needs attribute."""
        builder = PluggingerAppBuilder("test_app")

        class InvalidNeedsPlugin(PluginBase):
            needs = "invalid"  # type: ignore[assignment]  # Should be list[Depends]

        # Add plugin metadata
        setattr(InvalidNeedsPlugin, PLUGIN_METADATA_KEY, True)
        InvalidNeedsPlugin._plugginger_plugin_name = "invalid_plugin"

        with pytest.raises(PluginRegistrationError) as exc_info:
            builder._register_item_class("invalid", InvalidNeedsPlugin, False)

        assert "invalid 'needs' attribute" in str(exc_info.value)

    def test_register_item_class_needs_with_non_depends_error(self) -> None:
        """Test error when needs contains non-Depends objects."""
        builder = PluggingerAppBuilder("test_app")

        class InvalidNeedsPlugin(PluginBase):
            needs = [Depends("valid"), "invalid"]  # type: ignore[list-item]  # Mixed valid and invalid

        # Add plugin metadata
        setattr(InvalidNeedsPlugin, PLUGIN_METADATA_KEY, True)
        InvalidNeedsPlugin._plugginger_plugin_name = "invalid_plugin"

        with pytest.raises(PluginRegistrationError) as exc_info:
            builder._register_item_class("invalid", InvalidNeedsPlugin, False)

        assert "invalid 'needs' attribute" in str(exc_info.value)

    def test_register_item_class_valid_needs(self) -> None:
        """Test successful registration with valid needs."""
        builder = PluggingerAppBuilder("test_app")

        class ValidNeedsPlugin(PluginBase):
            needs = [Depends("dependency1"), Depends("dependency2")]

        # Add plugin metadata
        setattr(ValidNeedsPlugin, PLUGIN_METADATA_KEY, True)
        ValidNeedsPlugin._plugginger_plugin_name = "valid_plugin"

        # Should not raise
        builder._register_item_class("valid", ValidNeedsPlugin, False)

        assert "valid" in builder._registered_item_classes
        assert len(builder._plugin_dependency_declarations["valid"]) == 2


class TestPluggingerAppBuilderIntegration:
    """Test integration scenarios."""

    def test_fluent_interface_chaining(self, mock_plugin_class: type[PluginBase], mock_app_plugin_class: type[Any]) -> None:
        """Test that methods return self for fluent interface."""
        builder = PluggingerAppBuilder("test_app")

        result = (builder
                  .include(mock_plugin_class)
                  .include_app(mock_app_plugin_class, "app_plugin"))

        assert result is builder
        assert len(builder._registered_item_classes) == 2

    def test_multiple_plugin_registration(self) -> None:
        """Test registering multiple plugins."""
        builder = PluggingerAppBuilder("test_app")

        # Create multiple mock plugins
        plugins = []
        for i in range(3):
            class TestPlugin(PluginBase):
                needs: list[Depends] = []

            setattr(TestPlugin, PLUGIN_METADATA_KEY, True)
            TestPlugin._plugginger_plugin_name = f"plugin_{i}"
            TestPlugin._plugginger_plugin_version = "1.0.0"
            TestPlugin.__name__ = f"TestPlugin{i}"

            plugins.append(TestPlugin)

        # Register all plugins
        for plugin in plugins:
            builder.include(plugin)

        assert len(builder._registered_item_classes) == 3
        for i in range(3):
            assert f"plugin_{i}" in builder._registered_item_classes

    def test_mixed_plugin_and_app_plugin_registration(self, mock_plugin_class: type[PluginBase], mock_app_plugin_class: type[Any]) -> None:
        """Test registering both regular plugins and app plugins."""
        builder = PluggingerAppBuilder("test_app")

        builder.include(mock_plugin_class)
        builder.include_app(mock_app_plugin_class, "my_app")

        assert len(builder._registered_item_classes) == 2
        assert "mock_plugin" in builder._registered_item_classes
        assert "my_app" in builder._registered_item_classes


class TestPluggingerAppBuilderBuild:
    """Test the build method and its phases."""

    @patch('plugginger.api.builder.RuntimeFacade')
    @patch('plugginger.api.app.PluggingerAppInstance')
    def test_build_empty_app(self, mock_app_class: Mock, mock_runtime_facade_class: Mock) -> None:
        """Test building an app with no plugins."""
        # Setup mocks
        mock_runtime_facade = Mock()
        mock_runtime_facade_class.return_value = mock_runtime_facade

        mock_app_instance = Mock()
        mock_app_class.return_value = mock_app_instance

        # Mock the builder phases
        builder = PluggingerAppBuilder("empty_app")

        # Mock phase helpers
        builder._config_resolver = Mock()
        builder._config_resolver.resolve_and_validate.return_value = GlobalAppConfig(app_name="empty_app")

        builder._dependency_orchestrator = Mock()
        builder._dependency_orchestrator.build_graph.return_value = {}
        builder._dependency_orchestrator.validate_graph_and_resolve_order.return_value = []

        builder._plugin_instantiator = Mock()
        builder._plugin_instantiator.instantiate_all.return_value = ({}, {})

        builder._interface_registrar = Mock()

        result = builder.build()

        assert result is mock_app_instance
        builder._config_resolver.resolve_and_validate.assert_called_once()
        builder._dependency_orchestrator.build_graph.assert_called_once()
        mock_runtime_facade.finalize_setup.assert_called_once()

    @patch('plugginger.api.builder.RuntimeFacade')
    def test_build_with_config_dict(self, mock_runtime_facade_class: Mock) -> None:
        """Test building with configuration dictionary."""
        mock_runtime_facade = Mock()
        mock_runtime_facade_class.return_value = mock_runtime_facade

        builder = PluggingerAppBuilder("test_app")

        # Mock phase helpers
        builder._config_resolver = Mock()
        builder._config_resolver.resolve_and_validate.return_value = GlobalAppConfig(app_name="test_app")

        builder._dependency_orchestrator = Mock()
        builder._dependency_orchestrator.build_graph.return_value = {}
        builder._dependency_orchestrator.validate_graph_and_resolve_order.return_value = []

        builder._plugin_instantiator = Mock()
        builder._plugin_instantiator.instantiate_all.return_value = ({}, {})

        builder._interface_registrar = Mock()

        config_dict = {"app_name": "test_app", "max_fractal_depth": 3}

        with patch('plugginger.api.app.PluggingerAppInstance') as mock_app_class:
            mock_app_instance = Mock()
            mock_app_class.return_value = mock_app_instance

            builder.build(config_dict)

        builder._config_resolver.resolve_and_validate.assert_called_once_with(
            "test_app", builder._max_fractal_depth, config_dict
        )

    @patch('plugginger.api.builder.RuntimeFacade')
    def test_build_with_global_config_object(self, mock_runtime_facade_class: Mock) -> None:
        """Test building with GlobalAppConfig object."""
        mock_runtime_facade = Mock()
        mock_runtime_facade_class.return_value = mock_runtime_facade

        builder = PluggingerAppBuilder("test_app")

        # Mock phase helpers
        builder._config_resolver = Mock()
        builder._config_resolver.resolve_and_validate.return_value = GlobalAppConfig(app_name="test_app")

        builder._dependency_orchestrator = Mock()
        builder._dependency_orchestrator.build_graph.return_value = {}
        builder._dependency_orchestrator.validate_graph_and_resolve_order.return_value = []

        builder._plugin_instantiator = Mock()
        builder._plugin_instantiator.instantiate_all.return_value = ({}, {})

        builder._interface_registrar = Mock()

        config = GlobalAppConfig(app_name="custom_app", max_fractal_depth=5)

        with patch('plugginger.api.app.PluggingerAppInstance') as mock_app_class:
            mock_app_instance = Mock()
            mock_app_class.return_value = mock_app_instance

            builder.build(config)

        builder._config_resolver.resolve_and_validate.assert_called_once_with(
            "test_app", builder._max_fractal_depth, config
        )

    def test_build_fractal_depth_validation_error(self) -> None:
        """Test build fails when current depth exceeds max depth."""
        builder = PluggingerAppBuilder("test_app", _current_depth=5)

        # Mock config resolver to return restrictive config
        builder._config_resolver = Mock()
        restrictive_config = GlobalAppConfig(app_name="test_app", max_fractal_depth=3)
        builder._config_resolver.resolve_and_validate.return_value = restrictive_config

        with pytest.raises(AppPluginError) as exc_info:
            builder.build()

        assert "already meets or exceeds max_fractal_depth" in str(exc_info.value)

    @patch('plugginger.api.builder.RuntimeFacade')
    def test_build_updates_max_fractal_depth_from_config(self, mock_runtime_facade_class: Mock) -> None:
        """Test that build updates max fractal depth from resolved config."""
        mock_runtime_facade = Mock()
        mock_runtime_facade_class.return_value = mock_runtime_facade

        builder = PluggingerAppBuilder("test_app")
        initial_max_depth = builder._max_fractal_depth

        # Mock config resolver to return more restrictive config
        builder._config_resolver = Mock()
        restrictive_config = GlobalAppConfig(app_name="test_app", max_fractal_depth=2)
        builder._config_resolver.resolve_and_validate.return_value = restrictive_config

        builder._dependency_orchestrator = Mock()
        builder._dependency_orchestrator.build_graph.return_value = {}
        builder._dependency_orchestrator.validate_graph_and_resolve_order.return_value = []

        builder._plugin_instantiator = Mock()
        builder._plugin_instantiator.instantiate_all.return_value = ({}, {})

        builder._interface_registrar = Mock()

        with patch('plugginger.api.app.PluggingerAppInstance') as mock_app_class:
            mock_app_instance = Mock()
            mock_app_class.return_value = mock_app_instance

            builder.build()

        # Max depth should be updated to the more restrictive value
        assert builder._max_fractal_depth == 2
        assert builder._max_fractal_depth < initial_max_depth

    @patch('plugginger.api.builder.RuntimeFacade')
    def test_build_sets_app_instance_fractal_attributes(self, mock_runtime_facade_class: Mock) -> None:
        """Test that build sets fractal depth attributes on app instance."""
        mock_runtime_facade = Mock()
        mock_runtime_facade_class.return_value = mock_runtime_facade

        builder = PluggingerAppBuilder("test_app", _current_depth=2)
        builder._max_fractal_depth = 5

        # Mock phase helpers
        builder._config_resolver = Mock()
        builder._config_resolver.resolve_and_validate.return_value = GlobalAppConfig(app_name="test_app")

        builder._dependency_orchestrator = Mock()
        builder._dependency_orchestrator.build_graph.return_value = {}
        builder._dependency_orchestrator.validate_graph_and_resolve_order.return_value = []

        builder._plugin_instantiator = Mock()
        builder._plugin_instantiator.instantiate_all.return_value = ({}, {})

        builder._interface_registrar = Mock()

        with patch('plugginger.api.app.PluggingerAppInstance') as mock_app_class:
            mock_app_instance = Mock()
            mock_app_class.return_value = mock_app_instance

            builder.build()

        # Check that fractal depth attributes are set
        assert mock_app_instance._current_build_depth_for_sub_apps == 2
        assert mock_app_instance._max_build_depth_for_sub_apps == 5

    @patch('plugginger.api.builder.RuntimeFacade')
    def test_build_with_plugins_calls_all_phases(self, mock_runtime_facade_class: Mock, mock_plugin_class: type[PluginBase]) -> None:
        """Test that build calls all phases when plugins are registered."""
        mock_runtime_facade = Mock()
        mock_runtime_facade_class.return_value = mock_runtime_facade

        builder = PluggingerAppBuilder("test_app")
        builder.include(mock_plugin_class)

        # Mock phase helpers
        builder._config_resolver = Mock()
        builder._config_resolver.resolve_and_validate.return_value = GlobalAppConfig(app_name="test_app")

        builder._dependency_orchestrator = Mock()
        builder._dependency_orchestrator.build_graph.return_value = {"mock_plugin": []}
        builder._dependency_orchestrator.validate_graph_and_resolve_order.return_value = ["mock_plugin"]

        mock_plugin_instance = Mock()
        builder._plugin_instantiator = Mock()
        builder._plugin_instantiator.instantiate_all.return_value = (
            {"mock_plugin": mock_plugin_instance},
            {"mock_plugin": {}}
        )

        builder._interface_registrar = Mock()

        with patch('plugginger.api.app.PluggingerAppInstance') as mock_app_class:
            mock_app_instance = Mock()
            mock_app_class.return_value = mock_app_instance

            builder.build()

        # Verify all phases were called
        builder._config_resolver.resolve_and_validate.assert_called_once()
        builder._dependency_orchestrator.build_graph.assert_called_once()
        builder._dependency_orchestrator.validate_graph_and_resolve_order.assert_called_once()
        builder._plugin_instantiator.instantiate_all.assert_called_once()
        builder._interface_registrar.register_all.assert_called_once()
        mock_runtime_facade.finalize_setup.assert_called_once()

        # Check that finalize_setup was called with correct arguments
        finalize_call_args = mock_runtime_facade.finalize_setup.call_args
        assert finalize_call_args[1]["plugins_in_order"] == [mock_plugin_instance]
        assert finalize_call_args[1]["plugin_configs_for_setup"] == {"mock_plugin": {}}


class TestPluggingerAppBuilderEdgeCases:
    """Test edge cases and error conditions."""

    def test_include_app_plugin_as_regular_plugin_error(self, mock_app_plugin_class: type[Any]) -> None:
        """Test error when trying to include AppPlugin as regular plugin."""
        builder = PluggingerAppBuilder("test_app")

        with pytest.raises(PluginRegistrationError) as exc_info:
            builder.include(mock_app_plugin_class)

        assert "Please use `builder.include_app(" in str(exc_info.value)

    def test_include_regular_plugin_as_app_plugin_error(self, mock_plugin_class: type[PluginBase]) -> None:
        """Test error when trying to include regular plugin as AppPlugin."""
        builder = PluggingerAppBuilder("test_app")

        # This should work fine - the error would be caught during type checking
        # or in the _register_item_class method if we had stricter validation
        # For now, let's test that it doesn't crash
        try:
            builder.include_app(mock_plugin_class, "test_name")  # type: ignore[arg-type]
        except (PluginRegistrationError, TypeError):
            # Either error is acceptable for this edge case
            pass

    def test_builder_with_very_deep_nesting(self, mock_app_plugin_class: type[Any]) -> None:
        """Test builder behavior with deep nesting levels."""
        builder = PluggingerAppBuilder("deep_app", _current_depth=8, _max_depth_from_config=10)

        assert builder._current_fractal_depth == 8
        assert builder._max_fractal_depth == 10

        # This should work (depth 8 + 1 = 9, which is < 10)
        builder.include_app(mock_app_plugin_class, "deep_plugin")

    def test_builder_logger_configuration(self) -> None:
        """Test that builder logger is properly configured."""
        builder = PluggingerAppBuilder("test_app")

        assert builder._logger is not None
        assert builder._logger.name == "plugginger.builder"

    def test_instance_id_generation_edge_cases(self) -> None:
        """Test instance ID generation with edge cases."""
        # Test with special characters in app name
        builder = PluggingerAppBuilder("app-with-dashes")
        instance_id = builder._generate_plugin_instance_id("plugin_name")
        assert instance_id == "app-with-dashes:plugin_name"

        # Test with empty plugin name (should still work)
        instance_id = builder._generate_plugin_instance_id("")
        assert instance_id == "app-with-dashes:"
