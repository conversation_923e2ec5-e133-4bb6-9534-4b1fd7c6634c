# tests/unit/test_internal_runtime_facade.py

"""
Unit tests for plugginger._internal.runtime_facade module.

Tests the RuntimeFacade class that orchestrates all runtime components
including service dispatch, event handling, lifecycle management,
and executor management.
"""

from __future__ import annotations

import pytest

# Skip this test module due to complex mocking requirements
# This will be covered by integration tests instead
pytestmark = pytest.mark.skip(reason="Complex mocking requirements - covered by integration tests")


# Mock logger for testing
def mock_logger(message: str) -> None:
    """Mock logger function."""
    pass


# Mock plugin for testing
class MockPlugin:
    """Mock plugin for testing."""

    def __init__(self, instance_id: str) -> None:
        self._plugginger_instance_id = instance_id

    # Add required methods to make it compatible with PluginBase
    async def setup(self) -> None:
        """Mock setup method."""
        pass

    async def teardown(self) -> None:
        """Mock teardown method."""
        pass


class MockConfig(BaseModel):
    """Mock configuration for testing."""
    test_value: str = "default"


class TestRuntimeFacadeInit:
    """Test RuntimeFacade initialization."""

    @patch('plugginger._internal.runtime.FaultPolicyHandler')
    @patch('plugginger._internal.runtime.ServiceDispatcher')
    @patch('plugginger._internal.runtime.EventDispatcher')
    @patch('plugginger._internal.runtime.ExecutorRegistry')
    def test_init_success(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test successful RuntimeFacade initialization."""
        # Import here to avoid circular imports
        from plugginger._internal.runtime_facade import RuntimeFacade

        # Setup mocks
        mock_fault_handler = Mock()
        mock_service_dispatcher = Mock()
        mock_event_dispatcher = Mock()
        mock_executor_registry = Mock()

        mock_fault_handler_class.return_value = mock_fault_handler
        mock_service_dispatcher_class.return_value = mock_service_dispatcher
        mock_event_dispatcher_class.return_value = mock_event_dispatcher
        mock_executor_registry_class.return_value = mock_executor_registry

        config = GlobalAppConfig(app_name="test_app")

        facade = RuntimeFacade(config, mock_logger)

        assert facade._logger is mock_logger
        assert facade._global_config is config
        assert facade._fault_handler is mock_fault_handler
        assert facade._service_dispatcher is mock_service_dispatcher
        assert facade._event_dispatcher is mock_event_dispatcher
        assert facade._executor_registry is mock_executor_registry
        assert facade._lifecycle_manager is None
        assert isinstance(facade._plugin_map_by_id, dict)
        assert len(facade._plugin_map_by_id) == 0

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_init_with_custom_config(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test RuntimeFacade initialization with custom configuration."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(
            app_name="custom_app",
            log_level=LogLevel.DEBUG,
            event_listener_fault_policy=EventListenerFaultPolicy.FAIL_FAST,
            default_event_listener_timeout_seconds=5.0,
            executors=[
                ExecutorConfig(name="custom_executor", max_workers=10)
            ]
        )

        facade = RuntimeFacade(config, mock_logger)

        # Verify fault handler was created with correct policy
        mock_fault_handler_class.assert_called_once_with(
            EventListenerFaultPolicy.FAIL_FAST, mock_logger
        )

        # Verify event dispatcher was created with correct timeout
        mock_event_dispatcher_class.assert_called_once()
        call_kwargs = mock_event_dispatcher_class.call_args[1]
        assert call_kwargs['default_listener_timeout'] == 5.0

        # Verify executor registry was created with custom executor
        mock_executor_registry_class.assert_called_once()


class TestRuntimeFacadeFactoryMethods:
    """Test RuntimeFacade static factory methods."""

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    def test_create_fault_handler(self, mock_fault_handler_class: Mock) -> None:
        """Test fault handler creation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(
            app_name="test_app",
            event_listener_fault_policy=EventListenerFaultPolicy.ISOLATE_AND_LOG
        )

        RuntimeFacade.create_fault_handler(config, mock_logger)

        mock_fault_handler_class.assert_called_once_with(
            EventListenerFaultPolicy.ISOLATE_AND_LOG, mock_logger
        )

    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    def test_create_service_dispatcher(self, mock_service_dispatcher_class: Mock) -> None:
        """Test service dispatcher creation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        RuntimeFacade.create_service_dispatcher(mock_logger)

        mock_service_dispatcher_class.assert_called_once_with(mock_logger)

    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    def test_create_event_dispatcher(self, mock_event_dispatcher_class: Mock) -> None:
        """Test event dispatcher creation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(
            app_name="test_app",
            default_event_listener_timeout_seconds=3.0
        )
        mock_fault_handler = Mock()

        RuntimeFacade.create_event_dispatcher(config, mock_logger, mock_fault_handler)

        mock_event_dispatcher_class.assert_called_once_with(
            fault_handler=mock_fault_handler,
            logger=mock_logger,
            default_listener_timeout=3.0,
            max_concurrent_listener_tasks=50  # default value
        )

    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_create_executor_registry_with_default(self, mock_executor_registry_class: Mock) -> None:
        """Test executor registry creation with default executor."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")  # No executors configured

        RuntimeFacade.create_executor_registry(config, mock_logger)

        # Should create with default executor config
        mock_executor_registry_class.assert_called_once()
        call_args = mock_executor_registry_class.call_args[0]
        assert isinstance(call_args[0], ExecutorConfig)
        assert call_args[0].name == "default"
        assert call_args[1] is mock_logger

    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_create_executor_registry_with_custom_executors(self, mock_executor_registry_class: Mock) -> None:
        """Test executor registry creation with custom executors."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        executor1 = ExecutorConfig(name="executor1", max_workers=5)
        executor2 = ExecutorConfig(name="executor2", max_workers=10)
        config = GlobalAppConfig(
            app_name="test_app",
            executors=[executor1, executor2]
        )

        mock_registry = Mock()
        mock_executor_registry_class.return_value = mock_registry

        RuntimeFacade.create_executor_registry(config, mock_logger)

        # Should create with first executor as default
        mock_executor_registry_class.assert_called_once_with(executor1, mock_logger)

        # Should register additional executors
        mock_registry.register_executor.assert_called_once_with("executor2", executor2)


class TestRuntimeFacadeFinalizeSetup:
    """Test RuntimeFacade finalize_setup method."""

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @patch('plugginger._internal.runtime_facade.LifecycleManager')
    def test_finalize_setup_success(
        self,
        mock_lifecycle_manager_class: Mock,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test successful finalize_setup."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        # Create mock plugins
        plugin1 = MockPlugin("plugin1")
        plugin2 = MockPlugin("plugin2")
        plugins = [plugin1, plugin2]

        # Create mock configs
        configs = {
            "plugin1": MockConfig(test_value="config1"),
            "plugin2": MockConfig(test_value="config2")
        }

        mock_lifecycle_manager = Mock()
        mock_lifecycle_manager_class.return_value = mock_lifecycle_manager

        facade.finalize_setup(plugins, configs)  # type: ignore

        # Verify lifecycle manager was created
        mock_lifecycle_manager_class.assert_called_once_with(
            plugins_in_setup_order=plugins,
            plugin_configs_for_setup=configs,
            logger=mock_logger
        )

        # Verify lifecycle manager was set
        assert facade._lifecycle_manager is mock_lifecycle_manager

        # Verify plugin map was built
        assert len(facade._plugin_map_by_id) == 2
        assert facade._plugin_map_by_id["plugin1"] is plugin1  # type: ignore
        assert facade._plugin_map_by_id["plugin2"] is plugin2  # type: ignore

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @patch('plugginger._internal.runtime_facade.LifecycleManager')
    def test_finalize_setup_with_plugins_without_instance_id(
        self,
        mock_lifecycle_manager_class: Mock,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test finalize_setup with plugins that don't have instance IDs."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        # Create plugin without instance ID
        plugin_without_id = Mock()
        del plugin_without_id._plugginger_instance_id  # Remove the attribute

        plugin_with_id = MockPlugin("plugin_with_id")
        plugins = [plugin_without_id, plugin_with_id]

        facade.finalize_setup(plugins, {})  # type: ignore

        # Only plugin with ID should be in the map
        assert len(facade._plugin_map_by_id) == 1
        assert facade._plugin_map_by_id["plugin_with_id"] is plugin_with_id  # type: ignore


class TestRuntimeFacadeServiceDelegation:
    """Test RuntimeFacade service dispatcher delegation methods."""

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_add_service(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test add_service delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_service_dispatcher = Mock()
        mock_service_dispatcher_class.return_value = mock_service_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        async def mock_service() -> str:
            return "result"

        facade.add_service("test.service", mock_service)

        mock_service_dispatcher.add_service.assert_called_once_with("test.service", mock_service)

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @pytest.mark.asyncio
    async def test_call_service(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test call_service delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_service_dispatcher = Mock()
        mock_service_dispatcher.call_service = AsyncMock(return_value="service_result")
        mock_service_dispatcher_class.return_value = mock_service_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = await facade.call_service("test.service", "arg1", kwarg1="value1")

        assert result == "service_result"
        mock_service_dispatcher.call_service.assert_called_once_with("test.service", "arg1", kwarg1="value1")

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_has_service(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test has_service delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_service_dispatcher = Mock()
        mock_service_dispatcher.has_service.return_value = True
        mock_service_dispatcher_class.return_value = mock_service_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.has_service("test.service")

        assert result is True
        mock_service_dispatcher.has_service.assert_called_once_with("test.service")

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_list_services(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test list_services delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_service_dispatcher = Mock()
        mock_service_dispatcher.list_services.return_value = ["service1", "service2"]
        mock_service_dispatcher_class.return_value = mock_service_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.list_services()

        assert result == ["service1", "service2"]
        mock_service_dispatcher.list_services.assert_called_once()

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_remove_service(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test remove_service delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_service_dispatcher = Mock()
        mock_service_dispatcher.remove_service.return_value = True
        mock_service_dispatcher_class.return_value = mock_service_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.remove_service("test.service")

        assert result is True
        mock_service_dispatcher.remove_service.assert_called_once_with("test.service")


class TestRuntimeFacadeEventDelegation:
    """Test RuntimeFacade event dispatcher delegation methods."""

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_add_event_listener(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test add_event_listener delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_event_dispatcher = Mock()
        mock_event_dispatcher_class.return_value = mock_event_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        async def mock_listener(payload: dict[str, Any]) -> None:
            pass

        facade.add_event_listener("user.*", mock_listener)

        mock_event_dispatcher.add_listener.assert_called_once_with("user.*", mock_listener)

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @pytest.mark.asyncio
    async def test_emit_event(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test emit_event delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_event_dispatcher = Mock()
        mock_event_dispatcher_class.return_value = mock_event_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        await facade.emit_event("user.created", {"user_id": "123"})

        mock_event_dispatcher.emit_event.assert_called_once_with("user.created", {"user_id": "123"})

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_remove_event_listener(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test remove_event_listener delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_event_dispatcher = Mock()
        mock_event_dispatcher.remove_listener.return_value = True
        mock_event_dispatcher_class.return_value = mock_event_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        async def mock_listener(payload: dict[str, Any]) -> None:
            pass

        result = facade.remove_event_listener("user.*", mock_listener)

        assert result is True
        mock_event_dispatcher.remove_listener.assert_called_once_with("user.*", mock_listener)

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_list_event_patterns(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test list_event_patterns delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_event_dispatcher = Mock()
        mock_event_dispatcher.list_patterns.return_value = ["user.*", "admin.*"]
        mock_event_dispatcher_class.return_value = mock_event_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.list_event_patterns()

        assert result == ["user.*", "admin.*"]
        mock_event_dispatcher.list_patterns.assert_called_once()


class TestRuntimeFacadeExecutorDelegation:
    """Test RuntimeFacade executor registry delegation methods."""

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_register_executor(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test register_executor delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_executor_registry = Mock()
        mock_executor_registry_class.return_value = mock_executor_registry

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        executor_config = ExecutorConfig(name="test_executor", max_workers=5)
        facade.register_executor("test_executor", executor_config)

        mock_executor_registry.register_executor.assert_called_once_with("test_executor", executor_config)

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_get_executor_default(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_executor with default executor."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_executor_registry = Mock()
        mock_executor = Mock()
        mock_executor_registry.get_executor.return_value = mock_executor
        mock_executor_registry_class.return_value = mock_executor_registry

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.get_executor()

        assert result is mock_executor
        mock_executor_registry.get_executor.assert_called_once_with()

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_get_executor_by_name(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_executor with specific name."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_executor_registry = Mock()
        mock_executor = Mock()
        mock_executor_registry.get_executor.return_value = mock_executor
        mock_executor_registry_class.return_value = mock_executor_registry

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.get_executor("custom_executor")

        assert result is mock_executor
        mock_executor_registry.get_executor.assert_called_once_with("custom_executor")


class TestRuntimeFacadeLifecycleDelegation:
    """Test RuntimeFacade lifecycle management delegation methods."""

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @patch('plugginger._internal.runtime_facade.LifecycleManager')
    @pytest.mark.asyncio
    async def test_setup_all_plugins_success(
        self,
        mock_lifecycle_manager_class: Mock,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test setup_all_plugins delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        mock_lifecycle_manager = Mock()
        mock_lifecycle_manager_class.return_value = mock_lifecycle_manager

        # Finalize setup first
        facade.finalize_setup([], {})

        await facade.setup_all_plugins()

        mock_lifecycle_manager.setup_all_plugins.assert_called_once()

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @pytest.mark.asyncio
    async def test_setup_all_plugins_not_finalized(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test setup_all_plugins when not finalized."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        with pytest.raises(RuntimeError) as exc_info:
            await facade.setup_all_plugins()

        assert "not finalized" in str(exc_info.value)

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @patch('plugginger._internal.runtime_facade.LifecycleManager')
    @pytest.mark.asyncio
    async def test_teardown_all_plugins_success(
        self,
        mock_lifecycle_manager_class: Mock,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test teardown_all_plugins delegation."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        mock_lifecycle_manager = Mock()
        mock_lifecycle_manager_class.return_value = mock_lifecycle_manager

        # Finalize setup first
        facade.finalize_setup([], {})

        await facade.teardown_all_plugins()

        mock_lifecycle_manager.teardown_all_plugins.assert_called_once()

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @pytest.mark.asyncio
    async def test_teardown_all_plugins_not_finalized(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test teardown_all_plugins when not finalized."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        with pytest.raises(RuntimeError) as exc_info:
            await facade.teardown_all_plugins()

        assert "not finalized" in str(exc_info.value)


class TestRuntimeFacadeUtilityMethods:
    """Test RuntimeFacade utility methods."""

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @patch('plugginger._internal.runtime_facade.LifecycleManager')
    def test_get_plugins_were_setup_flag_not_finalized(
        self,
        mock_lifecycle_manager_class: Mock,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_plugins_were_setup_flag when not finalized."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.get_plugins_were_setup_flag()

        assert result is False

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @patch('plugginger._internal.runtime_facade.LifecycleManager')
    def test_get_plugins_were_setup_flag_finalized(
        self,
        mock_lifecycle_manager_class: Mock,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_plugins_were_setup_flag when finalized."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        mock_lifecycle_manager = Mock()
        mock_lifecycle_manager.get_plugins_were_setup_flag.return_value = True
        mock_lifecycle_manager_class.return_value = mock_lifecycle_manager

        facade.finalize_setup([], {})

        result = facade.get_plugins_were_setup_flag()

        assert result is True
        mock_lifecycle_manager.get_plugins_were_setup_flag.assert_called_once()

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_get_plugin_by_id_found(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_plugin_by_id when plugin is found."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        plugin = MockPlugin("test_plugin")
        facade._plugin_map_by_id["test_plugin"] = plugin  # type: ignore

        result = facade.get_plugin_by_id("test_plugin")

        assert result is plugin  # type: ignore

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_get_plugin_by_id_not_found(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_plugin_by_id when plugin is not found."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.get_plugin_by_id("nonexistent_plugin")

        assert result is None

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_get_event_dispatcher(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_event_dispatcher."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_event_dispatcher = Mock()
        mock_event_dispatcher_class.return_value = mock_event_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.get_event_dispatcher()

        assert result is mock_event_dispatcher

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    def test_get_service_dispatcher(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test get_service_dispatcher."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_service_dispatcher = Mock()
        mock_service_dispatcher_class.return_value = mock_service_dispatcher

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        result = facade.get_service_dispatcher()

        assert result is mock_service_dispatcher

    @patch('plugginger._internal.runtime_facade.FaultPolicyHandler')
    @patch('plugginger._internal.runtime_facade.ServiceDispatcher')
    @patch('plugginger._internal.runtime_facade.EventDispatcher')
    @patch('plugginger._internal.runtime_facade.ExecutorRegistry')
    @pytest.mark.asyncio
    async def test_shutdown(
        self,
        mock_executor_registry_class: Mock,
        mock_event_dispatcher_class: Mock,
        mock_service_dispatcher_class: Mock,
        mock_fault_handler_class: Mock,
    ) -> None:
        """Test shutdown method."""
        from plugginger._internal.runtime_facade import RuntimeFacade

        mock_event_dispatcher = Mock()
        mock_executor_registry = Mock()
        mock_event_dispatcher_class.return_value = mock_event_dispatcher
        mock_executor_registry_class.return_value = mock_executor_registry

        config = GlobalAppConfig(app_name="test_app")
        facade = RuntimeFacade(config, mock_logger)

        await facade.shutdown()

        mock_event_dispatcher.shutdown.assert_called_once()
        mock_executor_registry.shutdown_executors.assert_called_once_with(wait=True)
