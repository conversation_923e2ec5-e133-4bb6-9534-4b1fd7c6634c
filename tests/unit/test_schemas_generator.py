"""
Tests for manifest generation utilities.

These tests verify that plugin and application manifests can be
generated correctly from plugin classes and application instances.
"""


import pytest
from pydantic import BaseModel

from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.schemas.generator import (
    generate_app_manifest,
    generate_plugin_manifest,
    manifest_from_yaml,
    manifest_to_yaml,
)
from plugginger.schemas.manifest import (
    ExecutionMode,
    PluginManifest,
)


# Test configuration schema
class TestConfig(BaseModel):
    """Test configuration schema."""
    api_key: str
    timeout: int = 30
    debug: bool = False


# Test plugin classes
@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin for manifest generation."""
    needs: list[Depends] = [Depends("logger"), Depends("database", optional=True)]

    @service(name="get_data", timeout_seconds=5.0, description="Get data by ID")
    async def get_data_by_id(self, data_id: int, include_meta: bool = False) -> dict:
        """Get data by ID with optional metadata."""
        return {"id": data_id, "meta": include_meta}

    @service()
    async def simple_service(self) -> str:
        """Simple service without parameters."""
        return "simple"

    @on_event("data.created", priority=10, description="Handle data creation")
    async def on_data_created(self, event_data: dict) -> None:
        """Handle data creation event."""
        pass

    @on_event(["data.*", "admin.data.*"])
    async def on_any_data_event(self, event_data: dict, event_type: str) -> None:
        """Handle any data-related event."""
        pass


@plugin(name="config_plugin", version="2.1.0", config_schema=TestConfig)
class ConfigPlugin(PluginBase):
    """Test plugin with configuration schema."""
    needs: list[Depends] = [Depends("logger")]

    @service()
    async def configured_service(self, value: str) -> bool:
        """Service that uses configuration."""
        return True


@plugin(name="minimal_plugin", version="0.1.0")
class MinimalPlugin(PluginBase):
    """Minimal plugin with no services or events."""
    pass


class TestGeneratePluginManifest:
    """Test plugin manifest generation."""

    def test_generate_basic_manifest(self) -> None:
        """Test generating manifest from basic plugin."""
        manifest = generate_plugin_manifest(TestPlugin)

        # Check metadata
        assert manifest.metadata.name == "test_plugin"
        assert manifest.metadata.version == "1.0.0"
        assert manifest.metadata.description == "Test plugin for manifest generation."

        # Check runtime
        assert manifest.runtime.execution_mode == ExecutionMode.THREAD
        assert manifest.runtime.plugginger_version == ">=0.9.0"

        # Check dependencies
        assert len(manifest.dependencies) == 2
        dep_names = [dep.name for dep in manifest.dependencies]
        assert "logger" in dep_names
        assert "database" in dep_names

        # Find database dependency and check it's optional
        db_dep = next(dep for dep in manifest.dependencies if dep.name == "database")
        assert db_dep.optional is True

        # Check services
        assert len(manifest.services) == 2
        service_names = [svc.name for svc in manifest.services]
        assert "get_data" in service_names
        assert "simple_service" in service_names

        # Check get_data service details
        get_data_svc = next(svc for svc in manifest.services if svc.name == "get_data")
        assert get_data_svc.method_name == "get_data_by_id"
        assert get_data_svc.description == "Get data by ID"
        assert get_data_svc.timeout_seconds == 5.0
        assert len(get_data_svc.parameters) == 2

        # Check parameters
        param_names = [p.name for p in get_data_svc.parameters]
        assert "data_id" in param_names
        assert "include_meta" in param_names

        # Check event listeners
        assert len(manifest.event_listeners) == 2

        # Check specific event listener
        data_created_listener = next(
            listener for listener in manifest.event_listeners
            if listener.method_name == "on_data_created"
        )
        assert data_created_listener.patterns == ["data.created"]
        assert data_created_listener.priority == 10
        assert data_created_listener.description == "Handle data creation"

    def test_generate_manifest_with_config_schema(self) -> None:
        """Test generating manifest from plugin with config schema."""
        manifest = generate_plugin_manifest(ConfigPlugin)

        assert manifest.metadata.name == "config_plugin"
        assert manifest.config_schema is not None

        # Check that config schema is JSON Schema
        schema = manifest.config_schema
        assert "properties" in schema
        assert "api_key" in schema["properties"]
        assert "timeout" in schema["properties"]
        assert "debug" in schema["properties"]

    def test_generate_manifest_with_custom_metadata(self) -> None:
        """Test generating manifest with custom metadata."""
        manifest = generate_plugin_manifest(
            TestPlugin,
            author="John Doe",
            license="MIT",
            keywords=["test", "data"],
            homepage="https://example.com",
            repository="https://github.com/user/plugin",
            description="Custom description",
            execution_mode=ExecutionMode.PROCESS,
            python_version=">=3.9",
            plugginger_version=">=1.0.0",
        )

        assert manifest.metadata.author == "John Doe"
        assert manifest.metadata.license == "MIT"
        assert manifest.metadata.keywords == ["test", "data"]
        assert manifest.metadata.homepage == "https://example.com"
        assert manifest.metadata.repository == "https://github.com/user/plugin"
        assert manifest.metadata.description == "Custom description"
        assert manifest.runtime.execution_mode == ExecutionMode.PROCESS
        assert manifest.runtime.python_version == ">=3.9"
        assert manifest.runtime.plugginger_version == ">=1.0.0"

    def test_generate_manifest_minimal_plugin(self) -> None:
        """Test generating manifest from minimal plugin."""
        manifest = generate_plugin_manifest(MinimalPlugin)

        assert manifest.metadata.name == "minimal_plugin"
        assert manifest.metadata.version == "0.1.0"
        assert len(manifest.dependencies) == 0
        assert len(manifest.services) == 0
        assert len(manifest.event_listeners) == 0

    def test_generate_manifest_invalid_plugin(self) -> None:
        """Test that generating manifest from invalid plugin raises error."""
        class NotAPlugin:
            pass

        with pytest.raises(ValueError, match="not a valid plugin class"):
            generate_plugin_manifest(NotAPlugin)


class TestGenerateAppManifest:
    """Test application manifest generation."""

    def test_generate_basic_app_manifest(self) -> None:
        """Test generating basic app manifest."""
        manifest = generate_app_manifest(
            app_name="test_app",
            app_version="1.0.0",
            plugin_classes=[TestPlugin, ConfigPlugin],
        )

        assert manifest.app_name == "test_app"
        assert manifest.app_version == "1.0.0"
        assert manifest.plugins == ["test_plugin", "config_plugin"]
        assert manifest.plugin_configs == {}
        assert manifest.global_config == {}

    def test_generate_app_manifest_with_configs(self) -> None:
        """Test generating app manifest with configurations."""
        plugin_configs = {
            "test_plugin": {"setting": "value"},
            "config_plugin": {"api_key": "secret"},
        }
        global_config = {"debug": True, "log_level": "INFO"}

        manifest = generate_app_manifest(
            app_name="configured_app",
            app_version="2.0.0",
            plugin_classes=[TestPlugin, ConfigPlugin],
            description="App with configurations",
            plugin_configs=plugin_configs,
            global_config=global_config,
        )

        assert manifest.description == "App with configurations"
        assert manifest.plugin_configs == plugin_configs
        assert manifest.global_config == global_config

    def test_generate_app_manifest_invalid_plugin(self) -> None:
        """Test that generating app manifest with invalid plugin raises error."""
        class NotAPlugin:
            pass

        with pytest.raises(ValueError, match="not a valid plugin class"):
            generate_app_manifest(
                app_name="invalid_app",
                app_version="1.0.0",
                plugin_classes=[TestPlugin, NotAPlugin],
            )


class TestManifestYamlSerialization:
    """Test YAML serialization and deserialization."""

    def test_manifest_to_yaml(self) -> None:
        """Test converting manifest to YAML."""
        manifest = generate_plugin_manifest(TestPlugin)
        yaml_content = manifest_to_yaml(manifest)

        assert isinstance(yaml_content, str)
        assert "manifest_version: 1.0.0" in yaml_content
        assert "name: test_plugin" in yaml_content
        assert "version: 1.0.0" in yaml_content
        assert "services:" in yaml_content
        assert "event_listeners:" in yaml_content

    def test_manifest_from_yaml(self) -> None:
        """Test loading manifest from YAML."""
        # Generate manifest and convert to YAML
        original_manifest = generate_plugin_manifest(TestPlugin)
        yaml_content = manifest_to_yaml(original_manifest)

        # Load back from YAML
        loaded_manifest = manifest_from_yaml(yaml_content, PluginManifest)

        # Compare key fields
        assert loaded_manifest.metadata.name == original_manifest.metadata.name
        assert loaded_manifest.metadata.version == original_manifest.metadata.version
        assert len(loaded_manifest.services) == len(original_manifest.services)
        assert len(loaded_manifest.event_listeners) == len(original_manifest.event_listeners)

    def test_app_manifest_to_yaml(self) -> None:
        """Test converting app manifest to YAML."""
        manifest = generate_app_manifest(
            app_name="test_app",
            app_version="1.0.0",
            plugin_classes=[TestPlugin],
        )
        yaml_content = manifest_to_yaml(manifest)

        assert isinstance(yaml_content, str)
        assert "app_name: test_app" in yaml_content
        assert "app_version: 1.0.0" in yaml_content
        assert "plugins:" in yaml_content

    def test_invalid_yaml_loading(self) -> None:
        """Test that invalid YAML raises appropriate error."""
        invalid_yaml = "invalid: yaml: content: ["

        with pytest.raises(ValueError, match="Invalid YAML"):
            manifest_from_yaml(invalid_yaml, PluginManifest)

    def test_invalid_manifest_data(self) -> None:
        """Test that invalid manifest data raises appropriate error."""
        invalid_data_yaml = """
        manifest_version: "1.0.0"
        metadata:
          name: "invalid-name-with-dashes"
          version: "1.0.0"
        runtime:
          plugginger_version: ">=0.9.0"
        """

        with pytest.raises(ValueError, match="Invalid manifest data"):
            manifest_from_yaml(invalid_data_yaml, PluginManifest)
