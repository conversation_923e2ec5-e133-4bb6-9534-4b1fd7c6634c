# Pluggineer

[![PyPI version](https://img.shields.io/pypi/v/plugginger.svg)](https://pypi.org/project/plugginger/)
[![Build Status](https://img.shields.io/github/actions/workflow/status/jkehrhahn/plugginger/ci.yml?branch=main)](https://github.com/jkehrhahn/plugginger/actions/workflows/ci.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python Versions](https://img.shields.io/pypi/pyversions/plugginger.svg)](https://pypi.org/project/plugginger/)

**Plugginger: Stop developing Python monoliths. Better compose them from pluggable and reusable mini-applications.**

Pluggineer is a Python framework designed to help you build applications in a modular and flexible way. Instead of creating large, monolithic codebases, Pluggineer encourages you to break down your application into smaller, independent, and reusable plugins. This approach leads to more maintainable, scalable, and understandable software.

## Core Features

*   **Plugin-Based Architecture:** Build your application as a collection of plugins. Each plugin encapsulates a specific piece of functionality.
*   **Service-Oriented:** Plugins can expose functionalities as services, allowing for clear contracts and communication between different parts of your application.
*   **Dependency Injection:** Plugins can declare their dependencies (other services or framework components), which are automatically injected by the framework.
*   **Asynchronous First:** Built with `asyncio` in mind, Pluggineer is well-suited for I/O-bound applications and modern Python development.
*   **Configuration Management:** Flexible configuration system for both the application core and individual plugins, supporting JSON and TOML files.
*   **Command-Line Interface:** Comes with a built-in CLI for running and managing Pluggineer applications.
*   **Event System:** Enables plugins to communicate through events, promoting loose coupling.

## Installation

You can install Pluggineer using pip:

```bash
pip install plugginger
```

For development, it's recommended to use [Poetry](https://python-poetry.org/):

```bash
git clone https://github.com/jkehrhahn/plugginger.git
cd plugginger
poetry install
```

## Basic Usage / Quick Start

Here's a simple example to illustrate how to create and run a Pluggineer application.

**1. Define a Plugin:**

Create a Python file, for example, `my_plugins.py`:

```python
from plugginger.api import PluginBase, plugin, service
from plugginger.core.types import ConfigType, AppType

@plugin(name="greeter", version="0.1.0")
class GreeterPlugin(PluginBase):
    async def setup(self, config: ConfigType, app: AppType) -> None:
        self.greeting = config.get("greeting", "Hello")
        print(f"GreeterPlugin: Setup complete. Greeting is '{self.greeting}'.")

    @service(name="greet")
    async def greet_service(self, name: str) -> str:
        return f"{self.greeting}, {name}!"

    async def teardown(self) -> None:
        print("GreeterPlugin: Teardown complete.")
```

**2. Create an App Factory:**

In the same directory, create `my_project.py` (or any other name):

```python
from plugginger.api import PluggableApp
from .my_plugins import GreeterPlugin # Assuming my_plugins.py is in the same directory

def create_app() -> PluggableApp:
    """
    App factory function that Pluggineer will call.
    """
    return PluggableApp(
        plugins=[
            GreeterPlugin(),
        ]
    )
```
*Note: Ensure `my_plugins.py` and `my_project.py` are in a directory that is part of your `PYTHONPATH`, or install them as a package.*

**3. Create a Configuration File:**

Create a `config.json` file:

```json
{
  "plugin_configs": {
    "greeter": {
      "greeting": "Welcome"
    }
  }
}
```

**4. Run the Application:**

From your terminal, in the directory containing `config.json` (and where `my_project` is accessible):

```bash
plugginger run --app-factory my_project:create_app --config config.json
```

You should see the setup message from the plugin and the application will wait for a shutdown signal (e.g., Ctrl+C).

To call the service (e.g., from another part of your application or another plugin if you had one):

```python
# Assuming 'app' is your PluggineerAppInstance
# response = await app.call_service("greet", name="Pluggineer User")
# print(response) # Output: Welcome, Pluggineer User!
```
*(Actual service calling from outside the `plugginger run` context would require more setup, like embedding Pluggineer or using a client if it were a networked service).*


## Running Tests

To run the test suite for Pluggineer itself (if you've cloned the repository):

```bash
poetry run pytest
```

For applications built *with* Pluggineer, you would typically use `pytest` as well, integrating with Pluggineer's testing utilities if needed.

## Contributing

Contributions are welcome! If you have a bug fix, improvement, or new feature, please:

1.  Fork the repository.
2.  Create your feature branch (`git checkout -b feature/AmazingFeature`).
3.  Commit your changes (`git commit -m 'Add some AmazingFeature'`).
4.  Push to the branch (`git push origin feature/AmazingFeature`).
5.  Open a Pull Request.

Please also make sure to add or update tests as appropriate and ensure existing tests pass. You can also open an issue to discuss potential changes.

## License

This project is licensed under the **MIT License**. See the [LICENSE](LICENSE) file for details.