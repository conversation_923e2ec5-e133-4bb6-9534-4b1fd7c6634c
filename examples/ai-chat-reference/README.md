# AI-Chat Reference App

**Plugginger Framework Production-Ready Reference Implementation**

A complete AI-powered chat application demonstrating the Plugginger framework's capabilities with plugin architecture, dependency injection, event system, and REST API integration.

## 🚀 Quick Start (< 10 Minutes)

### Prerequisites
- Python 3.11+
- Git

### 1. Clone and Setup (2 minutes)

```bash
# Clone the repository
git clone https://github.com/jkehrhahn/plugginger.git
cd plugginger/examples/ai-chat-reference

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e ../../  # Install Plugginger framework
pip install fastapi uvicorn pydantic
```

### 2. Configure Environment (1 minute)

```bash
# Copy environment template
cp .env.example .env

# Optional: Add OpenAI API key for real AI responses
# echo "OPENAI_API_KEY=your_key_here" >> .env
# (Without API key, app runs in mock mode)
```

### 3. Run the Application (1 minute)

```bash
# Start the AI-Chat app
python app.py
```

**Expected Output:**
```
🚀 AI-Chat Reference App started!
📡 Web API available at: http://0.0.0.0:8000
🏥 Health check: http://0.0.0.0:8000/health
💬 Chat endpoint: http://0.0.0.0:8000/chat
📋 API docs: http://0.0.0.0:8000/docs
✨ Application running. Press Ctrl+C to stop.
```

### 4. Test the Application (3 minutes)

#### Health Check
```bash
curl http://localhost:8000/health
```

#### Send a Chat Message
```bash
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?"}'
```

#### List Conversations
```bash
curl http://localhost:8000/conversations
```

#### Inspect Plugin Architecture
```bash
python -m plugginger.cli inspect app:create_app_for_inspection --json
```

### 5. Explore API Documentation (2 minutes)

Open your browser and visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **Health Status**: http://localhost:8000/health

## 🏗️ Architecture Overview

### Plugin Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   web_api       │    │    chat_ai      │    │  memory_store   │
│                 │    │                 │    │                 │
│ • FastAPI       │───▶│ • OpenAI API    │───▶│ • In-Memory     │
│ • REST Endpoints│    │ • Mock Mode     │    │ • Conversations │
│ • CORS Support  │    │ • Response Gen  │    │ • Messages      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Dependency Graph
- `memory_store` → Base storage layer (no dependencies)
- `chat_ai` → Depends on `memory_store` for conversation history
- `web_api` → Depends on both `chat_ai` and `memory_store`

### Event Flow
1. **HTTP Request** → `web_api` receives chat message
2. **Store User Message** → `memory_store` saves user input
3. **Generate AI Response** → `chat_ai` creates response
4. **Event Emission** → `chat.response_generated` event fired
5. **Auto-Store Response** → `memory_store` saves AI response via event
6. **HTTP Response** → `web_api` returns complete chat response

## 📊 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/health` | Health check - returns 200 OK when healthy |
| `POST` | `/chat` | Send message and get AI response |
| `GET` | `/conversations` | List all conversations with metadata |
| `GET` | `/conversations/{id}/history` | Get conversation history |
| `GET` | `/conversations/{id}` | Get conversation information |
| `DELETE` | `/conversations/{id}` | Delete a conversation |
| `GET` | `/models` | Get AI model information |

### Example Chat Request

```json
POST /chat
{
  "message": "What is the weather like?",
  "conversation_id": "optional-existing-id",
  "model": "gpt-3.5-turbo"
}
```

### Example Chat Response

```json
{
  "response": "I'm sorry, I don't have access to real weather data in mock mode, but I hope it's nice where you are!",
  "conversation_id": "550e8400-e29b-41d4-a716-446655440000",
  "model": "gpt-3.5-turbo",
  "response_time": 0.05
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `OPENAI_API_KEY` | None | OpenAI API key (optional, uses mock mode if not provided) |
| `SERVER_HOST` | `0.0.0.0` | Host for the web server |
| `SERVER_PORT` | `8000` | Port for the web server |
| `ENABLE_MANIFESTS` | `true` | Enable manifest loading and validation |

### Plugin Configuration

Each plugin can be configured via the app manifest or environment variables:

- **memory_store**: In-memory storage with configurable limits
- **chat_ai**: OpenAI integration with fallback to mock mode
- **web_api**: FastAPI server with CORS and health checks

## 🧪 Testing

### Run Integration Tests
```bash
python -m pytest tests/test_integration.py -v
```

### Test Coverage
```bash
python -m pytest tests/ --cov=plugins --cov=app --cov-report=html
```

### Manual Testing Scenarios

1. **Basic Chat Flow**: Send message → Get AI response → Check history
2. **Multiple Conversations**: Create multiple chats → Verify isolation
3. **Error Handling**: Invalid conversation ID → Proper error response
4. **Health Monitoring**: Health endpoint → System status
5. **Plugin Discovery**: Inspect command → Complete architecture

## 🔍 Plugin Discovery

The application supports the Plugginger CLI for introspection:

```bash
# Inspect complete application structure
python -m plugginger.cli inspect app:create_app_for_inspection --json

# View plugin services and dependencies
python -m plugginger.cli inspect app:create_app_for_inspection --format=table
```

## 🚀 Production Deployment

### Docker Support (Coming Soon)
```dockerfile
FROM python:3.11-slim
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
CMD ["python", "app.py"]
```

### Environment Setup
- Set `OPENAI_API_KEY` for production AI responses
- Configure `SERVER_HOST` and `SERVER_PORT` for your environment
- Enable logging and monitoring
- Set up reverse proxy (nginx/traefik) for production

## 🛠️ Development

### Adding New Plugins

1. Create plugin directory: `plugins/my_plugin/`
2. Implement `PluginBase` with `@plugin` decorator
3. Add services with `@service()` decorator
4. Define dependencies with `needs: List[Depends]`
5. Include in `app.py` builder

### Plugin Development Guidelines

- Follow dependency injection patterns
- Use type hints for all methods
- Implement proper error handling
- Add comprehensive docstrings
- Write unit and integration tests

## 📚 Learning Resources

- **Plugginger Documentation**: [Framework Docs](../../docs/)
- **Plugin Architecture**: [ARCHITECTURE.md](ARCHITECTURE.md)
- **API Reference**: http://localhost:8000/docs (when running)
- **Example Plugins**: Browse `plugins/` directory

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/my-feature`
3. Make changes and add tests
4. Ensure all tests pass: `pytest`
5. Submit pull request

## 📄 License

MIT License - see [LICENSE](../../LICENSE) for details.

---

**🎯 Success Criteria**: If you completed the Quick Start in under 10 minutes and can send chat messages via the API, you've successfully demonstrated Plugginger's production readiness!
