# Plugginger Examples

Dieser Ordner wird Beispiel-Plugins und Reference-Apps enthalten.

## 🚧 Aktueller Status

**Noch leer** - <PERSON><PERSON><PERSON><PERSON> werden in **Sprint 1** der [ROADMAP](../ROADMAP.md) entwickelt.

## 📋 Geplante Beispiele

### Sprint 1: Reference-App "AI-Chat mit Memory"
```
examples/ai-chat-reference/
├── plugins/
│   ├── chat_ai/          # OpenAI Integration
│   ├── memory_store/     # Simple Vector Store  
│   └── web_api/          # FastAPI Endpoints
├── app.py               # Main Application
├── requirements.txt     # Dependencies
└── README.md            # Setup in <10 Minuten
```

**Ziel**: Fremder Entwickler kann die App in ≤10 Minuten nachbauen

### Sprint 2: Plugin-Templates
```
examples/plugin-templates/
├── basic-service/       # Einfacher Service-Plugin
├── event-listener/      # Event-Processing Plugin
├── fractal-app/         # AppPlugin mit internen Services
└── ai-integration/      # KI-API Integration Plugin
```

**Ziel**: KI-Agenten haben Referenz-Patterns für häufige Use-Cases

## 🎯 Verwendung

Sobald die Beispiele verfügbar sind:

```bash
# Reference-App ausprobieren
cd examples/ai-chat-reference
pip install -r requirements.txt
python app.py

# Plugin-Template verwenden
cp -r examples/plugin-templates/basic-service my-new-plugin
cd my-new-plugin
# Anpassungen vornehmen...
```

## 📚 Weitere Ressourcen

- [ROADMAP.md](../ROADMAP.md) - Entwicklungsplan
- [docs/RFC_TEMPLATE.md](../docs/RFC_TEMPLATE.md) - Für größere Änderungen
- [Plugginger Documentation](https://plugginger.readthedocs.io) - Vollständige Docs (geplant)

---

**Status**: 🚧 In Entwicklung (Sprint 1)  
**Letzte Aktualisierung**: 1. Juni 2025
