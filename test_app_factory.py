"""
Test application factory for CLI testing.
"""

from plugginger.api.builder import Plugginger<PERSON>pp<PERSON>uilder
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.api.events import on_event
from plugginger.api.depends import Depends


@plugin(name="test_service_plugin", version="1.0.0")
class TestServicePlugin(PluginBase):
    """Test plugin with services."""

    @service()
    async def get_data(self, param: str = "default") -> str:
        """Get some test data."""
        return f"data:{param}"

    @service()
    async def process_data(self, input_data: str) -> dict[str, str]:
        """Process input data."""
        return {"result": f"processed:{input_data}"}


@plugin(name="test_event_plugin", version="1.0.0")
class TestEventPlugin(PluginBase):
    """Test plugin with event listeners."""

    @on_event("user.created")
    async def on_user_created(self, user_id: int, username: str) -> None:
        """Handle user creation events."""
        pass

    @service()
    async def emit_event(self, event_type: str) -> str:
        """Emit a test event."""
        return f"emitted:{event_type}"


@plugin(name="test_dependent_plugin", version="1.0.0")
class TestDependentPlugin(PluginBase):
    """Test plugin with dependencies."""

    needs = [Depends("test_service_plugin")]

    @service()
    async def dependent_service(self, input_data: str) -> dict[str, str]:
        """Service that depends on another plugin."""
        return {"result": input_data, "processed_by": "dependent_plugin"}


def create_test_app() -> PluggingerAppBuilder:
    """Create a test application for CLI testing."""
    builder = PluggingerAppBuilder("test_cli_app")
    builder.include(TestServicePlugin)
    builder.include(TestEventPlugin)
    builder.include(TestDependentPlugin)
    return builder
